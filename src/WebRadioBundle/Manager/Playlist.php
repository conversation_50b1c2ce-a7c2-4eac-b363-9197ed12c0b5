<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\WebRadioBundle\Manager;

use Psr\Cache\InvalidArgumentException;
use League\Flysystem\FileNotFoundException;
use League\Flysystem\MountManager;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\Asset\Packages;

/**
 * Class Playlist
 * @package SonVideo\Cms\FrontOffice\WebRadioBundle\Manager
 */
class Playlist
{
    public const NOT_FOUND_COVER_KEY = 'not_found';
    public const NO_COVER_URL = 'https://www.son-video.com/images/webradio/ImageNonDisponible.png';
    public const ITUNES_SEARCH_PATTERN = 'https://itunes.apple.com/WebObjects/MZStoreServices.woa/ws/wsSearch?term=%s&country=FR&media=music&entity=musicTrack&limit=1&genreId=&version=2&output=json';
    public const IMAGES_NEEDING_AN_UPDATE = [
        '',
        'https://www.son-video.com/images/webradio/ImageNonDisponible.png',
        'https://www.son-video.com/images/webradio/ImageNonDisponible.jpg',
    ];
    public const TITLE_PATTERN = '%s - %s';
    public const CACHE_KEY = 'webradio.playlist';
    public const CACHE_EXPIRE_AFTER = 'PT4S';

    protected string $playing_now_file;

    protected string $not_found_covers_logfile;

    protected MountManager $mount_manager;

    protected Packages $packages;

    protected CacheItemPoolInterface $cache;

    /**
     * Playlist manager constructor.
     */
    public function __construct(
        MountManager $mount_manager,
        Packages $packages,
        CacheItemPoolInterface $cache,
        string $playing_now_file,
        string $not_found_covers_logfile
    ) {
        $this->mount_manager = $mount_manager;
        $this->packages = $packages;
        $this->cache = $cache;
        $this->playing_now_file = $playing_now_file;
        $this->not_found_covers_logfile = $not_found_covers_logfile;
    }

    /**
     * getPlaylist
     *
     * Get the decorated content extracted from the playlist file as XML or null if not found.
     * Content may be stored/retrieved in/from cache for performance boost.
     *
     * @return \SimpleXMLElement|null
     * @throws \Exception
     * @throws InvalidArgumentException
     */
    public function getPlaylist()
    {
        // get cached value if valid
        $playlist_cache = $this->cache->getItem(static::CACHE_KEY);
        if ($playlist_cache->isHit()) {
            return new \SimpleXMLElement($playlist_cache->get());
        }

        $playlist = $this->getUpdatedPlayingNow();

        // cache result
        if ($playlist instanceof \SimpleXMLElement) {
            $playlist_cache->set($playlist->asXML());
            $playlist_cache->expiresAfter(new \DateInterval(static::CACHE_EXPIRE_AFTER));
            $this->cache->save($playlist_cache);
        }

        return $playlist;
    }

    /**
     * getUpdatedPlayingNow
     *
     * Read the playlist file and update images urls.
     * Overwrite the playlist file with updated data.
     * Decorate not found images.
     *
     * @return \SimpleXMLElement|null
     * @throws FileNotFoundException
     */
    protected function getUpdatedPlayingNow()
    {
        $webradio_filesystem = $this->mount_manager->getFilesystem('webradio_filesystem');

        // short exit if no file found
        if (!$webradio_filesystem->has($this->playing_now_file)) {
            return null;
        }

        $xml = new \SimpleXMLElement($webradio_filesystem->read($this->playing_now_file));

        // check if some images need an update
        $image_updated = false;
        foreach (['Current', 'Next', 'Last'] as $key) {
            $image = (string) $xml->{$key}->Image;
            if (in_array($image, static::IMAGES_NEEDING_AN_UPDATE)) {
                $title = sprintf(static::TITLE_PATTERN, $xml->{$key}->Artist, $xml->{$key}->Title);
                $xml->{$key}->Image = $this->retrieveCover($title);
                $image_updated = true;
            }
        }

        // write new data to file (ie. cache for future reads)
        if ($image_updated) {
            // check if playlist has not been updated since last access
            $test_xml = new \SimpleXMLElement($webradio_filesystem->read($this->playing_now_file));
            if ((string) $test_xml->Current->Title !== (string) $xml->Current->Title) {
                return $this->getUpdatedPlayingNow();
            }

            $webradio_filesystem->update($this->playing_now_file, $xml->asXML());
        }

        return $this->decorateNotFoundCovers($xml);
    }

    /**
     * retrieveCover
     *
     *
     */
    protected function retrieveCover(string $title): string
    {
        $media_filesystem = $this->mount_manager->getFilesystem('media_filesystem');

        // check if already in webradio medias
        if ($media_filesystem->has($this->getCoverFilePathFromTitle($title))) {
            return $this->getCoverUrlFromTitle($title);
        }

        // else get it from iTunes
        return $this->retrieveCoverFromITunes($title);
    }

    /**
     * Return path of the cover in media filesystem
     *
     *
     */
    protected function getCoverFilePathFromTitle(string $title): string
    {
        $md5_title = md5($title);

        return sprintf('/images/webradio/%s/%s.jpg', $md5_title[0], $md5_title);
    }

    /**
     * Return url of the cover in cdn
     *
     *
     */
    protected function getCoverUrlFromTitle(string $title): string
    {
        return $this->packages->getUrl($this->getCoverFilePathFromTitle($title), 'static_images');
    }

    /**
     * retrieveCoverFromITunes
     *
     * Get the cover of the song from iTunes and store it in webradio medias.
     *
     *
     * @return string Url of the new uploaded file or NOT_FOUND_COVER_KEY
     */
    protected function retrieveCoverFromITunes(string $title): string
    {
        // search in iTunes
        $url = sprintf(static::ITUNES_SEARCH_PATTERN, urlencode($title));
        $content = file_get_contents($url);
        $data = json_decode($content, null, 512, JSON_THROW_ON_ERROR);

        if ($data && array_key_exists(0, $data->results)) {
            $obj = $data->results[0];
            $url_image = str_replace('100x100', '170x170', $obj->artworkUrl100);

            // retrieve and store the file
            if ($url_image !== null && $url_image !== '') {
                $media_filesystem = $this->mount_manager->getFilesystem('media_filesystem');
                $media_filesystem->put($this->getCoverFilePathFromTitle($title), file_get_contents($url_image));

                return $this->getCoverUrlFromTitle($title);
            }
        }

        // log not found covers
        $this->logNotFoundCover($title);

        return static::NOT_FOUND_COVER_KEY;
    }

    /**
     * logNotFoundCover
     *
     * Append title in NOT_FOUND_COVERS_LOGFILE file if not already in it.
     * Create file if not exists.
     */
    protected function logNotFoundCover(string $title)
    {
        $webradio_filesystem = $this->mount_manager->getFilesystem('webradio_filesystem');
        try {
            $content = $webradio_filesystem->read($this->not_found_covers_logfile);
            $content = explode(PHP_EOL, $content);
        } catch (FileNotFoundException $e) {
            $content = [];
        }

        if (!in_array($title, $content)) {
            $content[] = $title;
            $webradio_filesystem->put($this->not_found_covers_logfile, implode(PHP_EOL, $content));
        }
    }

    /**
     * decorateNotFoundCovers
     *
     * Replace NOT_FOUND_COVER_KEY with NO_COVER_URL in the xml's images
     */
    protected function decorateNotFoundCovers(\SimpleXMLElement $xml): \SimpleXMLElement
    {
        foreach (['Current', 'Next', 'Last'] as $key) {
            $image = (string) $xml->{$key}->Image;
            if ($image === static::NOT_FOUND_COVER_KEY) {
                $xml->{$key}->Image = static::NO_COVER_URL;
            }
        }

        return $xml;
    }
}
