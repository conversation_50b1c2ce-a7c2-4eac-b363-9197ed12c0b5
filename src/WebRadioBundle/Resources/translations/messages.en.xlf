<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="fr" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="player_title">
                <source>player_title</source>
                <target>Son-Vidéo.com's WebRadio</target>
            </trans-unit>
            <trans-unit id="webradio_description">
                <source>webradio_description</source>
                <target>The webradio of Son-Vidéo.com offers you a very nice selection of multi-genre music to enjoy faithfully on your hi-fi system.</target>
            </trans-unit>
            <trans-unit id="player_description">
                <source>player_description</source>
                <target>Son-Vidé<PERSON>, an application to listen to Son-Vidéo's internet radio, know the song titles, buy them on Amazon or iTunes and listen them on Deezer or Spotify.</target>
            </trans-unit>
            <trans-unit id="player_short_description">
                <source>player_short_description</source>
                <target>Son-Vidéo.com's WebRadio: our continuous playlist.</target>
            </trans-unit>
            <trans-unit id="require_update_message">
                <source>require_update_message</source>
                <target>
                    <![CDATA[<strong>Update required:</strong>
                    To listen to Son-Vidéo.com internet radio you need to update your browser or download <a href="https://get.adobe.com/flashplayer/" target="_blank">Adobe Flash Player</a>.]]>
                </target>
            </trans-unit>
            <trans-unit id="loading">
                <source>loading</source>
                <target>Loading</target>
            </trans-unit>
            <trans-unit id="buy_on_amazon">
                <source>buy_on_amazon</source>
                <target>Buy on Amazon</target>
            </trans-unit>
            <trans-unit id="buy_on_itunes">
                <source>buy_on_itunes</source>
                <target>Buy on iTunes</target>
            </trans-unit>
            <trans-unit id="iphone_app_link_title">
                <source>iphone_app_link_title</source>
                <target>WebRadio iPhone app</target>
            </trans-unit>
            <trans-unit id="android_app_link_title">
                <source>android_app_link_title</source>
                <target>WebRadio Android app</target>
            </trans-unit>
            <trans-unit id="html_solution_description">
                <source>html_solution_description</source>
                <target>Listen Son-Vidéo internet radio from your browser.</target>
            </trans-unit>
            <trans-unit id="html_solution_link">
                <source>html_solution_link</source>
                <target>Son-Vidéo player</target>
            </trans-unit>
            <trans-unit id="stream_solution_description">
                <source>stream_solution_description</source>
                <target>Listen Son-Vidéo internet radio from the media player in your own setup.</target>
            </trans-unit>
            <trans-unit id="stream_solution_link">
                <source>stream_solution_link</source>
                <target>MP3 Stream</target>
            </trans-unit>
            <trans-unit id="iphone_solution_description">
                <source>iphone_solution_description</source>
                <target>Download Son-Vidéo WebRadio for you Iphone.</target>
            </trans-unit>
            <trans-unit id="iphone_solution_link">
                <source>iphone_solution_link</source>
                <target>iPhone app</target>
            </trans-unit>
            <trans-unit id="android_solution_description">
                <source>android_solution_description</source>
                <target>Download Son-Vidéo WebRadio for you Android phone.</target>
            </trans-unit>
            <trans-unit id="android_solution_link">
                <source>android_solution_link</source>
                <target>Android app</target>
            </trans-unit>
            <trans-unit id="cover_not_available">
                <source>cover_not_available</source>
                <target>Cover not available.</target>
            </trans-unit>
        </body>
    </file>
</xliff>
