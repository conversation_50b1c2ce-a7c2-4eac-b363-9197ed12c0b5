<?php

namespace SonVideo\Cms\FrontOffice\Application\Utils;

class ArrayUtils
{
    /**
     * Compute a one level array out of a multi-level array having keys.
     * Keys are concatenated with dot delimiter.
     * If an array does not use named keys, the values are returned as is.
     * Works with any depth (it's recursive).
     *
     * See unit tests for exemples.
     */
    public static function flattenArrayHavingKeys(array $array_having_keys, ?string $prefix = null): array
    {
        $flat_array = [];

        foreach ($array_having_keys as $key => $value) {
            if (is_array($value) && self::isArrayContainingNamedKeys($value)) {
                $flat_value = self::flattenArrayHavingKeys($value, $key);
                $flat_array = array_merge($flat_array, $flat_value);
            } else {
                $array_key = $prefix ? "$prefix.$key" : $key;
                $flat_array[$array_key] = $value;
            }
        }

        return $flat_array;
    }

    public static function isArrayContainingNamedKeys(array $array): bool
    {
        return array_reduce(
            array_keys($array),
            fn($result, $key): bool => $result || gettype($key) === 'string',
            false,
        );
    }
}
