<?php

namespace SonVideo\Cms\FrontOffice\Application\Manager\Payment\RedirectBackReturnUrl\Handler;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\Exception\UnexpectedResultException;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\RedirectBackReturnUrl\RedirectBackAfterPaymentHandlerInterface;
use Symfony\Component\HttpFoundation\Request;

class PrestoRedirectBackAfterPaymentHandler implements RedirectBackAfterPaymentHandlerInterface
{
    public const SUCCESSFUL_RETURN_STATUSES = ['DONE', 'LATER'];

    public function canHandle(string $code): bool
    {
        return PaymentMethod::PRESTO_CODE === $code;
    }

    /**
     * @throws UnexpectedResultException
     * @throws \JsonException
     */
    public function isSuccessful(Request $request): bool
    {
        $status = $request->get('status');
        if (!$status) {
            throw new UnexpectedResultException('[PRESTO] Parameter "status" was not found in response');
        }

        return in_array($status, static::SUCCESSFUL_RETURN_STATUSES, true);
    }
}
