<?php

namespace SonVideo\Cms\FrontOffice\Application\Manager\Payment\RedirectBackReturnUrl\Handler;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;
use SonVideo\Cms\FrontOffice\AppBundle\Referential\PaymentApiV2;
use SonVideo\Cms\FrontOffice\Application\Client\PaymentV2ClientInterface;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\Exception\UnexpectedResultException;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\RedirectBackReturnUrl\RedirectBackAfterPaymentHandlerInterface;
use Symfony\Component\HttpFoundation\Request;

class WorldlineRedirectBackAfterPaymentHandler implements RedirectBackAfterPaymentHandlerInterface
{
    private PaymentV2ClientInterface $client;
    private string $code;

    public function __construct(PaymentV2ClientInterface $client)
    {
        $this->client = $client;
    }

    public function canHandle(string $code): bool
    {
        $this->code = $code;

        return in_array(
            $code,
            [...PaymentMethod::WORLDLINE_CODES, PaymentMethod::WORLDLINE_GROUPED_CREDIT_CARDS_CODE],
            true,
        );
    }

    public function isSuccessful(Request $request): bool
    {
        try {
            $data = $this->getData($request);

            // successful hosted tokenization requests do not send back the REF
            if ([] === $data) {
                return true;
            }

            $response = $this->client->post(PaymentApiV2::TUNNEL_REDIRECT_BACK_STATUS_CHECKER, [
                'code' => $this->code,
                'data' => $data,
            ]);

            return $response['is_successful'] ?? false;
        } catch (\Exception $exception) {
            throw new UnexpectedResultException(
                '[WORLDLINE] Could not check status of the payment after redirection',
                0,
                $exception,
            );
        }
    }

    private function getData(Request $request): array
    {
        $ref = $request->get('REF');
        if (null !== $ref) {
            return ['payment_id' => $ref];
        }

        $hosted_checkout_id = $request->get('hostedCheckoutId');
        if (null !== $hosted_checkout_id) {
            return ['hosted_checkout_id' => $hosted_checkout_id];
        }

        return [];
    }
}
