{% if banners is not empty %}
    <div class="SVDv3_list_products">
        <h3 class="title-associated-products">{% trans %}also_discover{% endtrans %}</h3>
        <ul class="list-store-calls">
            {% for banner in banners %}
                <li class="store-call widget-{{ banner.getTitle(app.request.getLocale()) | slugify }}" id='banner-{{ loop.index }}' data-title='{{ banner.getTitle(app.request.getLocale()) }}' data-id='{{ banner.clickbait_id }}' data-location='{{ locations[0] }}' data-position='{{ loop.index }}' data-url='{{ banner.url }}'>
                    <a href="{{ banner.url }}" id="SVDv3_appelBoutique_{{ banner.clickbait_id }}">
                        <div class="content-img">
                            <div class="content-img-background lozad" data-background-image="{{ asset(banner.getMediaUri(app.request.getLocale()), 'clickbait_images') }}">
                            </div>
                        </div>
                        <div class="content-text">
                            <div class="content-title">
                                <p class="store-call-title">{{ banner.getTitle(app.request.getLocale()) }}</p>
                                <p class="store-call-content" id="SVDv3_appelBoutique_accroche_{{ banner.clickbait_id }}">{{ banner.getHook(app.request.getLocale()) | raw }}</p>
                                <p class="store-call-button"><span>{{ banner.getOverTitle(app.request.getLocale()) }}</span></p>
                            </div>
                        </div>
                    </a>
                </li>
            {% endfor %}
        </ul>
    </div>
{% endif %}
