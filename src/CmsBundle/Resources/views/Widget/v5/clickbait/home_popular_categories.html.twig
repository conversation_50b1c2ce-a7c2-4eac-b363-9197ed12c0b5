{% if elements is not empty %}
    {% embed 'AppBundle:V5/Component:swiper.html.twig' with {
        data_context: 'popular-categories',
        class: 'w-full my-6 md:order-2 lg:container lg:my-0',
        title_class: 'hidden md:block',
        init_attr: 'data-swiper-popular-categories',
        swiper_class: 'xl:flex xl:justify-center [&:not(.swiper-initialized)]:!overflow-visible',
        wrapper_class: 'xl:!w-auto xl:flex xl:justify-between xl:gap-x-2.5 xl:max-w-[1180px]',
        hide_ui: ['scrollbar', 'navigation'],
        key_to_check: 'elements',
    } %}
        {% block content %}
            {% for element in elements %}
                {% set item = element.toArray(app.request.locale) %}
                <div class="swiper-slide group lg:!w-auto lg:!pl-[11px] lg:!pr-[11px] lg:!max-w-[131px]">
                    <a href="{{ item.url }}" class="flex flex-col items-center gap-y-0.5 md:gap-y-[11px]">
                        <div>
                            <div class='w-16 lg:w-20 xl:w-24 h-16 lg:h-20 xl:h-24 rounded-full bg-[#f0f0f0]'></div>
                            <div class="relative -mt-16 lg:-mt-20 xl:-mt-24 lg:group-hover:scale-110 lg:duration-200">
                                <img fetchpriority="high" height="100%" width="96px" class="mx-auto w-16 lg:w-20 xl:w-24"
                                     src="{{ preset(asset(item.media, 'static_images'), '100') }}"
                                     srcset="{{ presets(asset(item.media, 'static_images'), [['75','75w'], ['90','90w'], ['100','100w'], ['150','150w'], ['300','300w']]) }}"
                                     sizes="(min-width: 1280px) 100px, (min-width: 974px) 90px, 64px"
                                     alt="Logo exemple de {{ item.name }}"/>
                            </div>
                        </div>
                        <span class="text-xs font-semibold mt-2 text-svd-neutral-700 md:text-sm xl:text-base text-center">{{ item.name }}</span>
                    </a>
                </div>
            {% endfor %}

        {% endblock %}
    {% endembed %}
{% endif %}
