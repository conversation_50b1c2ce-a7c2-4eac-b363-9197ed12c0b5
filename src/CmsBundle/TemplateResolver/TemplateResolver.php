<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver;

use PommProject\Foundation\Pomm;
use PommProject\ModelManager\Model\CollectionIterator;
use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\CmsBundle\ExpressionLanguage\ExpressionLanguageFactory;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\TemplateResolverModel;

/**
 * Class TemplateResolver
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle
 * <AUTHOR> <<EMAIL>>
 */
class TemplateResolver
{
    private Pomm $pomm;

    private LoggerInterface $logger;

    /**
     * TemplateResolver constructor.
     */
    public function __construct(Pomm $pomm, LoggerInterface $logger)
    {
        $this->pomm = $pomm;
        $this->logger = $logger;
    }

    /**
     * resolve
     *
     * Try to returns a response in function of context.
     * Template rules are evaluated with the context. If none satisfies the context then null is returned.
     *
     * @return string|null
     */
    public function resolve(ContextInterface $context)
    {
        $expression_language = ExpressionLanguageFactory::getExpressionLanguage();
        $template_rules = $this->fetchRulesByTypeOrderByMaxPriority($context->getType());

        foreach ($template_rules as $template) {
            try {
                if ($expression_language->evaluate($template->get('rule'), $context->getContext())) {
                    $string_template = $this->fetchStringTemplate($template->get('template_resolver_id'));
                    if (!is_null($string_template)) {
                        return $string_template;
                    }
                }
            } catch (\Exception $e) {
                $this->logger->error('template_resolver.resolve.template_error', [
                    'template' => $template->extract(),
                    'error_type' => get_class($e),
                    'error_code' => $e->getCode(),
                    'error_message' => $e->getMessage(),
                ]);
            }
        }

        return null;
    }

    /**
     * fetchRulesByTypeOrderByMaxPriority
     */
    protected function fetchRulesByTypeOrderByMaxPriority(string $context_type): CollectionIterator
    {
        return $this->getTemplateResolverModel()->fetchRulesByTypeOrderByMaxPriority($context_type);
    }

    /**
     * fetchStringTemplate
     *
     * @return null|string
     */
    protected function fetchStringTemplate(string $template_resolver_id)
    {
        return $this->getTemplateResolverModel()->fetchStringTemplate($template_resolver_id);
    }

    /**
     * getTemplateResolverModel
     */
    private function getTemplateResolverModel(): TemplateResolverModel
    {
        return $this->pomm->getDefaultSession()->getModel(TemplateResolverModel::class);
    }
}
