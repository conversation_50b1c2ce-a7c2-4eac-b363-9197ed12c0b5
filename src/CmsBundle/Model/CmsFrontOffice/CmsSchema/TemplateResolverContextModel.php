<?php

namespace SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\AutoStructure\TemplateResolverContext as TemplateResolverContextStructure;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\TemplateResolverContext;

/**
 * TemplateResolverContextModel
 *
 * Model class for table template_resolver_context.
 *
 * @see Model
 */
class TemplateResolverContextModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new TemplateResolverContextStructure();
        $this->flexible_entity_class = TemplateResolverContext::class;
    }
}
