<?php

namespace SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema;

use PommProject\ModelManager\Model\FlexibleEntity;

/**
 * StaticPageI18n
 *
 * Flexible entity for relation
 * cms.static_page_i18n
 *
 * @see FlexibleEntity
 */
class StaticPageI18n extends FlexibleEntity
{
    /**
     * getRouteUri
     *
     * Construct and returns route from entity.
     */
    public function getRouteUri(): string
    {
        return $this->get('uri');
    }
}
