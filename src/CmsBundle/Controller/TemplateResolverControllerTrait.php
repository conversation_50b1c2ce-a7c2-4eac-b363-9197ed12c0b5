<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\Controller;

use SonVideo\Cms\FrontOffice\CmsBundle\Exception\PreviewNotAuthorizedException;
use SonVideo\Cms\FrontOffice\CmsBundle\Exception\TypeNotValidForPreviewException;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\ContextException;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\ContextInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * trait TemplateResolverControllerTrait
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
trait TemplateResolverControllerTrait
{
    /**
     * resolveTemplate
     *
     * Try to resolve using the context.
     * Check if we are in preview mode, otherwise try to resolve.
     * If a template is found, returns a response with the view parameters, otherwise returns null.
     *
     * @return null|Response
     * @throws \Exception
     */
    public function resolveTemplate(ContextInterface $context, array $view_parameters)
    {
        try {
            $string_template =
                $this->previewTemplate($context) ?? $this->get('cms.template_resolver')->resolve($context);
        } catch (ContextException $e) {
            // If context type is not defined in DB, log the error then returns null.
            $this->get('logger')->error('template_resolver_controller_trait.resolve_template', [
                'error' => 'Context definition not found in db.',
                'message' => $e->getMessage(),
            ]);

            return null;
        }

        return is_null($string_template) ? null : $this->renderStringTemplate($string_template, $view_parameters);
    }

    /**
     * previewTemplate
     *
     * Returns a string template if we are in preview mode, null otherwise.
     *
     * @return string|null
     * @throws \Exception
     */
    private function previewTemplate(ContextInterface $context)
    {
        try {
            return $this->get('cms.template_previewer')->preview($context);
        } catch (\Exception $e) {
            if ($e instanceof PreviewNotAuthorizedException || $e instanceof TypeNotValidForPreviewException) {
                $this->addFlash('error', $e->getMessage());
            } else {
                throw $e;
            }
        }

        return null;
    }

    /**
     * renderStringTemplate
     */
    protected function renderStringTemplate(string $string_template, array $params = []): Response
    {
        // Important: to use cache, name must be the same.
        $name = sprintf('__string_template__%s', hash('sha512', $string_template, false));

        // Add our template into Twig environment.
        // The template could use others templates in filesystem. So we need to use Twig_Loader_Chain to
        // define our template in addition of the current loader.
        $this->get('twig')->setLoader(
            new \Twig_Loader_Chain([
                $this->get('twig')->getLoader(),
                new \Twig_Loader_Array([
                    $name => $string_template,
                    // we need to add below line to use twig cache otherwise twig crashes... Seems to be a bug in twig.
                    $string_template => $string_template,
                ]),
            ]),
        );

        // Let the cache active could throw error. Rendering is slowest but it's more secure!
        $this->get('twig')->setCache(false);

        return new Response($this->get('twig')->render($name, $params));
    }
}
