<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\V5\Home;

use PommProject\Foundation\Exception\FoundationException;
use SonVideo\Cms\FrontOffice\AppBundle\Cache\CacheTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\HomeSchema\StandBestSellerModel;
use SonVideo\Cms\FrontOffice\Application\UseCase\Price\PriceHelpers;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class BestSellerController
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\Home
 * <AUTHOR> <<EMAIL>>
 */
class BestSellerController extends Controller
{
    use CacheTrait;

    public const HOME_BEST_SELLER_MAX_NUMBER = 15;

    /**
     * @throws FoundationException
     */
    public function showAction(string $locale = APP_DEFAULT_LOCALE): Response
    {
        /** @var StandBestSellerModel $model */
        $model = $this->get('pomm')
            ->getDefaultSession()
            ->getModel(StandBestSellerModel::class);

        $articles = $model->fetchHomeBestSellerArticles($locale, static::HOME_BEST_SELLER_MAX_NUMBER)->extract();
        $articles = array_map([PriceHelpers::class, 'mapReferencePriceAndDiscountOnArticle'], $articles);

        if ($articles !== []) {
            $this->addTags(array_column($articles, 'article_id'), 'article');
        }

        return $this->render('AppBundle:V5/Component:swiper.html.twig', [
            'data_context' => 'our-best-sellers',
            'additional_attrs' => 'data-ga=products_best_sellers',
            'title' => 'Les meilleures ventes',
            'articles' => $articles,
        ]);
    }
}
