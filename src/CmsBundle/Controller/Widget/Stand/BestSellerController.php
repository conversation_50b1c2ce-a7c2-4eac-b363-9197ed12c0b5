<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\Stand;

use SonVideo\Cms\FrontOffice\AppBundle\Cache\CacheTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleI18nModel;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class BestSellerController
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\Stand
 * <AUTHOR> <<EMAIL>>
 */
class BestSellerController extends Controller
{
    public const BEST_SELL_LIMIT = 12;

    use CacheTrait;

    /**
     * showAction
     */
    public function showAction(string $stand_tag, string $locale): Response
    {
        $articles = $this->get('pomm')
            ->getDefaultSession()
            ->getModel(ArticleI18nModel::class)
            ->findBestSellerForStand($stand_tag, $locale, static::BEST_SELL_LIMIT)
            ->extract();

        if ((is_countable($articles) ? count($articles) : 0) !== 0) {
            $this->addTags(array_column($articles, 'article_id'), 'article');
        }

        return $this->render('CmsBundle::Widget/stand/best_seller.html.twig', ['articles' => $articles]);
    }
}
