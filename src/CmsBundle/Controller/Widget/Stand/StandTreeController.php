<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\Stand;

use SonVideo\Cms\FrontOffice\AppBundle\Cache\CacheTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class StandTreeController
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\Stand
 * <AUTHOR> <<EMAIL>>
 */
class StandTreeController extends Controller
{
    use CacheTrait;

    /**
     * showAction
     */
    public function showAction(string $stand_tag, string $locale): Response
    {
        $stands = $this->get('pomm')
            ->getDefaultSession()
            ->getModel(StandI18nModel::class)
            ->findAllDescendantsForTagpath($stand_tag, $locale)
            ->extract();

        if ((is_countable($stands) ? count($stands) : 0) !== 0) {
            $this->addTags(array_column($stands, 'stand_id'), 'stand');
        }

        return $this->render('CmsBundle::Widget/stand/stand_tree.html.twig', ['stands' => $stands]);
    }
}
