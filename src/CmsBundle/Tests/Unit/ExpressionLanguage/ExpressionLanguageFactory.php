<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\ExpressionLanguage;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\CmsBundle\ExpressionLanguage\ExpressionLanguageFactory as TestedClass;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

/**
 * Class ExpressionLanguageFactory
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\ExpressionLanguage
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class ExpressionLanguageFactory extends Test
{
    /**
     * testGetExpressionLanguage
     */
    public function testGetExpressionLanguage(): void
    {
        $this->assert('Check result is an instance of ExpressionLanguage.')
            ->given($instance = TestedClass::getExpressionLanguage())
            ->object($instance)
            ->isInstanceOf(ExpressionLanguage::class)

            ->assert('Check date function is well registered')
            ->given($res = $instance->evaluate('date("1998-07-12") < date("now")'))
            ->boolean($res)
            ->isTrue()

            ->assert('Check betweenDate function is well registered')
            ->given($res = $instance->evaluate('betweenDate("1998-07-12", "2010-01-31")'))
            ->boolean($res)
            ->isFalse();
    }
}
