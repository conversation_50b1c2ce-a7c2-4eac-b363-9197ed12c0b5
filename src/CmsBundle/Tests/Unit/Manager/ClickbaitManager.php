<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\Manager\Widget;

use Psr\Log\NullLogger;
use PommProject\ModelManager\Model\CollectionIterator;
use Psr\Log\InvalidArgumentException;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\CmsBundle\Manager\Widget\ClickbaitManager as TestedClass;

/**
 * ClickbaitManager
 *
 * @package     SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\Manager\ClickbaitManager
 * @copyright   2017 Son-Video Distribution
 * <AUTHOR> <<EMAIL>>, Benoit Audic <<EMAIL>>
 */
class ClickbaitManager extends PommTest
{
    private function getTestedInstance(): TestedClass
    {
        return new TestedClass($this->getPommSession(), new NullLogger());
    }

    /**
     * testResolveHomeBannerTypeFailure
     */
    public function testResolveHomeBannerTypeFailure(): void
    {
        $this->assert('Test with non existing type')
            ->exception(function (): void {
                $this->getTestedInstance()->resolve('test', 'fr', 'home', 1);
            })
            ->isInstanceOf(InvalidArgumentException::class);
    }

    /**
     * testResolveMultipleColumnBanners
     */
    public function testResolveMultipleColumnBanners(): void
    {
        $this->assert('Check default clickbaits when no location is specified')
            ->given($defaults = $this->getTestedInstance()->resolveMultiple([], 'column_banner', 'fr'))
            ->object($defaults)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($defaults->count())
            ->isEqualTo(2)
            ->assert('Check some fields of default clickbaits')
            ->array($immediate_recall = $defaults->extract()[0])
            ->string['clickbait_id']->isEqualTo('499ca65d-9b8f-444a-93ec-e73c9c92dbb3')
            ->string['name']->isEqualTo('Rappel immédiat - Un simple clic et un conseiller vous rappelle gratuitement')
            ->boolean['is_active']->isTrue()
            ->string($immediate_recall['definition']['column_banner']['fr']['title'])
            ->isEqualTo('Rappel immédiat')
            ->array($catalog = $defaults->extract()[1])
            ->string['clickbait_id']->isEqualTo('d2f4dde0-65e0-4b9a-9d90-657e1f3cbcef')
            ->string['url']->isEqualTo('/catalogue')
            ->boolean['is_active']->isTrue()
            ->string['name']->isEqualTo('Catalogue 2018 - nouveautés et promotions')
            ->assert('Check empty default locations for a non existing foreign culture')
            ->given($defaults_en = $this->getTestedInstance()->resolveMultiple([], 'column_banner', 'en'))
            ->integer($defaults_en->count())
            ->isEqualTo(0)
            ->assert('Check clickbaits on a list of locations')
            ->given(
                $clickbaits = $this->getTestedInstance()->resolveMultiple(
                    ['stand_9', 'parent_stand_3'],
                    'column_banner',
                    'fr',
                ),
            )
            ->object($clickbaits)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($clickbaits->count())
            ->isEqualTo(5)
            ->assert('Check that the default clickbaits are present in the results')
            ->given($clickbaits_list = $clickbaits->extract())
            ->array(array_column($clickbaits_list, 'clickbait_id'))
            ->containsValues(['499ca65d-9b8f-444a-93ec-e73c9c92dbb3', 'd2f4dde0-65e0-4b9a-9d90-657e1f3cbcef'])
            ->assert('Check the names of other clickbaits')
            ->array(array_column($clickbaits_list, 'name'))
            ->containsValues([
                'Super promo Haute-Fidélité',
                'Enceintes - comment bien choisir ?',
                'Dali - Gamme Spektor',
            ])
            ->assert(
                'Check order of clickbaits (first recall, after promo offer (parent_stand_%), catalog and finally others types (order by display order)',
            )
            ->string($clickbaits_list[0]['clickbait_id'])
            ->isEqualTo('499ca65d-9b8f-444a-93ec-e73c9c92dbb3')
            ->string($clickbaits_list[1]['name'])
            ->isEqualTo('Super promo Haute-Fidélité')
            ->string($clickbaits_list[2]['clickbait_id'])
            ->isEqualTo('d2f4dde0-65e0-4b9a-9d90-657e1f3cbcef')
            ->string($clickbaits_list[3]['name'])
            ->isEqualTo('Enceintes - comment bien choisir ?')
            ->integer($clickbaits_list[4]['display_order'])
            ->isGreaterThan($clickbaits_list[3]['display_order']);
    }
}
