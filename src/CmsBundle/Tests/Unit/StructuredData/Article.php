<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\StructuredData;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\CommonContentReviewModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\CmsBundle\StructuredData\Article as TestedClass;

/**
 * Class Article
 *
 * @package CmsBundle\Tests\Unit\StructuredData
 * <AUTHOR> <<EMAIL>>
 */
class Article extends PommTest
{
    /**
     * getTestedClass
     *
     *
     */
    private function getTestedClass(string $uri): TestedClass
    {
        $article = $this->getPommSession()
            ->getModel(ArticleModel::class)
            ->findByUriAndCulture($uri, 'fr');

        $reviews = $this->getPommSession()
            ->getModel(CommonContentReviewModel::class)
            ->findReviewsForStructuredData($article->get('common_content_id'));

        $article['main_stands'] = $this->getPommSession()->getModel(StandModel::class)
            ->findHierarchyForStand($article['default_stand_id'], 'fr')->extract()
        ;

        $article['declinations'] = $this->getPommSession()
            ->getModel(ArticleModel::class)
            ->getDeclinationByCommonContentId($article->get('common_content_id'), 'fr')
            ->extract();

        return new TestedClass($article, $reviews, $this->getContainer());
    }

    /**
     * testGetData
     */
    public function testGetData(): void
    {
        $this->assert('Test product structured data for article with declination')
            ->given($product = $this->getTestedClass('/article/elipson-prestige-4i-calvados-fr')->getData())
            ->array($product)
            ->hasKeys(['@type', 'name', 'image', 'description', 'category', 'brand', 'offers', 'gtin'])
            ->string($product['@type'])
            ->isEqualTo('Product')
            ->string($product['name'])
            ->isEqualTo('Prestige 4i Calvados')
            ->string($product['image'])
            ->isEqualTo(
                sprintf(
                    '%s/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_600.jpg',
                    $this->getContainer()->getParameter('base_url'),
                ),
            )
            ->string($product['description'])
            ->contains(
                'L\'enceinte Elipson Prestige 4i est une grande et puissante colonne qui fait la démonstration de tout le savoir-faire de la marque française.',
            )
            ->string($product['category'])
            ->isEqualTo('Amplis home-cinéma')
            ->given($offers = $product['offers'])
            ->array($offers)
            ->hasKey('@type')
            ->string($offers['@type'])
            ->isEqualTo('Offer')
            ->string($product['gtin'])
            ->isEqualTo('123456789');

        $this->checkAggregateRating($product)->checkReview($product);
    }

    public function testDeclinationLength(): void
    {
        $this->loadSqlFixtures(['article_structured_data.sql']);
        $this->assert('Test article structured data with too long description')
            ->given($product = $this->getTestedClass('/article/enceintes-compactes/kef/q100-blanc')->getData())
            ->array($product)
            ->hasKeys(['@type', 'name', 'image', 'description', 'category', 'brand', 'offers', 'gtin'])
            ->string($product['description'])
            ->hasLengthLessThan(5000)
            ->endWith('…')
        ;
    }

    /**
     * testGetItemWithoutDeclination
     *
     */
    public function testGetItemWithoutDeclination(): void
    {
        $this->assert('Test product structured data for article without declination')
            ->given(
                $product = $this->getTestedClass(
                    '/article/enceintes-compactes/triangle/plaisir-lymna-blanc',
                )->getData(),
            )
            ->array($product)
            ->hasKeys(['@type', 'name', 'image', 'description', 'category', 'brand', 'offers', 'gtin'])
            ->string($product['@type'])
            ->isEqualTo('Product')
            ->string($product['name'])
            ->isEqualTo('Plaisir Lymna Blanc')
            ->string($product['image'])
            ->isEqualTo(
                sprintf(
                    '%s/images/dynamic/Enceintes/articles/Triangle/TRIPLAILYMBC/Triangle-Plaisir-Lymna-Blanc_Vd2_600.jpg',
                    $this->getContainer()->getParameter('base_url'),
                ),
            )
            ->string($product['description'])
            ->isEqualTo('introduction')
            ->string($product['category'])
            ->isEqualTo('Amplis home-cinéma')
            ->given($offers = $product['offers'])
            ->array($offers)
            ->hasKey('@type')
            ->string($offers['@type'])
            ->isEqualTo('Offer')
            ->string($product['gtin'])
            ->isEqualTo('123123123');
    }

    /**
     * checkAggregateRating
     */
    protected function checkAggregateRating(array $product): self
    {
        $this->assert('Product contains a valid aggregateRating tag.')
            ->array($aggregate_rating = $product['aggregateRating'])
            ->string($aggregate_rating['@type'])
            ->isEqualTo('AggregateRating')
            ->string($aggregate_rating['ratingValue'])
            ->isEqualTo('4.5')
            ->integer($aggregate_rating['ratingCount'])
            ->isEqualTo(5)
            ->string($aggregate_rating['bestRating'])
            ->isEqualTo('5')
            ->string($aggregate_rating['worstRating'])
            ->isEqualTo('1');

        return $this;
    }

    /**
     * checkReviews
     */
    protected function checkReview(array $product): self
    {
        $this->assert('Product contains a valid review tag.')
            ->array($product['review'])
            ->hasSize(1)
            ->array($review = $product['review'][0])
            ->string($review['@type'])
            ->isEqualTo('Review')
            ->string($review['reviewBody'])
            ->isEqualTo('Ce produit est excellent')
            ->string($review['inLanguage'])
            ->isEqualTo('fr')
            ->string($review['datePublished'])
            ->match('/^\d{4}-\d{2}-\d{2}$/')
            ->array($rating = $review['reviewRating'])
            ->string($rating['@type'])
            ->isEqualTo('Rating')
            ->string($rating['ratingValue'])
            ->isEqualTo('5')
            ->string($rating['bestRating'])
            ->isEqualTo('5')
            ->string($rating['worstRating'])
            ->isEqualTo('1')
            ->array($review['publisher'])
            ->isEqualTo(['@type' => 'Organization', 'name' => 'son-video.com'])
            ->array($review['author'])
            ->isEqualTo(['@type' => 'Person', 'name' => 'Batman']);

        return $this;
    }
}
