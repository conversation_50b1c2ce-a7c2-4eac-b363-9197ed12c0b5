<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\StructuredData;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\CmsBundle\StructuredData\AggregateOffer as TestedClass;

/**
 * Class AggregateOffer
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\StructuredData
 * <AUTHOR> <<EMAIL>>
 */
class AggregateOffer extends PommTest
{
    /**
     * getTestedClass
     *
     *
     */
    private function getTestedClass(string $uri): TestedClass
    {
        $article = $this->getPommSession()
            ->getModel(ArticleModel::class)
            ->findByUriAndCulture($uri, 'fr');

        $article['declinations'] = $this->getPommSession()
            ->getModel(ArticleModel::class)
            ->getDeclinationByCommonContentId($article->get('common_content_id'), 'fr')
            ->extract();

        return new TestedClass($article, $this->getContainer());
    }

    /**
     * testGetData
     */
    public function testGetData(): void
    {
        $this->assert('Test aggregate offer structured data')
            ->given($offer = $this->getTestedClass('/article/elipson-prestige-4i-calvados-fr')->getData())
            ->array($offer)
            ->hasKeys(['@type', 'offerCount', 'lowPrice', 'highPrice', 'priceCurrency', 'availability'])
            ->string($offer['@type'])
            ->isEqualTo('AggregateOffer')
            // one article does'nt have entry on product table and one article (123461) does'nt have selling price
            ->integer($offer['offerCount'])
            ->isEqualTo('2')
            ->float($offer['lowPrice'])
            ->isEqualTo('200')
            ->float($offer['highPrice'])
            ->isEqualTo('599')
            ->string($offer['priceCurrency'])
            ->isEqualTo('EUR')
            ->string($offer['availability'])
            ->isEqualTo('http://schema.org/InStock');

        $this->assert('Exclude destocks from offers')
            ->given($offer = $this->getTestedClass('/article/super-enceinte')->getData())
            ->array($offer)
            ->hasKeys(['@type', 'offerCount', 'lowPrice', 'highPrice', 'priceCurrency'])
            ->string($offer['@type'])
            ->isEqualTo('AggregateOffer')
            // one article does'nt have entry on product table and one article (123461) does'nt have selling price
            ->integer($offer['offerCount'])
            ->isEqualTo('3')
            ->float($offer['lowPrice'])
            ->isEqualTo('100')
            ->float($offer['highPrice'])
            ->isEqualTo('100')
            ->string($offer['priceCurrency'])
            ->isEqualTo('EUR');
    }
}
