<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver\Context;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ShopSchema\ShopI18n;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context\ShopContext as TestedClass;

/**
 * Class ShopContext
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver\Context
 * <AUTHOR> <<EMAIL>>
 */
class ShopContext extends Test
{
    /**
     * getTestedInstance
     */
    public static function getTestedInstance(int $shop_id): TestedClass
    {
        return new TestedClass(new ShopI18n(['shop_id' => $shop_id]));
    }

    /**
     * testGetType
     */
    public function testGetType(): void
    {
        $this->assert('Check the type.')
            ->given($type = self::getTestedInstance(1)->getType())
            ->string($type)
            ->isEqualTo('shop');
    }

    /**
     * testGetContext
     */
    public function testGetContext(): void
    {
        $this->assert('Check the array context.')
            ->given($context = self::getTestedInstance(1)->getContext())
            ->array($context)
            ->hasKeys(['shop'])
            ->object['shop']->isInstanceOf(ShopI18n::class);
    }
}
