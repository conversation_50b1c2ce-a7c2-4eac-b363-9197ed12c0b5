<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Brand;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\CmsBundle\Exception\PreviewNotAuthorizedException;
use SonVideo\Cms\FrontOffice\CmsBundle\Exception\TypeNotValidForPreviewException;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context\BrandContext;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\TemplatePreviewer as TestedClass;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context\ArticleContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Class TemplatePreviewer
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver
 * <AUTHOR> Marniesse <<EMAIL>>
 */
class TemplatePreviewer extends PommTest
{
    /**
     * getTestedInstance
     */
    private function getTestedInstance(array $query_parameters): TestedClass
    {
        $request_stack = new RequestStack();
        $request_stack->push(new Request($query_parameters));

        return new TestedClass($this->getContainer()->get('pomm'), $request_stack);
    }

    /**
     * getValidToken
     */
    public static function getValidToken(): string
    {
        return password_hash(sprintf(TestedClass::TEMPLATE_PREVIEW_TOKEN, date('Y-m-d')), PASSWORD_DEFAULT);
    }

    /**
     * testPreviewOk
     */
    public function testPreviewOk(): void
    {
        $this->assert('Check if preview returns a template string for a valid preview request.')
            ->given(
                $instance = $this->getTestedInstance([
                    'template_resolver_id' => '07a0f945-6188-4c47-a96c-24433e4ae22f',
                    'token' => self::getValidToken(),
                ]),
            )
            ->and($res = $instance->preview(new BrandContext(new Brand())))
            ->string($res)
            ->isEqualTo('<div>Template never displayed.</div>');
    }

    /**
     * testPreviewNonValidType
     */
    public function testPreviewNonValidType(): void
    {
        $this->assert('Check if preview returns an exception if type is not valid for context.')
            ->given(
                $instance = $this->getTestedInstance([
                    'template_resolver_id' => '07a0f945-6188-4c47-a96c-24433e4ae22f',
                    'token' => self::getValidToken(),
                ]),
            )
            ->exception(function () use ($instance): void {
                $instance->preview(new ArticleContext(new Article()));
            })
            ->isInstanceOf(TypeNotValidForPreviewException::class);
    }

    /**
     * testPreviewNonAuthorized
     */
    public function testPreviewNonAuthorized(): void
    {
        $this->assert('Check if preview returns an exception if token is not valid.')
            ->given(
                $instance = $this->getTestedInstance([
                    'template_resolver_id' => '07a0f945-6188-4c47-a96c-24433e4ae22f',
                    'token' => '42',
                ]),
            )
            ->exception(function () use ($instance): void {
                $instance->preview(new BrandContext(new Brand()));
            })
            ->isInstanceOf(PreviewNotAuthorizedException::class);
    }
}
