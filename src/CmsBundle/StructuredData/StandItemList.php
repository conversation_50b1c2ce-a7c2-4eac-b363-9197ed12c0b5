<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\StructuredData;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use Symfony\Component\DependencyInjection\ContainerInterface;

class StandItemList extends AbstractStructuredData implements StructuredDataInterface
{
    protected string $name;

    protected string $slug;

    /**
     * @var CollectionIterator<StandI18n>
     */
    protected CollectionIterator $stands;

    public function __construct(string $name, string $slug, CollectionIterator $stands, ContainerInterface $container)
    {
        $this->name = $name;
        $this->slug = $slug;
        $this->stands = $stands;

        parent::__construct($container);
    }

    /**
     * {@inheritdoc}
     *
     * base_url is always used because google doesn't accept multi domain in structured data.
     */
    public function getData(): array
    {
        return [
            '@context' => 'http://schema.org',
            '@type' => 'ItemList',
            'name' => $this->name,
            'url' => sprintf('%s%s', $this->container->getParameter('base_url'), $this->slug),
            'numberOfItems' => $this->stands->count(),
            'itemListElement' => $this->getListElements(),
        ];
    }

    private function getListElements(): array
    {
        $list_elements = [];
        /** @var StandI18n $stand */
        foreach ($this->stands as $position => $stand) {
            $list_elements[] = [
                '@type' => 'ListItem',
                'position' => $position + 1,
                'image' => sprintf('%s%s', $this->container->getParameter('base_url'), $stand->get('logo_uri')),
                'url' => sprintf('%s/rayon/%s', $this->container->getParameter('base_url'), $stand->get('slug')),
                'name' => $stand->get('name'),
            ];
        }

        return $list_elements;
    }
}
