<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\ExpressionLanguage;

use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

/**
 * Class ExpressionLanguageFactory
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\ExpressionLanguage
 * <AUTHOR> <<EMAIL>>
 */
class ExpressionLanguageFactory
{
    /**
     * getExpressionLanguage
     *
     * Returns an instance of expression language with new providers.
     */
    public static function getExpressionLanguage(): ExpressionLanguage
    {
        $expression_language = new ExpressionLanguage();
        $expression_language->registerProvider(new DateExpressionLanguageProvider());

        return $expression_language;
    }
}
