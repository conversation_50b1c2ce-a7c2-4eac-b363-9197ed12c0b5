<?php

namespace SonVideo\Cms\FrontOffice\Infrastructure\Controller\Search;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\TrackClick\TrackClick;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\TrackClick\TrackClickRequest;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AutocompleteClickController extends BaseController
{
    /**
     * @Route("/autocomplete/click", name="autocomplete_click", methods={"POST"})
     */
    public function __invoke(Request $request, TrackClick $use_case): JsonResponse
    {
        $this->mustBeXmlHttpRequest($request);

        try {
            $data = json_decode($request->getContent(), null, 512, JSON_THROW_ON_ERROR);
            $request = new TrackClickRequest($data->search_terms, $data->document_id, ['autocomplete']);
            $response = $use_case->execute($request);

            return $this->json($response);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception]);

            return $this->json($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
