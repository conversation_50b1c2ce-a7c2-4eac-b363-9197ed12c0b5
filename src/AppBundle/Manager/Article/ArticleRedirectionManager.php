<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article;

use PommProject\Foundation\Where;
use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleUrlModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Routing\RedirectionTrait;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Translation\TranslatorInterface;

/**
 * Class ArticleRedirectionManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article
 * <AUTHOR> Baudouin <<EMAIL>>
 */
class ArticleRedirectionManager
{
    use RedirectionTrait;

    private PommSession $pomm_session;
    private TranslatorInterface $translator;
    private RouterInterface $router;

    /**
     * ArticleRedirectionManager constructor.
     */
    public function __construct(PommSession $pomm_session, TranslatorInterface $translator, RouterInterface $router)
    {
        $this->pomm_session = $pomm_session;
        $this->translator = $translator;
        $this->router = $router;
    }

    /**
     * getRouter
     */
    public function getRouter(): RouterInterface
    {
        return $this->router;
    }

    /**
     * redirectArticle
     *
     *
     */
    public function redirectArticle(string $uri): RedirectResponse
    {
        $partial_urls = array_values(
            array_filter(explode('/', $uri), fn($value): bool => $value !== '' && $value !== null),
        );
        $redirection = null;
        $category_slug = isset($partial_urls[1]) ? $this->getCategorySlug($partial_urls[1]) : null;

        if ($category_slug !== null) {
            $redirection = $this->getPartialUrlRedirection($partial_urls, $category_slug);
        }

        if (!$redirection instanceof RedirectResponse) {
            $redirection = $this->getArticleRedirection($uri);
        }

        if (!$redirection instanceof RedirectResponse) {
            throw new NotFoundHttpException($this->translator->trans('unknown_article'));
        }

        return $redirection;
    }

    /**
     * getCategorySlug
     *
     *
     * @return string|null
     */
    protected function getCategorySlug(string $partial_url)
    {
        $category = $this->pomm_session
            ->getModel(CategoryModel::class)
            ->findWhere(Where::create('slug ~ $*', [$partial_url]))
            ->current();

        if ($category !== null) {
            return $category->get('slug');
        }

        return null;
    }

    /**
     * getPartialUrlRedirection
     *
     */
    protected function getPartialUrlRedirection(array $partial_urls, string $category_slug): ?RedirectResponse
    {
        $redirection = null;

        switch (count($partial_urls)) {
            case 2:
                $redirection = $this->redirectToRoute(
                    'category_get',
                    ['slug' => $category_slug],
                    Response::HTTP_MOVED_PERMANENTLY,
                );
                break;
            case 3:
                $redirection = $this->redirectToRoute(
                    'show_brand_category',
                    ['slug' => $category_slug, 'brand_slug' => $partial_urls[2]],
                    Response::HTTP_MOVED_PERMANENTLY,
                );
                break;
        }

        return $redirection;
    }

    /**
     * getArticleRedirection
     *
     */
    protected function getArticleRedirection(string $uri): ?RedirectResponse
    {
        $redirected_article = $this->pomm_session->getModel(ArticleUrlModel::class)->fetchRedirectInformation($uri);

        if ($redirected_article !== null && $redirected_article['article_url'] !== $uri) {
            return $this->redirect($redirected_article['article_url'], Response::HTTP_MOVED_PERMANENTLY);
        }

        return null;
    }
}
