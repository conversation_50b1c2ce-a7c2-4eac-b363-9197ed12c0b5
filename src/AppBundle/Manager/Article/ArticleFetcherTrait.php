<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use PommProject\ModelManager\Session as PommSession;

/**
 * Trait ArticleIdFetcherTrait
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article
 * <AUTHOR> <<EMAIL>>
 */
trait ArticleFetcherTrait
{
    private $article_ids;

    abstract protected function getPommSession(): PommSession;

    /**
     * initArticleIdFetcher
     */
    protected function initArticleFetcher(array $skus): self
    {
        $this->article_ids = [];
        if ($skus === []) {
            return $this;
        }

        $results = $this->getPommSession()
            ->getModel(ArticleModel::class)
            ->fetchArticlesBySkus($skus);

        foreach ($results as $result) {
            $this->article_ids[$result->get('sku')] = $result;
        }

        return $this;
    }

    /**
     * fetchArticleId
     *
     * @return Article|null
     */
    protected function fetchArticle(string $sku)
    {
        return $this->article_ids[$sku] ?? null;
    }
}
