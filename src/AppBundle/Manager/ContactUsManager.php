<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager;

use SonVideo\Cms\FrontOffice\AppBundle\DataFormatter\StringFormatter;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\ContactMessage\AlreadyExistsException;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\ContactMessage\ElapsedTimeException;
use SonVideo\Synapps\Client\RpcClientService;

/**
 * Class ContactUsManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager
 */
class ContactUsManager
{
    public const CHOICES = [
        'need_technical_advice' => 39,
        'personalized_quote' => 40,
        'checkout_with_abroad_delivery' => 41,
        'track_my_order' => 'redirect_to_customer_page',
        'delivery_problem' => 'redirect_to_customer_page',
        'report_a_breakdown' => 'redirect_to_customer_page',
        'remark_or_suggestion_about_the_website' => 42,
        'becoming_reseller' => 8,
        'other_subject' => 11,
    ];

    public const HANDLED_ERRORS = [
        'ALREADY_EXISTS' => AlreadyExistsException::class,
        'ELAPSED_TIME' => ElapsedTimeException::class,
    ];

    protected RpcClientService $rpc_client_service;

    /**
     * ContactManager constructor.
     */
    public function __construct(RpcClientService $rpc_client_service)
    {
        $this->rpc_client_service = $rpc_client_service;
    }

    /**
     * submit
     *
     *
     * @throws \Exception
     */
    public function submit(string $choice, string $message, int $customer_id): bool
    {
        if (!isset(self::CHOICES[$choice])) {
            throw new \UnexpectedValueException(sprintf('Invalid supplied choice for contact message : "%s"', $choice));
        }

        $message = StringFormatter::transliterate($message);

        $response = $this->rpc_client_service->call('bridge', 'message:submit', [
            self::CHOICES[$choice],
            $message,
            $customer_id,
        ]);

        if (!isset($response['result'])) {
            throw new \Exception('RPC call fails for message:submit');
        }

        if ($response['result']['is_valid'] === false) {
            $this->createException($response['result']['error'] ?? 'none', $customer_id);
        }

        return true;
    }

    /**
     * createException
     *
     *
     * @throws \Exception
     */
    private function createException(string $reported_error, int $customer_id): void
    {
        if (!isset(self::HANDLED_ERRORS[$reported_error])) {
            throw new \Exception(
                sprintf(
                    'Error while saving the contact us message for customer: %d - "%s"',
                    $customer_id,
                    $reported_error,
                ),
            );
        }

        $class = self::HANDLED_ERRORS[$reported_error];
        throw new $class(sprintf('contact_us_message_%s', strtolower($reported_error)));
    }
}
