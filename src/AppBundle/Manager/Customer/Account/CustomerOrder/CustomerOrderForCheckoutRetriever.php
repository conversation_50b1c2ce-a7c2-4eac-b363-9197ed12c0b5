<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\CustomerOrder;

use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\ShipmentMethodModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrderModel;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CustomerOrderForCheckoutRetriever
{
    private PommSession $pomm_session;

    public function __construct(PommSession $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    public function retrieve(int $customer_order_id, $customer_id): array
    {
        $customer_order = $this->pomm_session
            ->getModel(CustomerOrderModel::class)
            ->findForCheckout($customer_order_id, $customer_id);

        if ($customer_order === null) {
            throw new NotFoundHttpException(
                $this->get('translator')->trans(
                    'order_not_exists',
                    ['%customer_order_id%' => $customer_order_id],
                    'order',
                ),
            );
        }

        $shipment_method = $this->pomm_session
            ->getModel(ShipmentMethodModel::class)
            ->fetchWithList([
                [
                    'shipment_method_id' => $customer_order['shipment_method_id'],
                    'cost' => $customer_order['shipping_price'],
                ],
            ])
            ->current();

        $customer_order = array_merge($customer_order, [
            'shipment_method' => $shipment_method->extract(),
            'summary' => $this->getCustomerOrderSummary($customer_order_id),
        ]);

        $customer_order['shipment_method']['extra_data'] = $customer_order['shipping_address']['extra_data'] ?? [];
        $customer_order['billing_address'] = $this->formatAddressForCheckout($customer_order['billing_address']);
        $customer_order['shipping_address'] = $this->formatAddressForCheckout($customer_order['shipping_address']);
        // remove null values in articles warranties
        $customer_order['articles'] = array_map(function (array $article) {
            $article['warranties'] = array_filter(
                $article['warranties'],
                fn($warranty): bool => $warranty['label'] !== null,
            );

            return $article;
        }, $customer_order['articles']);

        $customer_order['total_discount_tax_included'] = array_sum(
            array_column($customer_order['articles'], 'total_discount_amount'),
        );

        return $customer_order;
    }

    private function formatAddressForCheckout(array $address): array
    {
        $address = array_merge($address, [
            'firstname' => $address['first_name'],
            'lastname' => $address['last_name'],
            'title' => $address['civility'],
        ]);

        unset($address['first_name']);
        unset($address['last_name']);
        unset($address['civility']);

        return $address;
    }

    private function getCustomerOrderSummary(int $customer_order_id): array
    {
        $products = $this->pomm_session->getModel(CustomerOrderModel::class)->getCompleteProducts($customer_order_id);
        $i = 0;

        return array_map(
            fn($product): array => [ // Note: this returned structure is based on the "summary" in the checkout process v2
                'description' => sprintf('%s %s', $product['name'], $product['brand_name']),
                'quantity' => $product['quantity'],
                'type' => 'article.article',
                'sub_type' => null,
                'order_line_no' => ++$i,
                'id' => $product['article_id'],
                'article_name' => $product['name'],
                'selling_price' => $product['selling_price'],
                'brand_name' => $product['brand_name'],
                'category_name' => $product['category_name'],
            ],
            $products->extract(),
        );
    }
}
