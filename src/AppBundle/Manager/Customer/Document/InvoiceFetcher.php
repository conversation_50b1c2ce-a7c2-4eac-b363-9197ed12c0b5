<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Document;

use League\Flysystem\MountManager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\Invoice;

/**
 * Class InvoiceFetcher
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Document
 */
class InvoiceFetcher
{
    public const ROOT_PATH = 'sonvideopro.com/data/backoffice/factures';

    private MountManager $mount_manager;

    /**
     * InvoiceFetcher constructor.
     */
    public function __construct(MountManager $mount_manager)
    {
        $this->mount_manager = $mount_manager;
    }

    /**
     * exists
     *
     *
     */
    public function exists(Invoice $invoice): bool
    {
        $file_name = sprintf('%s.pdf', $invoice->get('invoice_id'));
        $created_at_next_month = strtotime($invoice->get('created_at') . ' + 1 month');
        $created_at = strtotime($invoice->get('created_at'));
        $year = date('Y', $created_at);
        $month = date('m', $created_at);
        $day = date('d', $created_at);

        if ($this->checkPathWith([$year, $month, $day, $file_name])) {
            return true;
        }

        if (
            $this->checkPathWith([
                date('Y', $created_at_next_month),
                date('m', $created_at_next_month),
                $day,
                $file_name,
            ])
        ) {
            return true;
        }

        if ($this->checkPathWith([$year, $file_name])) {
            return true;
        }

        // TODO Notify synapps about the document non existence,
        // eventually it may be created on the legacy server
        // http://gitlab.son-video.work/son-video/front-cms/issues/224

        return $this->checkPathWith([$year + 1, $file_name]);
    }

    /**
     * getContent
     *
     *
     * @return null|string
     */
    public function getContent(Invoice $invoice)
    {
        $file_name = sprintf('%s.pdf', $invoice->get('invoice_id'));
        $created_at_next_month = strtotime($invoice->get('created_at') . ' + 1 month');
        $created_at = strtotime($invoice->get('created_at'));
        $year = date('Y', $created_at);
        $month = date('m', $created_at);
        $day = date('d', $created_at);

        $content = $this->getPathWith([$year, $month, $day, $file_name]);
        if ($content !== null) {
            return $content;
        }

        $content = $this->getPathWith([
            date('Y', $created_at_next_month),
            date('m', $created_at_next_month),
            $day,
            $file_name,
        ]);
        if ($content !== null) {
            return $content;
        }

        $content = $this->getPathWith([$year, $file_name]);
        if ($content !== null) {
            return $content;
        }

        return $this->getPathWith([$year + 1, $file_name]);
    }

    /**
     * checkPathWith
     *
     *
     */
    protected function checkPathWith(array $path_segments): bool
    {
        array_unshift($path_segments, self::ROOT_PATH);

        return $this->mount_manager->getFilesystem('legacy_filesystem')->has(implode(DIRECTORY_SEPARATOR, $path_segments));
    }

    /**
     * getPathWith
     *
     *
     * @return null|string
     */
    protected function getPathWith(array $path_segments)
    {
        if (!$this->checkPathWith($path_segments)) {
            return null;
        }

        array_unshift($path_segments, self::ROOT_PATH);

        $stream = $this->mount_manager
            ->getFilesystem('legacy_filesystem')
            ->readStream(implode(DIRECTORY_SEPARATOR, $path_segments));

        $content = stream_get_contents($stream);
        fclose($stream);

        return $content;
    }
}
