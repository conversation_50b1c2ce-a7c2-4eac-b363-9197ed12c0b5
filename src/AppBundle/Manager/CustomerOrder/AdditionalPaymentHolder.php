<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder;

use PommProject\Foundation\ConvertedResultIterator;
use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethodModel;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

/**
 * Class AdditionalPaymentHolder
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class AdditionalPaymentHolder
{
    public const SESSION_ADDITIONAL_PAYMENT_KEY = 'customer_order_additional_payments';

    /**
     * @var array
     */
    private $payments = [];

    private SessionInterface $session;

    private PommSession $pomm_session;

    /**
     * AdditionalPaymentHandler constructor.
     */
    public function __construct(SessionInterface $session, PommSession $pomm_session)
    {
        $this->session = $session;
        $this->pomm_session = $pomm_session;

        $this->initPayments();
    }

    /**
     * initPayments
     */
    private function initPayments(): self
    {
        $this->payments = $this->session->has(static::SESSION_ADDITIONAL_PAYMENT_KEY)
            ? $this->session->get(static::SESSION_ADDITIONAL_PAYMENT_KEY)
            : [];

        return $this;
    }

    /**
     * savePayments
     */
    private function savePayments(): self
    {
        $this->session->set(static::SESSION_ADDITIONAL_PAYMENT_KEY, $this->payments);
        $this->session->save();

        return $this;
    }

    /**
     * clearPayments
     */
    public function clearPayments(array $ignored_payment_method = []): self
    {
        if ($ignored_payment_method === []) {
            $this->payments = [];
        } else {
            $this->payments = array_values(
                array_filter(
                    $this->payments,
                    fn($payment): bool => in_array($payment['payment_method_id'], $ignored_payment_method),
                ),
            );
        }

        $this->savePayments();

        return $this;
    }

    /**
     * addPayment
     */
    public function addPayment(
        int $customer_order_id,
        int $payment_method_id,
        float $price,
        array $extra_data = []
    ): self {
        $payment_method = $this->fetchOnePayment($payment_method_id);
        if ($payment_method instanceof PaymentMethod) {
            $description_i18n = $payment_method->get('description_i18n');
            $this->payments[] = [
                'customer_order_id' => $customer_order_id,
                'payment_method_id' => $payment_method_id,
                'price' => $price,
                'extra_data' => $extra_data,
                'description' => is_array($description_i18n) ? current($description_i18n) : '',
                'code' => $payment_method->get('code'),
            ];

            $this->savePayments();
        }

        return $this;
    }

    /**
     * getPayments
     */
    public function getPayments(): array
    {
        return $this->payments;
    }

    /**
     * getPaymentsTotalAmount
     */
    public function getPaymentsTotalAmount(): float
    {
        return array_reduce(
            $this->payments,
            fn(float $sum, $payment): float => $sum + ((float) ($payment['price'] ?? 0)),
            0,
        );
    }

    /**
     * fetchOnePayment
     *
     * @return PaymentMethod|null
     */
    protected function fetchOnePayment(int $payment_method_id)
    {
        return $this->pomm_session
            ->getModel(PaymentMethodModel::class)
            ->findByPK(['payment_method_id' => $payment_method_id]);
    }

    /**
     * fetchPaymentMethods
     *
     *
     * @return null|ConvertedResultIterator
     */
    public function fetchPaymentMethods(string $locale)
    {
        return $this->pomm_session
            ->getModel(PaymentMethodModel::class)
            ->fetchForPaymentMethodList($this->payments, $locale);
    }
}
