<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2015 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Catalog;

use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\CatalogException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\AccessoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\BrandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\MainProduct;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\MainProductModel;

/**
 * Class CatalogFetcher
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Catalog
 * <AUTHOR> <<EMAIL>>
 */
class CatalogFetcher
{
    public const SENNHEISER_CATALOG_NAME = 'sennheiser';
    public const LAMP_CATALOG_NAME = 'lamp';

    private PommSession $pomm_session;

    /**
     * CatalogFetcher constructor.
     */
    public function __construct(PommSession $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * getMainProduct
     *
     *
     * @throws CatalogException
     */
    public function getMainProduct(string $catalog_path, string $product_path): MainProduct
    {
        $product = $this->pomm_session->getModel(MainProductModel::class)->findByCatalog($product_path, $catalog_path);

        if (!$product instanceof MainProduct) {
            throw new CatalogException(
                sprintf('No product found for "%s" path in catalog "%s".', $product_path, $catalog_path),
            );
        }

        return $product;
    }

    /**
     * getMainProducts
     *
     * @return array<MainProduct>
     */
    public function getMainProducts(string $catalog_path): array
    {
        return $this->pomm_session
            ->getModel(MainProductModel::class)
            ->findAllByCatalog($catalog_path)
            ->extract();
    }

    /**
     * getMainProductsByBrand
     *
     *
     */
    public function getMainProductsByBrand(string $catalog_path, string $brand_path): array
    {
        return $this->pomm_session
            ->getModel(MainProductModel::class)
            ->findByCatalogAndBrand($catalog_path, $brand_path)
            ->extract();
    }

    /**
     * getAccessoriesByProduct
     *
     *
     */
    public function getAccessoriesByProduct(string $product_path, string $culture): array
    {
        return $this->pomm_session
            ->getModel(AccessoryModel::class)
            ->findByProduct($product_path, $culture)
            ->extract();
    }

    /**
     * getAccessoriesByBrandAndProduct
     *
     *
     */
    public function getAccessoriesByBrandAndProduct(string $brand_path, string $culture, $product_name = null): array
    {
        if ($product_name === null) {
            return $this->pomm_session
                ->getModel(AccessoryModel::class)
                ->findByBrand($brand_path, $culture)
                ->extract();
        }

        return $this->pomm_session
            ->getModel(AccessoryModel::class)
            ->findByBrandAndProduct($brand_path, $product_name, $culture)
            ->extract();
    }

    /**
     * getBrandsByCatalog
     *
     *
     */
    public function getBrandsByCatalog(string $catalog_name): array
    {
        return $this->pomm_session
            ->getModel(BrandModel::class)
            ->findByCatalog($catalog_name)
            ->extract();
    }
}
