<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Event;

use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Session as PommSession;
use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\Installation;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\InstallationModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\InstallationModelLayer;
use SonVideo\Synapps\Client\Manager\SynappsNotifier;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Class InstallationEdit
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Event
 * <AUTHOR> <<EMAIL>>
 */
class InstallationEdit
{
    public const INSTALLATION_EDIT_MESSAGE = 'installation.edit.fo-cms';

    private PommSession $pomm_session;

    private InstallationImageUploader $image_uploader;

    private SynappsNotifier $notifier;

    private LoggerInterface $logger;

    /**
     * InstallationCreator constructor.
     */
    public function __construct(
        PommSession $pomm_session,
        InstallationImageUploader $image_uploader,
        SynappsNotifier $notifier,
        LoggerInterface $logger
    ) {
        $this->pomm_session = $pomm_session;
        $this->notifier = $notifier;
        $this->logger = $logger;
        $this->image_uploader = $image_uploader;
    }

    /**
     * uploadAndAddMedia
     *
     * @return null|Installation
     * @throws ModelException
     */
    public function uploadAndAddMedia(Installation $installation, UploadedFile $file)
    {
        $media = $installation->getMediaObject();
        if ($media->count() >= InstallationCreator::MAX_PICTURES_BY_INSTALLATION) {
            throw new \LogicException(
                'Max number of pictures is reached.',
                InstallationCreator::ERROR_MAX_PICTURES_IS_REACHED,
            );
        }

        $this->image_uploader->addImageToUpload($file);
        $images = $this->image_uploader->upload([
            'customer_id' => $installation->get('customer_id'),
            'installation_id' => $installation->get('installation_id'),
        ]);

        $this->logger->info('installation_creator.create.before_update_medias', [
            'customer_id' => $installation->get('customer_id'),
            'installation' => $installation->extract(),
            'images' => $images,
        ]);

        $media->addMedia($images[0], $images[0]);

        return $this->getInstallationModelLayer()->updateMedia(
            $installation->get('installation_id'),
            $media->normalize(),
        );
    }

    /**
     * save
     *
     * @return Installation
     */
    public function save(Installation $installation, array $data)
    {
        $this->logger->info('installation_edit.save.begin', ['installation' => $installation->extract()]);
        $keys = ['budget', 'surface_area_m2', 'comment', 'description'];
        foreach ($keys as $key) {
            if (array_key_exists($key, $data)) {
                $installation->set($key, $data[$key]);
            }
        }

        $this->removeMediaByImageUrl($installation, $data['images_to_remove'] ?? '');

        $this->getInstallationModel()->updateOne($installation, [...$keys, 'medias']);
        $this->logger->info('installation_edit.save.end', ['installation' => $installation->extract()]);
        $this->notifier->notify(static::INSTALLATION_EDIT_MESSAGE, $installation->extract());

        return $installation;
    }

    /**
     * removeMediaByThumbnails
     */
    protected function removeMediaByImageUrl(Installation &$installation, string $images_to_remove): self
    {
        $media = $installation->getMediaObject();
        $removed_media = $media->removeMediaByImageUrl(array_filter(explode(',', $images_to_remove)));

        foreach ($removed_media as $one_media) {
            try {
                $this->image_uploader->remove($one_media['image_url']);
                if ($one_media['thumbnail_url'] !== $one_media['image_url']) {
                    $this->image_uploader->remove($one_media['thumbnail_url']);
                }
            } catch (\Exception $e) {
                $this->logger->error('Unable to remove pictures from media filesystem.', ['media' => $one_media]);
            }
        }

        $installation->set('medias', $media->normalize());

        return $this;
    }

    /**
     * getInstallationModel
     */
    protected function getInstallationModel(): InstallationModel
    {
        return $this->pomm_session->getModel(InstallationModel::class);
    }

    /**
     * getInstallationModelLayer
     */
    private function getInstallationModelLayer(): InstallationModelLayer
    {
        return $this->pomm_session->getModelLayer(InstallationModelLayer::class);
    }
}
