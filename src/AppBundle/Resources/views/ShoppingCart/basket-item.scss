.selected-data {
  display: flex;
  flex-direction: column;
  gap: $space_16;
  border: solid 1px $color_grey_default;
  border-radius: $radius_16;
  padding: $space_16;
}

.data-prices-quantity {
  display: flex;
  flex-direction: column;
  gap: $space_16;
  padding: $space_8;
  @include media_min($media_md) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 $space_16;
  }
  .data {
    display: flex;
    flex-direction: row;
    gap: $space_16;
    @include media_min($media_md) {
      width: 70.8%;
      gap: $space_32;
    }
    .picture {
      min-height: $height_50;
      min-width: $width_50;
      display: flex;
      justify-content: center;
      align-items: center;
      align-content: center;
      text-align: center;
      cursor: pointer;
      &.small {
        @include media_min($media_md) {
          min-height: $height_50;
          min-width: $width_50;
        }
        img {
          max-height: $height_50;
          max-width: $width_50;
          @include media_min($media_md) {
            max-height: $height_50;
            max-width: $width_50;
          }
        }
      }
      @include media_min($media_md) {
        min-height: $height_100;
        min-width: $width_100;
      }
      img {
        max-height: $height_50;
        max-width: $width_50;
        @include media_min($media_md) {
          max-height: $height_100;
          max-width: $width_100;
          height: auto;
        }
      }
    }
    .infos {
      display: flex;
      flex-direction: column;
      gap: $space_2;
      font-size: $font_size_15;
      line-height: $height_24;
      font-weight: $font_bold;
      width: 100%;
      .basket-item-title {
        color: $color_dark_default;
        cursor: pointer;
      }
      .stock {
        color: $color_grey_typo;
        font-weight: $font_semi_bold;
        &.available {
          font-weight: $font_bold;
          color: $color_success;
        }
        &.available-with-delay {
          font-weight: $font_bold;
          color: $color_warning;
        }
        &.unbasketable {
          font-weight: $font_normal;
          color: $color_error;
        }
      }
      .other {
        font-weight: $font_normal;
      }
      .promo-offers {
        display: flex;
        flex-direction: column;
        gap: $space_8;
        padding-top: $space_6;
        @include media_min($media_desktop) {
          flex-direction: row;
          flex-wrap: wrap;
        }
        .promo-item {
          display: flex;
          justify-content: center;
          font-size: $font_size_13;
          line-height: $height_20;
          padding: $space_8 $space_16;
          border: solid 1px $color_grey_default;
          border-radius: $radius_32;
          width: fit-content;
        }
      }
      .promo-item {
        display: flex;
        justify-content: center;
        font-size: $font_size_13;
        line-height: $height_20;
        padding: $space_8 $space_16;
        border: solid 1px $color_grey_default;
        border-radius: $radius_32;
      }
    }
  }
  .prices-quantity {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    @include media_min($media_md) {
      width: fit-content;
      flex-direction: column;
      justify-content: flex-start;
      gap: $space_16;
    }
    .prices {
      display: flex;
      flex-direction: column;
      gap: $space_8;
      justify-content: center;
      &.hide-desktop {
        @include media_min($media_md) {
          display: none;
        }
      }
      .promo {
        display: flex;
        flex-direction: row;
        align-items: center;
        align-content: center;
        text-align: center;
        gap: $space_10;
        font-size: $font_size_13;
        line-height: $height_20;
        @include media_min($media_md) {
          font-size: $font_size_15;
          line-height: $height_24;
        }
        .percent {
          background-color: $color_blue_web_safe;
          color: $color_white;
          font-weight: $font_bold;
          padding: $space_4 $space_10;
        }
        .old {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: $space_4;
          cursor: pointer;
          font-weight: $font_semi_bold;
          text-decoration: line-through;
        }
      }
      .final {
        font-weight: $font_bold;
        font-size: $font_size_15;
        line-height: $height_24;
        color: $color_dark_default;
        @include media_min($media_md) {
          font-size: $font_size_18;
          line-height: $height_28;
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
        }
        &.free {
          color: $color_success;
        }
      }
    }
    .quantity {
      display: flex;
      flex-direction: column;
      gap: $space_16;
      justify-content: flex-end;
      align-items: flex-end;
      align-content: flex-end;
      text-align: right;
      .select {
        width: 70px;
        float: right;
      }
      .delete {
        font-size: $font_size_13;
        line-height: $height_20;
        color: $color_grey_typo;
        font-weight: $font_normal;
        text-decoration: underline;
        cursor: pointer;
        @include media_min($media_md) {
          font-size: $font_size_15;
          line-height: $height_24;
        }
      }
    }
  }
}