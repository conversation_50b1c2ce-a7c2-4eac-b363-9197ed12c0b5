{% if url is defined and url != '' and url != 'http://www.son-video.com' and url != 'https://www.son-video.com' %}
    {% set small = 'false' %}
    {% if format is defined and format == 'small' %}{% set small = 'true' %}{% endif %}
    {% if list is not defined or 'facebook' in list %}
        <a class="btn btn-facebook btn-social{% if small == 'true' %} btn-icon{% endif %}" target="_blank" rel="nofollow" href="https://www.facebook.com/sharer/sharer.php?u={{ url | url_encode }}">
            <i class="icon icon-facebook icon-lg"></i> <span>Facebook</span>
        </a>
    {% endif %}
    {% if list is not defined or 'twitter' in list %}
        <a class="btn btn-twitter btn-social{% if small == 'true' %} btn-icon{% endif %}" target="_blank" rel="nofollow" href="http://twitter.com/intent/tweet/?url={{ url | url_encode }}&amp;text={{ (titre ~ ' ' ~ ('on_son_video_com'|trans)) | url_encode }}">
            <i class="icon icon-twitter icon-lg"></i> <span>Twitter</span>
        </a>
    {% endif %}
    {% if list is not defined or 'googleplus' in list %}
        <a class="btn btn-google btn-social{% if small == 'true' %} btn-icon{% endif %}" target="_blank" rel="nofollow" href="https://plus.google.com/share?url={{ url | url_encode }}&amp;hl={{ app.request.getLocale() }}">
            <i class="icon icon-google-plus icon-lg"></i> <span>Google+</span>
        </a>
    {% endif %}
    {% if list is not defined or 'pinterest' in list %}
        <a class="btn btn-pinterest btn-social{% if small == 'true' %} btn-icon{% endif %}" target="_blank" rel="nofollow" href="http://pinterest.com/pin/create/button/?url={{ url | url_encode }}&amp;media={{ media }}&amp;description={{ (titre ~ ' ' ~ ('on_son_video_com'|trans)) | url_encode }}">
            <i class="icon icon-pinterest icon-lg"></i> <span>Pinterest</span>
        </a>
    {% endif %}
{% endif %}
