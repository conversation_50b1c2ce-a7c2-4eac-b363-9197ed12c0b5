{% block stylesheets %}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('component/input')) }}
{% endblock %}

{% set isForm = isForm ?? false %}
{% set required = required ?? 0 %}
{% set disabled = disabled ?? 0 %}
{% set default = default ?? '' %}
{% set placeholder = placeholder ?? '' %}
{% set label = label ?? '' %}
{% set type = type ?? 'text' %}

<section class='input-type'>
    {% if isForm %}
        {% set errors = form_errors(form) %}
        
        <div class="{{ disabled ? 'disabled input-label' :'input-label' }}">
            {{ form_label(form, label) }}
        </div>
        {{ form_widget(form) }}
        {% if errors is not empty %}
            <div class="helper">
                <div class='error'>
                    {{ errors|striptags|raw }}
                </div>
            </div>
        {% endif %}
    {% else %}
        <div class="{{ disabled ? 'disabled input-label' :'input-label' }}">
            {{ label }}
            {% if required %}
                <span class='is-required'>&nbsp;*</span>
            {% endif %}
        </div>
        <input
            name='{{ name }}'
            value="{{ default }}"
            type="{{ type }}"
            placeholder="{{ placeholder }}"
            data-disabled='{{ disabled }}'
            data-required="{{ required }}"
            aria-disabled="{{ disabled }}"
            aria-label="{{ label }}"
        >
        <div class='helper'>
            {% block helper %}
            {% endblock %}
        </div>
    {% endif %}
</section>

{% block javascripts %}
    {{ encore_entry_script_tags('component/input') }}
{% endblock %}