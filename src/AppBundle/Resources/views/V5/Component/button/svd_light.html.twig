{% block stylesheets %}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('component/button')) }}
{% endblock %}

{% set outline = outline ?? false %}
{% set link = link ?? false %}
{% set disabled = disabled ?? 0 %}
{% set is_submit = is_submit ?? false %}
{% set small = small ?? false %}
{% set id = id ?? random() %}
{% set class = class ?? '' %}
{% set icon = icon ?? '' %}
{% set iconColor = iconColor ?? '' %}
{% set href = href ?? '' %}

<section class='btn-section'>
    <button
        class='{{ class }} btn light {{ outline ? 'outline' : '' }} {{ link ? 'link' : '' }} {{ disabled ? 'disabled' : '' }} {{ small ? 'small' : '' }}'
        type='{{ is_submit ? 'submit' : 'button' }}'
        id='{{ id }}'
        data-disabled='{{ disabled }}'
        data-href='{{ href }}'
        aria-disabled="{{ disabled }}"
        aria-label="{{ label }}">
        <span class="btn-label">
            {% if icon != '' %}
                {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                    icon: icon,
                    size: 18,
                    color: iconColor
                } %}
            {% endif %}
            {{ label }}
        </span>
    </button>
</section>

{% block javascripts %}
    {{ encore_entry_script_tags('component/button') }}
{% endblock %}