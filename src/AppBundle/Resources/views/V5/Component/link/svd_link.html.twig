{% block stylesheets %}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('component/link')) }}
{% endblock %}

{% set small = small ?? false %}
{% set big = big ?? false %}
{% set discreet = discreet ?? false %}
{% set hyperDiscreet = hyperDiscreet ?? false %}
{% set back = back ?? false %}

<a
        aria-label="{{ label }}"
        {% if back %}
            href="#"
            onclick="event.preventDefault(); window.history.back();"
        {% else %}
            href="{{ href is not empty ? href : '#' }}"
        {% endif %}
        class="link {{ small ? 'small' : '' }} {{ big ? 'big' : '' }} {{ discreet ? 'discreet' : '' }} {{ hyperDiscreet ? 'hyper-discreet' : '' }}">
    {{ label }}
</a>

{% block javascripts %}
    {{ encore_entry_script_tags('component/link') }}
{% endblock %}