// *** STYLES *** //
require('./icon.scss')

// *** JS *** //
import { asset } from '@/shared/filters'
import { getFilterToApply } from '@/common/transform_img_color'
import { getIconUrl } from '@/shared/Icons'

window.addEventListener('load', (event) => {
    const icons = document.getElementsByClassName('svd-icon-v5')

    for (let i = 0; i < icons.length; i++) {
        const color = icons[i].getAttribute('data-color')
        const icon = icons[i].getAttribute('data-icon')
        const keepColor = icons[i].getAttribute('data-keepColor')
        if (keepColor) {
            icons[i].style.filter = 'unset'
        } else {
            icons[i].style.filter = getFilterToApply(color)
        }
        const imageSrc = asset(getIconUrl(icon), 'static_images')
        icons[i].style.backgroundImage = `url(${imageSrc})`
    }
})
