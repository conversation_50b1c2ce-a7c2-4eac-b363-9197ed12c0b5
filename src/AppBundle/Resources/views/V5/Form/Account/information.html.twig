{% set genders = [
    {value: 'Mr', label: '<PERSON><PERSON>'},
    {value: 'Ms', label: '<PERSON><PERSON>'}
] %}
{% block content %}
    <div class="account-form" data-context='account_informations'>
        <form method="POST" class="account" action="{{ path('customer_account_information_save') }}">
            {% include 'AppBundle:V5/Component/input:svd_text.html.twig' with {
                label: 'Votre adresse email',
                name: "account_information_form[email]",
                disabled: true,
                default: app.user.email
            } %}
            {% include 'AppBundle:V5/Component/message:svd_message.html.twig' %}
            {{ form_row(form._token) }}
            {% include 'AppBundle:V5/Component/input:svd_select.html.twig' with {
                label: form.title.vars.label | trans({}, 'account'),
                name: "account_information_form[title]",
                required: true,
                default: form.title.vars.value,
                options: genders
            } %}
            {% include 'AppBundle:V5/Component/input:svd_text.html.twig' with {
                label: form.firstname.vars.label | trans({}, 'account'),
                name: "account_information_form[firstname]",
                required: true,
                default: form.firstname.vars.value
            } %}
            {% include 'AppBundle:V5/Component/input:svd_text.html.twig' with {
                label: form.lastname.vars.label | trans({}, 'account'),
                name: "account_information_form[lastname]",
                required: true,
                default: form.lastname.vars.value
            } %}
            {% include 'AppBundle:V5/Component/input:svd_text.html.twig' with {
                label: form.cellphone.vars.label | trans({}, 'account'),
                name: "account_information_form[cellphone]",
                required: true,
                type: 'tel',
                default: form.cellphone.vars.value
            } %}
            {% include 'AppBundle:V5/Component/input:svd_text.html.twig' with {
                label: form.phone.vars.label | trans({}, 'account'),
                name: "account_information_form[phone]",
                type: 'tel',
                default: form.phone.vars.value
            } %}
            {% include 'AppBundle:V5/Component/input:svd_text.html.twig' with {
                label: form.birthday.vars.label | trans({}, 'account'),
                name: "account_information_form[birthday]",
                type: 'date',
                default: form.birthday.vars.value
            } %}
            {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                label: "Sauvegarder",
                is_submit: true
            } %}
        </form>
    </div>
{% endblock %}