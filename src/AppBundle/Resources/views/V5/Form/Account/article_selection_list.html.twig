{% block content %}
    <div class="items-basket">
        {% for selection in selections %}
            <div class="item-basket">
                <div class="data" data-context="item-data-name">
                    {% include 'AppBundle:V5/Component/link:svd_link.html.twig' with {
                        label: selection.name,
                        href: path('customer_account_article_selection_view', {'slug': selection.slug}),
                        big: true
                    } %}
                    <div class="date"><PERSON><PERSON><PERSON> le {{ selection.created_at | localizeddate('long', 'none', app.request.locale) }}</div>
                </div>
                <div class="data right">
                    <div class="price">{{ selection.nb_articles }} {{ selection.nb_articles > 1 ? 'articles': 'article' }}</div>

                    <form class="delete" action="{{ path('selection_delete') }}" method="POST">
                        <button class="DelPdt" name="slug" value="{{ selection.slug }}">
                            Supprimer
                        </button>
                    </form>
                </div>
            </div>
        {% endfor %}
    </div>
{% endblock %}