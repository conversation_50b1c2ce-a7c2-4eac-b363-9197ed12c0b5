<div id="order">
    <customer-order
            customer-order-id="{{ order.customer_order_id }}"
            messages="{{ order.messages|json_encode|raw|e }}"
    ></customer-order>
</div>

<form method="POST"
      id="commande_message_formulaire"
      action="{{ path('customer_account_order_detail_post', { 'customer_order_id': order.customer_order_id }) }}">

    {{ form_row(form._token) }}

    {{ form_widget(form.content, {'attr': {'rows': 5, 'maxlength': 1200, 'placeholder': 'enter_your_message'|trans({}, "order") }}) }}
    <div class="form-footer">
        <span><span id="counter">1200</span> caractères max</span>
        <div class="actions">
            {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                label: "Envoyer",
                is_submit: true
            } %}
        </div>
    </div>
</form>