@import '../../../../../scss/style';
$_width_352: 352px;
.confirm-password-section {
    display: flex;
    flex-direction: row;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    @include media_min($media_xs) {
        padding-top: $space_32;
        padding-bottom: $space_32;
    }
    .content {
        width: 100%;
        background-color: $color_white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: $space_32;
        padding: $space_32 $space_16;
        @include media_min($media_xs) {
            width: $_width_352;
            border: solid 1px $color_grey_default;
            border-radius: $radius_10;
            box-sizing: unset;
            padding: $space_32;
        }
        .explain {
            line-height: $height_24;
            font-size: $font_size_15;
            color: $color_grey_typo;
        }
    }
}
