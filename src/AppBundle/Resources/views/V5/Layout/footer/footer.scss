@import '../../../../scss/style';

$_item_min_width_sm: 136px;
$_item_min_width_other: 192px;
$_width_less_32: calc(100% - 32px);
$_with_172: 172.5px;
$_width_400: 400px;
$_width_937: 937px;
$_width_923: 923px;
$_width_323: 323px;

#footer {
    @include mixin_reset_style;

    background-color: $color_dark_medium;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: $color_white;
    .reassurance {
        background-color: $color_dark_default;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: flex-start;
        align-content: center;
        padding: $space_16;
        overflow: -moz-scrollbars-none;
        -ms-overflow-style: none;
        ::-webkit-scrollbar {
            width: 0 !important;
        }
        @include media_min($media_md) {
            padding-top: $space_32;
            padding-bottom: $space_32;
        }
        .global-div {
            width: 100%;
            max-width: $width_1260;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: flex-start;
            align-content: center;
            flex-wrap: nowrap;
            overflow-x: hidden;
            cursor: grab;
            &:active {
                cursor: grabbing;
            }
            @include media_min($media_desktop) {
                justify-content: flex-start;
                align-items: flex-start;
                cursor: default;
            }
            @media (pointer: coarse) {
                overflow-x: auto;
                #reassurance {
                    position: unset;
                }
            }
            #reassurance {
                display: flex;
                flex-direction: row;
                position: relative;
                gap: $space_16;
                @include media_min($media_lg) {
                    justify-content: space-between;
                    width: 100%;
                }

                .item {
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: center;
                    align-content: center;
                    text-align: center;
                    min-width: calc(50vw - 32px);
                    gap: $space_8;
                    -webkit-user-select: none; /* Safari */
                    -ms-user-select: none; /* IE 10 and IE 11 */
                    user-select: none; /* Standard syntax */
                    @include media_min($media_sm) {
                        min-width: calc(33.33vw - 21.33px);
                    }
                    @include media_min($media_md) {
                        min-width: calc(25vw - 20px);
                    }
                    @include media_min($media_lg) {
                        min-width: calc(16.66vw - 18.66px);
                        cursor: auto;
                    }
                    @include media_min($media_desktop) {
                        min-width: 189px;
                    }
                    span {
                        font-weight: $font_semi_bold;
                        line-height: $space_18;
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }
    .features {
        display: flex;
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;

        .secondary-border {
            @include media_min($media_md) {
                border-top: solid 1px $color_dark_light;
                margin-right: $space_16;
                margin-left: $space_16;
                width: $_width_less_32;
            }
        }

        .secondary-border-2 {
            @include media_min($media_lg) {
                border-top: solid 1px $color_dark_light;
                margin-right: $space_16;
                margin-left: $space_16;
                width: $_width_less_32;
            }
        }

        @include media_min($media_desktop) {
            flex-direction: column;
            align-items: center;
        }
        div {
            display: flex;
            width: 100%;
            max-width: $width_1260;
            &.intern {
                display: flex;
                flex-direction: column;
                text-transform: uppercase;
                @include media_min($media_sm) {
                    flex-direction: row;
                }
            }
            &.primary {
                display: flex;
                .content .title {
                    @include mixin_underline_transition;
                }
                :hover .content .title {
                    @include mixin_underline;
                }

                padding-top: $space_16;
                padding-left: $space_16;
                padding-right: $space_16;
                @include media_min($media_sm) {
                    padding-bottom: $space_16;
                    gap: $space_16;
                }
                @include media_min($media_md) {
                    padding-top: $space_32;
                    padding-bottom: $space_32;
                    gap: $space_32;
                }
                @include media_min($media_lg) {
                    gap: $space_16;
                }
                @include media_min($media_desktop) {
                    gap: $space_64;
                    padding-left: unset;
                    padding-right: unset;
                }
                .item {
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: center;
                    align-content: center;
                    flex-wrap: nowrap;
                    font-weight: $font_semi_bold;
                    width: 100%;
                    cursor: pointer;
                    gap: $space_8;
                    .picture {
                        width: $width_48;
                        height: $height_48;
                    }
                    @include media_min($media_md) {
                        .picture {
                            width: $width_80;
                            height: $height_80;
                        }
                    }
                    @include media_min($media_md) {
                        flex-direction: column;
                        width: 33%;
                    }
                    @include media_min($media_lg) {
                        align-items: flex-start;
                        align-content: flex-start;
                        flex-direction: row;
                        width: 100%;
                    }
                    .content {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;
                        align-content: center;
                        color: $color_white;
                        cursor: pointer;
                        @include media_min($media_md) {
                            align-items: center;
                        }
                        @include media_min($media_lg) {
                            align-items: flex-start;
                            align-content: flex-start;
                            gap: $space_8;
                            padding-top: $space_6;
                            padding-bottom: $space_6;
                        }
                        .title {
                            font-weight: $font_bold;
                        }
                        .description {
                            display: none;
                            line-height: $height_20;
                            transition: $transition_all_02;
                            @include media_min($media_lg) {
                                display: flex;
                                font-weight: $font_normal;
                                color: $color_grey_typo_light;
                                text-transform: none;
                            }
                        }
                    }
                }
            }
            &.secondary {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                align-content: center;
                padding-left: $space_16;
                padding-right: $space_16;
                @include media_min($media_sm) {
                    flex-direction: row;
                    gap: $space_16;
                }
                @include media_min($media_md) {
                    padding-top: $space_16;
                }
                @include media_min($media_lg) {
                    min-width: calc(100% - calc(100% / 3) - 37px);
                    padding-bottom: $space_16;
                    padding-right: unset;
                }
                @include media_min($media_desktop) {
                    min-width: 937px;
                    padding-left: unset;
                    padding-right: unset;
                    gap: $space_32;
                }
                .item {
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: center;
                    align-content: center;
                    flex-wrap: nowrap;
                    font-weight: $font_bold;
                    width: 100%;
                    cursor: pointer;
                    gap: $space_8;
                    .picture {
                        width: $width_48;
                        height: $height_48;
                    }
                    .content {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;
                        align-content: center;
                        width: 100%;
                        color: $color_white;
                        cursor: pointer;
                    }
                    .content {
                        @include mixin_underline_transition;
                    }
                    &:hover .content {
                        @include mixin_underline;
                    }
                }
            }
            &.links {
                padding-left: $space_16;
                padding-right: $space_16;
                @include media_min($media_lg) {
                    padding-left: unset;
                }
                .link-container {
                    padding-bottom: $space_14;
                    padding-top: $space_14;
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: center;
                    align-content: center;
                    flex-wrap: wrap;
                    border-top: solid 1px $color_dark_light;
                    border-bottom: solid 1px $color_dark_light;
                    gap: $space_4;
                    @include media_min($media_lg) {
                        border-top: 0;
                        justify-content: flex-start;
                        border-bottom: unset;
                    }
                    @include media_min($media_desktop) {
                        padding-bottom: $space_18;
                        padding-top: $space_18;
                    }
                    .item {
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                        align-content: center;
                        text-align: center;
                        width: $height_min_clickable;
                        height: $height_min_clickable;
                        cursor: pointer;
                        transition: $transition_all_02;
                        &:hover {
                            .background-container {
                                background-color: $color_white;
                                border-color: $color_white;
                            }
                            & .background {
                                filter: invert(1);
                            }
                            span {
                                border: unset;
                            }
                        }
                        .background-container {
                            width: $width_40;
                            height: $height_40;
                            border: solid 2px $color_dark_light;
                            border-radius: $radius_32;
                            transition: $transition_all_02;
                            .background {
                                background-position: center;
                                transition: $transition_all_02;
                            }
                        }
                    }
                }
            }
        }
        .secondary-and-links {
            display: flex;
            flex-direction: column;
            gap: $space_16;
            @include media_min($media_lg) {
                flex-direction: row;
                justify-content: flex-start;
                gap: $space_32;
            }
        }
    }
    .payment {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        align-content: center;
        font-weight: $font_bold;
        @include media_min($media_desktop) {
            flex-direction: row;
            align-items: flex-start;
            align-content: flex-start;
        }
        .global-div {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            max-width: $width_1260;
            padding: $space_16;
            @include media_min($media_md) {
                padding-bottom: $space_32;
            }
            @include media_min($media_desktop) {
                min-width: 937px;
                max-width: 937px;
                padding-right: unset;
                padding-left: unset;
                gap: $space_32;
            }
            .internal_link {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                width: 100%;
                opacity: 1;
                .category {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    align-content: center;
                    min-height: $height_min_clickable;
                    @include media_min($media_md) {
                        align-items: flex-start;
                        align-content: flex-start;
                        height: auto;
                        gap: $space_8;
                    }
                    .title {
                        text-transform: uppercase;
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        align-content: center;
                        width: 100%;
                        line-height: $height_20;
                        cursor: pointer;
                        height: $height_min_clickable;
                        .arrow {
                            transition: $transition_all_02;
                        }
                        @include media_min($media_md) {
                            align-items: flex-start;
                            cursor: auto;
                            height: 100%;
                            margin-top: $space_16;
                        }
                    }
                    .items {
                        opacity: 0;
                        display: flex;
                        visibility: hidden;
                        position: relative;
                        max-height: 0;
                        transition: $transition_all_02;
                        flex-direction: column;
                        font-weight: $font_normal;
                        color: $color_grey_typo_light;
                        text-transform: none;
                        gap: $space_4;
                        @include media_min($media_md) {
                            padding-bottom: $space_8;
                        }
                        div {
                            height: $height_18;
                            cursor: pointer;
                            a {
                                color: $color_grey_typo_light;
                                @include mixin_underline_transition;
                                &:hover {
                                    color: $color_white;
                                    @include mixin_underline_thin;
                                }
                            }
                        }
                        & .desktop {
                            visibility: hidden;
                            max-height: 0;
                            position: relative;
                            opacity: 0;
                        }
                        @include media_min($media_md) {
                            visibility: visible;
                            max-height: 100%;
                            position: static;
                            opacity: 1;
                            padding-bottom: unset;
                        }
                    }
                }
            }
            @include media_min($media_md) {
                flex-direction: row;
                flex-wrap: nowrap;
                justify-content: center;
                .category {
                    width: 100%;
                    .title .arrow {
                        display: none;
                    }
                }
            }
        }
        .newsletter {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            align-content: center;
            width: 100%;
            padding-left: $space_16;
            padding-right: $space_16;
            @include media_min($media_desktop) {
                max-width: $_width_323;
                min-width: $_width_323;
                justify-content: flex-start;
                align-items: flex-start;
                align-content: flex-start;
                padding-left: $space_32;
                padding-right: unset;
            }
            .newsletter-form {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                align-content: center;
                text-align: center;
                border-top: solid 1px $color_dark_light;
                border-bottom: solid 1px $color_dark_light;
                width: 100%;
                max-width: $width_1260;
                font-weight: $font_normal;
                padding-top: $space_16;
                padding-bottom: $space_16;
                gap: $space_8;
                @include media_min($media_md) {
                    padding-bottom: $space_32;
                    padding-top: $space_32;
                }
                @include media_min($media_desktop) {
                    border: unset;
                    justify-content: flex-start;
                    align-items: flex-start;
                    align-content: flex-start;
                    text-align: left;
                    gap: $space_14;
                }
                .title {
                    font-weight: $font_bold;
                    text-transform: uppercase;
                    max-width: $_width_400;
                    width: 100%;
                    line-height: $height_20;
                    order: 1;
                }
                .explain {
                    color: $color_grey_typo_light;
                    max-width: $_width_400;
                    line-height: $height_20;
                    width: 100%;
                    order: 2;
                    @include media_min($media_desktop) {
                        order: 3;
                    }
                }
                .input {
                    padding-top: $space_4;
                    min-height: $height_48;
                    max-width: $_width_400;
                    width: 100%;
                    order: 3;
                    @include media_min($media_desktop) {
                        order: 2;
                    }
                }
            }
        }
    }
    .payment-method {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        align-content: center;
        width: 100%;
        .link {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            align-content: center;
            padding: $space_11 $space_16;
            width: 100%;
            max-width: $width_1260;
            @include media_min($media_desktop) {
                border-top: solid 1px $color_dark_light;
                justify-content: flex-start;
                padding-left: unset;
                padding-right: unset;
                &.tunnel {
                    border-top: unset;
                }
            }
            .secure-link {
                min-height: $height_min_clickable;
                height: auto;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                width: 100%;
                gap: $space_8;
                padding-top: 5px;
                padding-bottom: 5px;
                flex-wrap: wrap;
                @include media_min($media_sm) {
                    gap: $space_16;
                }
                @include media_min($media_desktop) {
                    justify-content: flex-start;
                }
            }
        }
    }
    .no_wrap {
        white-space: nowrap;
    }

    .background-card {
        @include mixin_background_image('uiV5/64x64/card.svg', $width_64, $height_64);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/card.svg', $width_80, $height_80);
        }
    }
    .background-box {
        @include mixin_background_image('uiV5/64x64/box.svg', $width_64, $height_64);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/box.svg', $width_80, $height_80);
        }
    }
    .background-shield {
        @include mixin_background_image('uiV5/64x64/shield.svg', $width_64, $height_64);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/shield.svg', $width_80, $height_80);
        }
    }
    .background-truck {
        @include mixin_background_image('uiV5/64x64/truck.svg', $width_64, $height_64);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/truck.svg', $width_80, $height_80);
        }
    }
    .background-globe {
        @include mixin_background_image('uiV5/64x64/globe.svg', $width_64, $height_64);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/globe.svg', $width_80, $height_80);
        }
    }
    .background-speakers {
        @include mixin_background_image('uiV5/64x64/speakers.svg', $width_64, $height_64);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/speakers.svg', $width_80, $height_80);
        }
    }
    .background-mobile-comment {
        @include mixin_background_image('uiV5/48x48/mobile-comment.svg', $width_48, $height_48);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/mobile-comment.svg', $width_80, $height_80);
        }
    }
    .background-france {
        @include mixin_background_image('uiV5/48x48/france.svg', $width_48, $height_48);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/france.svg', $width_80, $height_80);
        }
    }
    .background-pen {
        @include mixin_background_image('uiV5/48x48/pen.svg', $width_48, $height_48);
        @include media_min($media_md) {
            @include mixin_background_image('uiV5/80x80/pen.svg', $width_80, $height_80);
        }
    }
    .background-book-open {
        @include mixin_background_image('uiV5/48x48/book-open.svg', $width_48, $height_48);
    }
    .background-videos {
        @include mixin_background_image('uiV5/48x48/videos.svg', $width_48, $height_48);
    }
    .background-screwdriver-wrench {
        @include mixin_background_image('uiV5/48x48/screwdriver-wrench.svg', $width_48, $height_48);
    }
    .background-visa {
        @include mixin_background_image('paiement/visa.svg', $width_34, $height_34);
    }
    .background-mastercard {
        @include mixin_background_image('paiement/mastercard.svg', $width_34, $height_34);
    }
    .background-cb {
        @include mixin_background_image('paiement/cb.svg', $width_34, $height_34);
    }
    .background-paypal {
        @include mixin_background_image('paiement/paypal.svg', $width_34, $height_34);
    }
    .background-amex {
        @include mixin_background_image('paiement/amex.svg', $width_34, $height_34);
    }
    .background-nx {
        @include mixin_background_image('paiement/nx.svg', $width_34, $height_34);
    }
    .background-facebook {
        @include mixin_background_image('uiV5/reseaux/facebook.svg', $width_20, $height_20);
    }
    .background-twitter {
        @include mixin_background_image('uiV5/reseaux/twitter.svg', $width_20, $height_20);
    }
    .background-youtube {
        @include mixin_background_image('uiV5/reseaux/youtube.svg', $width_20, $height_20);
    }
    .background-instagram {
        @include mixin_background_image('uiV5/reseaux/instagram.svg', $width_20, $height_20);
    }
    .background-tiktok {
        @include mixin_background_image('uiV5/reseaux/tiktok.svg', $width_20, $height_20);
    }
    .background-capital-2024 {
        @include mixin_background_image('uiV5/guarantee/capital-2024.svg', 220px, 80px);
    }
}