{% import 'AppBundle:Common:layout_macro.html.twig' as layout_macro %}

{% spaceless %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=0">
    <meta name="language" content="fr,fr-be,fr-lu,fr-ch">
    <meta name="description" content="{% trans from "touch_screen" %}description{% endtrans %}">
    <meta name="author" content="Son-Vidéo.com">
    <meta name="robots" content="index, follow">

    <title>{% trans from "touch_screen" %}title{% endtrans %}</title>

    <link rel="stylesheet" type="text/css" media="screen,print" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.0.3/css/bootstrap.min.css"/>
    {{ encore_entry_link_tags('globals') }}
    <link rel="stylesheet" type="text/css" media="screen,print" href="{{ asset('v3/modules/vitrineTactile/vitrineTactile.css', 'static_css') }}"/>
    <link rel="canonical" href="{{ base_url ~ canonical_url }}"/>

    {{ layout_macro.link_favicons(favicons) }}
</head>
<body>
    {% block content %}{% endblock %}

    <script type="text/javascript" src="https://code.jquery.com/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="{{ asset('/js/jquery.mobile.custom.min.js', 'static_js') }}"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.0.3/js/bootstrap.min.js"></script>
    {{ encore_entry_script_tags('touch_screen')  }}
</body>
</html>
{% endspaceless %}
