{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% set metas = metas|merge({'description': 'setups_meta_description' | trans({}, 'event') }) %}

{% block title %}
    {% trans from "event" %}setup_list_meta_title{% endtrans %} {% trans %}on_son_video_com{% endtrans %}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}

{% block content %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            <li>{% trans from "event" %}your_setups_selection{% endtrans %}</li>
        {% endblock crumbs %}
    {% endembed %}
    <div class="SVDv3_colonnes_1colonne mceContentBody">
        <h1 class="SVDv3_mobile_navbar">{% trans from "event" %}your_setups_selection{% endtrans %}</h1>

        <div class="vosInstallations" id="installationsTop">

            <div>
                <p>
                    <a href="{{ path('customer_social_new_installation_get') }}" class="center" style="display: block">
                        <img alt="{% trans from "event" %}setup_index_alt{% endtrans %}"
                             class="lozad"
                             src="{{ empty_data_image(980, 400) }}"
                             data-src="{{ asset('/images/illustration/entete/SVDINST_201207-980x400.jpg', 'static_images') }}">
                    </a>
                </p>
                <p class="text-center">(*) {% trans from "event" %}your_setups_reward{% endtrans %}</p>

                <div class="grid_container_12 mb-5">
                    <div class="grid_row">
                        <div class="col_8_col push-2">
                            <a class="btn btn-purple btn-round btn-lg btn-block text-uppercase" href="{{ path('customer_social_new_installation_get') }}">
                                {% trans from "event" %}your_setups_post_button{% endtrans %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="SVDv3_article_element">
                <p class="lead">{% trans from "event" %}your_setups_inspiration{% endtrans %}</p>
                <p>{% trans with {'%form_link%': path('contact_us_get')} from "event" %}your_setups_get_in_touch{% endtrans %}</p>
            </div>

            <installation-type-selector :choices="['all', 'hifi', 'home_cinema']" :selected="type" @choice-selected="choiceSelected"></installation-type-selector>

            <installation-list :installations="installations"></installation-list>

            <pagination-display :pager="pager" :offset="5" @page-selected="pageSelected"></pagination-display>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ path('bazinga_jstranslation_js', { 'domain': 'event' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script src="{{ path('bazinga_jstranslation_js', { 'domain': 'pager' }, true) }}?locales={{ app.request.getLocale() }}"></script>

    {{ encore_entry_script_tags('installation/list')  }}
{% endblock %}
