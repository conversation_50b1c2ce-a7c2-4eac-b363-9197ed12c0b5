{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% block content %}

    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            <li>{% trans from "account" %}update_email_confirm_breadcrumb{% endtrans %}</li>
        {% endblock crumbs %}
    {% endembed %}

    <div class="SVDv3_colonnes_2colonnes1 clearfix">

        <div class="SVDv3_colonnes_colonne_gauche">

        </div>

        <div class="SVDv3_colonnes_colonne_droite">
            <h1 class="SVDv3_mobile_navbar">{% trans from "account" %}update_email_confirm_title{% endtrans %}</h1>

            {% embed "@App/Common/block/message.html.twig" %}{% endembed %}

            {{ form_start(change_email_form, {
                'method': 'POST',
                'action': path('customer_change_email', {'token' : token}),
                'attr': {'class': 'SVDv3_form'}
            }) }}
            {{ form_row(change_email_form._token) }}

            {# Add a not used password field to have blank password fields #}
            <input type="password" style="display:none" />

            <p>
                {% trans from "account" %}update_email_confirm_content{% endtrans %}
            </p>
            <br/>

            <label for="{{ change_email_form.password.vars.id }}">
                {{ change_email_form.password.vars.label|trans }}
                {% if change_email_form.password.vars.required %}
                    &nbsp;(<span class="SVDv3_texteRouge">*</span>)
                {% endif %}
            </label>
            {{ form_widget(change_email_form.password) }}
            <p style="text-align: right;">
                <button type="submit" id="btnResetPassword" name="btnResetPassword">
                    <span class="SVDv3_bouton_37px SVDv3_bouton_37px_bleu_droit">
                        <span>{% trans %}Valider{% endtrans %}</span>
                    </span>
                </button>
            </p>
            {{ form_end(change_email_form) }}
        </div>
    </div>
{% endblock %}
