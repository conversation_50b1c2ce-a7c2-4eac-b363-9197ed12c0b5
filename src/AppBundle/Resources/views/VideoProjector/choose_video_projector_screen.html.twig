{% extends 'AppBundle:Layout:v3/base.html.twig' %}
{% import 'AppBundle:Common:form_macro.html.twig' as form_macro %}
{% import 'AppBundle:Common:article_macro.html.twig' as article_macro %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet([asset('v3/modules/produits/videoprojectionCalculEcran.css', 'static_css')]) }}
{% endblock %}

{% set metas = metas|merge({'description': 'choose_video_projector_screen_description' | trans({}, 'videoprojector') }) %}

{% block title %}
    {% trans from 'videoprojector' %}choose_video_projector_screen_title{% endtrans %}
{% endblock %}

{% block content %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            {% for stand in video_projector_section_hierarchy %}
                <li><a href="{{ path('show_stand', {'slug': stand.slug}) }}">{{ stand.name }}</a></li>
            {% endfor %}
            <li>{% trans from 'videoprojector' %}screen_calculator{% endtrans %}</li>
        {% endblock crumbs %}
    {% endembed %}

    <h1 class="SVDv3_mobile_navbar">{% trans from 'videoprojector' %}choose_video_projector_screen{% endtrans %}</h1>

    <div class="grid_container_12 mceContentBody">
        <div class="grid_row">
            <div class="col_8_col">
                <h2 class="SVDv3_titre_texte_niveau2">{% trans from 'videoprojector' %}already_know_video_projector{% endtrans %}</h2>
                {% trans from 'videoprojector' %}choose_screen_from_video_projector_instructions{% endtrans %}
            </div>
            <div class="col_4_col col_last">
                <div class="panel bg-offwhite font-opensans">
                    <div class="panel-body center">
                        <p class="margin-default">
                            <strong>{% trans from 'videoprojector' %}already_know_screen_size{% endtrans %}</strong>
                        </p>
                        <a class="btn btn-primary btn-block" href="{{ path('choose_video_projector') }}">
                            {% trans from 'videoprojector' %}choose_ideal_video_projector{% endtrans %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="SVDv3_hr"><hr></div>
    <div id="video-projector-app">
        <video-projector-screen></video-projector-screen>
    </div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}

    {# Load translations for current locale & domain #}
    <script src="{{ path('bazinga_jstranslation_js', { 'domain': 'common' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script src="{{ path('bazinga_jstranslation_js', { 'domain': 'messages' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script src="{{ path('bazinga_jstranslation_js', { 'domain': 'videoprojector' }, true) }}?locales={{ app.request.getLocale() }}"></script>

    <script type="text/javascript">
        var data = {
            'video_projectors': {{ video_projectors|json_encode()|raw }},
            'video_projector': {{ video_projector|json_encode()|raw }},
            'video_projector_distance': {{ video_projector_distance }},
            'screen_data': {{ screen_data|json_encode()|raw }}
        }

        var video_projectors = {{ video_projectors|json_encode()|raw }};
    </script>

    {{ encore_entry_script_tags('videoprojector')  }}
{% endblock %}
