<template>
    <div class="SVDv3_colonne_element search-filters" data-context="search-filters">
        <template v-if="(is_loading_filters || is_loading_articles) && !init && !is_mobile">
            <div class="article-list-skeleton" v-for="article_skeleton in article_skeletons"></div>
        </template>
        <template v-else>
            <filters-summary />

            <template v-if="is_mobile">
                <div class="search-filters-block d-flex" :class="{ 'is-open': is_open }">
                    <div class="btn btn-default btn-icon-after btn-toggle-filters" @click="is_open = !is_open">
                        <span v-if="!is_open">{{ 'show_more_filters' | trans({}, 'search') }}</span>
                        <span v-else>{{ 'hide_filters' | trans({}, 'search') }}</span>
                        <i class="icon icon-chevron-bottom"></i>
                    </div>
                </div>
            </template>

            <selected-filters />

            <component
                v-for="filter of filters"
                :is="`ArticleFilterType${capitalize(filter.type)}`"
                :filter="filter"
                :key="filter.name"
                data-context="search-filter"
                v-show="!is_mobile || is_open"
            />
        </template>
    </div>
</template>

<script>
import capitalize from 'lodash.capitalize'
import { mapState } from 'vuex'
import ArticleFilterTypeSlider from './ArticleFilterTypeSlider'
import ArticleFilterTypeDefault from './ArticleFilterTypeDefault'
import FiltersSummary from './FiltersSummary'
import SelectedFilters from './SelectedFilters'

export default {
    name: 'ArticleFilters',
    components: {
        ArticleFilterTypeSlider,
        ArticleFilterTypeDefault,
        FiltersSummary,
        SelectedFilters,
    },
    data() {
        return {
            is_mobile: false,
            is_open: false,
            article_skeletons: 20,
            init: false,
        }
    },
    computed: {
        ...mapState(['filters', 'is_loading_articles', 'is_loading_filters']),
    },
    watch: {
        is_loading_filters() {
            this.init = true
        },
    },
    methods: {
        capitalize,
        isMobileChecker() {
            this.is_mobile = window.innerWidth <= 992
        },
    },
    mounted() {
        this.isMobileChecker()
        window.addEventListener('resize', this.isMobileChecker)
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.isMobileChecker)
    },
}
</script>
