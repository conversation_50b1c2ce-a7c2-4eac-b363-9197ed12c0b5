const clickbait_column = Array.from(document.querySelectorAll('.SVDv3_colonne_element_appelBoutique'))

clickbait_column.forEach((clickbait) => {
    clickbait.addEventListener('click', (e) => {
        var targetUrl = clickbait.dataset.url;
        SonVideo.pushAnalytics('promoClick',
            [{
                name: clickbait.dataset.title,
                id: clickbait.dataset.id,
                creative: clickbait.dataset.location + '_column',
                position: clickbait.dataset.position
            }],
            targetUrl
        )
    })
})
