<template>
    <div class="newsletter-subscription">
        <div class="newsletter-subscription-body">
            <div class="ct-newsletter">
                <div class="ct-input">
                    <input
                        name="newsletter_toggle"
                        id="newsletter_toggle"
                        type="checkbox"
                        v-model="has_newsletter"
                        @change="toggleSubscription()"
                        :disabled="loading"
                    />
                </div>
                <div class="ct-label">
                    <label class="label-newletter" for="newsletter_toggle">
                        {{ 'passionate-newsletter-subscription-invitation-1' | trans({}, 'account') }}
                        <strong>
                            {{ 'passionate-newsletter-subscription-invitation-2' | trans({}, 'account') }}
                            <span class="text-nowrap">
                                {{ 'passionate-newsletter-subscription-invitation-3' | trans({}, 'account') }}
                            </span>
                        </strong>
                        {{ 'passionate-newsletter-subscription-invitation-4' | trans({}, 'account') }}
                        <strong>{{ 'passionate-newsletter-subscription-invitation-5' | trans({}, 'account') }}</strong>
                        {{ 'passionate-newsletter-subscription-invitation-6' | trans({}, 'account') }}
                    </label>
                    <svd-tooltip v-if="is_checkout">
                        <template #action>
                            <div class="logos-list-item logos-list-item-border cursor-pointer tootip-newsletter">
                                <img
                                    class="img_payment_detail"
                                    :src="'/images/ui/uiV3/icones/icon-help-16x16-2x.png' | asset('static_images')"
                                />
                            </div>
                        </template>
                        <template #reaction>
                            <div v-html="return_translation" />
                        </template>
                    </svd-tooltip>
                </div>
            </div>

            <ul v-if="!is_checkout" class="list-elem-newsletter-account">
                <li>{{ 'newsletter-subscription-list-elem-1' | trans({}, 'account') }}</li>
                <li>{{ 'newsletter-subscription-list-elem-2' | trans({}, 'account') }}</li>
            </ul>
        </div>
        <div v-if="loading && !is_checkout" class="newsletter-error-message">
            <div class="margin-md">
                <img :src="'/images/static/Basket/ajax-loader_p.gif' | asset('static_images')" />
            </div>
        </div>
        <div v-if="error_message" class="newsletter-error-message">
            <div class="SVDv3_messageAlerte SVDv3_messageAlerte_error">
                {{ error_message }}
            </div>
        </div>
        <div v-if="success && !is_checkout" class="newsletter-error-message">
            <div class="SVDv3_messageAlerte SVDv3_messageAlerte_success">
                {{ 'newsletter_status_changed_success' | trans({}, 'account') }}
            </div>
        </div>
    </div>
</template>

<script>
import api from '@/shared/api/handler'
import { trans } from '@/shared/filters'
import SvdTooltip from '@/v5/components/tooltip/SvdTooltip.vue'

export default {
    name: 'NewsletterRegistration',
    components: { SvdTooltip },
    props: {
        hasNewsletter: Boolean,
        context: { type: String, required: true },
        is_checkout: { type: Boolean, default: false },
    },
    data() {
        return {
            has_newsletter: this.hasNewsletter,
            loading: false,
            success: false,
            error_message: null,
            clear_counter: 0,
        }
    },
    computed: {
        return_translation() {
            const newsletter_subscription_list_elem_1 = trans('newsletter-subscription-list-elem-1', {}, 'account')
            const newsletter_subscription_list_elem_2 = trans('newsletter-subscription-list-elem-2', {}, 'account')
            return `<ul class="list-tooltip-newsletter">
                      <li>${newsletter_subscription_list_elem_1}</li>
                      <li>${newsletter_subscription_list_elem_2}</li>
                  </ul>`
        },
    },
    methods: {
        toggleSubscription() {
            // block functionality until previous loading is finished
            if (this.loading) {
                return
            }

            // clean any previous success or error message
            // and activate loading
            this.success = false
            this.error_message = null
            this.loading = true

            // to this point, has_newsletter is the desired state
            // call to api for subscribe or unsubscribe
            api.post('/api/newsletter/change-souscription', {
                subscription: this.has_newsletter,
                origin_subscribe: this.context,
            })
                .then((response) => {
                    // handle success and failure
                    if (response.success === false) {
                        this.has_newsletter = !this.has_newsletter
                        this.error_message = response.message
                    } else {
                        this.success = true
                        if (this.has_newsletter) {
                            window.dataLayer.push({
                                event: 'eventTracking',
                                category: 'newsletter',
                                action: 'submit',
                                label: this.context,
                            })

                            window.dataLayer.push({
                                event: 'newsletter_signup',
                                label: this.context,
                            })
                        }
                    }

                    this.loading = false
                })
                .catch((error) => {
                    this.success = false
                    switch (error.data[0]) {
                        case 'Bounced':
                        case 'Complained':
                            this.error_message = trans('newsletter_status_changed_bounced', {}, 'account')
                            this.has_newsletter = false
                            break
                        case 'Bounced email address.':
                            this.error_message = error.data[0]
                            this.has_newsletter = false
                            break
                        default:
                            this.error_message = trans('newsletter_status_changed_error', {}, 'account')
                            break
                    }
                    this.loading = false
                })
        },
    },
}
</script>
