import Vue from 'vue'
import { trans, asset } from '@/shared/filters'

Vue.filter('trans', trans)
Vue.filter('asset', asset)

// group things in an immediately executed function to:
// - alias window.SonVideo to "svd"
// - do some controls and exit on failure
// - encapsulate the Vue app
;((svd) => {
    new Vue({
        name: 'NewsletterSubscriptionApp',
        el: '#newsletter-subscription',
        provide: {
            version: svd?.version ?? 'v3',
        },
        components: {
            NewsletterSubscription: () => import(/* webpackPrefetch: true */ './components/NewsletterSubscription'),
        },
        template: `<newsletter-subscription />`,
    })
})(window.SonVideo)
