<template>
    <p>
        <a
            aria-label="Ajouter un équipement"
            class="btn btn-purple btn-outline btn-round btn-icon-before"
            href=""
            @click.prevent="openPopin()"
            ><i class="icon icon-add"></i>{{ 'add_an_equipment' | trans({}, 'event') }}</a
        >
    </p>
</template>

<script>
import Vue from 'vue'
import { mapActions } from 'vuex'
import EquipmentPicker from './EquipmentPicker.vue'
import EquipmentList from './EquipmentList.vue'
import { trans } from '../../../shared/filters'
import store from '../store/index'

export default {
    data() {
        return {
            app: null,
        }
    },
    methods: {
        openPopin() {
            window
                .swal({
                    title: trans('add_an_equipment', {}, 'event'),
                    html: `<div id="equipment-selector-popin" class="text-left mceContentBody">
                            <equipment-picker @equipment-selected="addEquipment"></equipment-picker>
	                        <div class="hr mb-3"><hr></div>
	                        <h3>${trans('list_of_already_selected_equipment', {}, 'event')}</h3>
                            <equipment-list></equipment-list>
                        </div>`,
                    customClass: 'equipments-popin',
                    showCancelButton: false,
                    showCloseButton: true,
                    showConfirmButton: true,
                    confirmButtonText: trans('finished'),
                    confirmButtonClass: 'btn btn-purple btn-round btn-lg equipments-submit',
                    buttonsStyling: false,
                    onOpen: () => {
                        this.app = new Vue({
                            name: 'EquipmentSelectorPopin',
                            el: '#equipment-selector-popin',
                            store,
                            components: {
                                EquipmentPicker,
                                EquipmentList,
                            },
                            methods: {
                                ...mapActions(['addEquipment']),
                            },
                        })
                    },
                    onClose: () => {
                        this.app.$destroy()
                    },
                })
                .catch(window.swal.noop)
        },
    },
}
</script>
