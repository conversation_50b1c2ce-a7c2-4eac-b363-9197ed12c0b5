import Vue from 'vue'
import store from './store/index.js'
import EquipmentList from './components/EquipmentList.vue'
import EquipmentSelectorPopin from './components/EquipmentSelectorPopin.vue'
import EquipmentFormWrapper from './components/SymfonyFormWrapper.vue'
import { trans } from '../../shared/filters.js'

Vue.filter('trans', trans)

new Vue({
    name: 'InstallationSelectorApp',
    el: '#install_form_materiels_block',
    store,
    components: { EquipmentList, EquipmentSelectorPopin, EquipmentFormWrapper },
})
