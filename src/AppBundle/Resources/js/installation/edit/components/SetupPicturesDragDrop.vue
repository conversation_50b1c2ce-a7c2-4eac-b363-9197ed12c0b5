<template>
    <div id="installation_pictures_edit" class="mceContentBody font-opensans">
        <div id="installation_select_pictures">
            <div id="file-drag-drop" v-if="dragAndDropCapable">
                <form ref="fileform" @click="triggerClick()">
                    <span class="drop-files">{{ 'drag_and_drop_here' | trans }}</span>
                </form>
            </div>
            <input
                v-show="!dragAndDropCapable"
                type="file"
                accept=".jpg"
                multiple
                ref="inputFiles"
                @change="uploadInputFiles()"
            />
        </div>
        <ul class="margin-sm">
            <li v-for="uploadedFile in uploadedFiles">
                <div>
                    <small>{{ uploadedFile.name }}</small>
                </div>
                <div v-if="uploadedFile.error_size">
                    <span class="error" style="margin-left: 0px">
                        {{ 'error_size_must_be_lower_than_mo' | trans({ size: pictureSizeLimit / 1000000 }) }}
                    </span>
                </div>
                <div v-else-if="uploadedFile.error_format">
                    <span class="error" style="margin-left: 0px">
                        {{ 'error_file_must_be_jpg_format' | trans }}
                    </span>
                </div>
                <div v-else>
                    <progress
                        v-if="uploadedFile.status === 'in_progress' || uploadedFile.status === 'success'"
                        max="100"
                        :value.prop="uploadedFile.uploadPercentage"
                    ></progress>
                    <br />
                    <span v-if="uploadedFile.status === 'error_max_size'" class="error" style="margin-left: 0px">
                        {{ 'maximum_number_of_files_is_reached' | trans }}
                    </span>
                    <span v-else-if="uploadedFile.status === 'unknown_error'" class="error" style="margin-left: 0px">{{
                        'an_error_occured' | trans
                    }}</span>
                </div>
            </li>
        </ul>
        <button
            aria-label="Sauvegarder"
            v-if="enable_show_and_receive_button"
            @click="saveAndReload()"
            class="btn btn-default btn-icon-before"
        >
            <i class="icon icon-chevron-right"></i>{{ 'use_pictures' | trans({}, 'event') }}
        </button>
    </div>
</template>

<script>
import api from '../../../shared/api/handler'
import { trans } from '../../../shared/filters.js'

export default {
    props: {
        installationId: String,
        pictureSizeLimit: Number,
    },
    /*
         Variables used by the drag and drop component
         */
    data() {
        return {
            dragAndDropCapable: true,
            fileToUploadStack: [],
            uploadedFiles: [],
            uploadPercentage: 0,
            enable_show_and_receive_button: false,
        }
    },
    methods: {
        triggerClick() {
            this.$refs.inputFiles.click()
        },
        uploadInputFiles() {
            for (let file of this.$refs.inputFiles.files) {
                this.fileToUploadStack.push(file)
            }

            if (this.$refs.inputFiles.files.length > 0) {
                this.uploadFileFromStack()
            }
        },
        /*
             Determines if the drag and drop functionality is in the
             window
             */
        determineDragAndDropCapable() {
            /*
                 Create a test element to see if certain events
                 are present that let us do drag and drop.
                 */
            var div = document.createElement('div')

            /*
                 Check to see if the `draggable` event is in the element
                 or the `ondragstart` and `ondrop` events are in the element. If
                 they are, then we have what we need for dragging and dropping files.

                 We also check to see if the window has `FormData` and `FileReader` objects
                 present so we can do our AJAX uploading
                 */
            return (
                ('draggable' in div || ('ondragstart' in div && 'ondrop' in div)) &&
                'FormData' in window &&
                'FileReader' in window
            )
        },
        showSave() {
            this.enable_show_and_receive_button = true
        },
        saveAndReload() {
            window.document.getElementsByName('installation_edit_form')[0].submit()
        },
        // Upload file one by one in order to avoid exceed size limit.
        uploadFileFromStack() {
            if (this.fileToUploadStack.length === 0) {
                return this.showSave()
            }

            var formData = new FormData()
            formData.append('image', this.fileToUploadStack[0])

            let uploadedFile = {
                name: this.fileToUploadStack[0].name,
                size: this.fileToUploadStack[0].size,
                type: this.fileToUploadStack[0].type,
                status: 'in_progress',
                uploadPercentage: 0,
            }
            this.uploadedFiles.push(uploadedFile)

            if (this.fileToUploadStack[0].size > this.pictureSizeLimit) {
                uploadedFile['error_size'] = 1
                this.fileToUploadStack.shift()

                return this.uploadFileFromStack()
            }

            if (this.fileToUploadStack[0].type !== 'image/jpeg') {
                uploadedFile['error_format'] = 1
                this.fileToUploadStack.shift()

                return this.uploadFileFromStack()
            }

            api.post('/mon-compte/mon-installation/' + this.installationId + '/upload-image', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
                onUploadProgress: function (progressEvent) {
                    uploadedFile.uploadPercentage = parseInt(
                        Math.round((progressEvent.loaded * 100) / progressEvent.total)
                    )
                }.bind(this),
            })
                .then(() => {
                    uploadedFile['status'] = 'success'
                    this.fileToUploadStack.shift()
                    this.uploadFileFromStack()
                })
                .catch((data) => {
                    if (typeof data.data.error_code !== 'undefined' && data.data.error_code === 1) {
                        uploadedFile['status'] = 'error_max_size'
                    } else {
                        uploadedFile['status'] = 'unknown_error'
                    }

                    this.fileToUploadStack.shift()
                    this.uploadFileFromStack()
                })
        },
    },
    mounted() {
        /*
             Determine if drag and drop functionality is capable in the browser
             */
        this.dragAndDropCapable = this.determineDragAndDropCapable()

        /*
             If drag and drop capable, then we continue to bind events to our elements.
             */
        if (this.dragAndDropCapable) {
            /*
                 Listen to all of the drag events and bind an event listener to each
                 for the fileform.
                 */
            ;['drag', 'dragstart', 'dragend', 'dragover', 'dragenter', 'dragleave', 'drop'].forEach(
                function (evt) {
                    /*
                     For each event add an event listener that prevents the default action
                     (opening the file in the browser) and stop the propagation of the event (so
                     no other elements open the file in the browser)
                     */
                    this.$refs.fileform.addEventListener(
                        evt,
                        function (e) {
                            e.preventDefault()
                            e.stopPropagation()
                        }.bind(this),
                        false
                    )
                }.bind(this)
            )

            /*
                 Add an event listener for drop to the form
                 */
            this.$refs.fileform.addEventListener(
                'drop',
                function (e) {
                    /*
                     Capture the files from the drop event and add them to our local files
                     array.
                     */
                    for (let i = 0; i < e.dataTransfer.files.length; i++) {
                        this.fileToUploadStack.push(e.dataTransfer.files[i])
                    }

                    this.enable_show_and_receive_button = false
                    this.uploadFileFromStack()
                }.bind(this)
            )
        }
    },
}
</script>
