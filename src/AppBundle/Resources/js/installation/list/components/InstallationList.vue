<template>
    <div id="listeInstallation" class="container">
        <div class="row">
            <div
                v-for="installation in installations"
                class="col-12 col-md-6 col-lg-4 grid-item"
                :key="installation.url"
            >
                <div class="card card-shadowed h-100">
                    <a aria-label="Lien vers l'installation" :href="installation.url">
                        <div class="card-wrapper">
                            <div class="card-media-wrapper card-media-wrapper-fixed-height">
                                <div class="card-media">
                                    <img
                                        class="lozad"
                                        :alt="
                                            'the_type_setup_of_username'
                                                | trans({ type: '', name: installation.customer_name }, 'event')
                                        "
                                        :src="empty_image"
                                        :data-src="
                                            preset(asset(installation.media.image_url, 'static_images'), PRESET_600)
                                        "
                                        :data-srcset="
                                            presets(asset(installation.media.image_url, 'static_images'), [
                                                [PRESET_400, '400w'],
                                                [PRESET_600, '600w'],
                                                [PRESET_1200, '1200w'],
                                            ])
                                        "
                                        :sizes="`${BREAKPOINT_SM_MIN_WIDTH} 400px, 600px`"
                                    />
                                </div>
                            </div>
                            <div class="card-body">
                                <h3 class="card-title">
                                    {{
                                        'the_type_setup_of_username'
                                            | trans({ type: '', name: installation.customer_name }, 'event')
                                    }}
                                    <small class="card-title-subtitle"
                                        >{{ 'the' | trans }} {{ formatDate(installation.date.date) }}</small
                                    >
                                </h3>
                                <p>{{ installation.comment }}</p>
                            </div>
                            <div class="card-footer">
                                <span class="link-arrow link-arrow-after link-arrow-right">{{
                                    'see_setup' | trans({}, 'event')
                                }}</span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import format from 'date-fns/format'
import lazyLoadImagesMixin from '../../../shared/mixins/lazyLoadImagesMixin'
import { asset, preset, presets } from '@/shared/filters'
import { PRESET_1200, PRESET_400, PRESET_600 } from '@/shared/referential/images'
import { emptyDataImage } from '@/shared/article/functions'
import { BREAKPOINT_SM_MIN_WIDTH } from '@/shared/referential/Breakpoints'

export default {
    mixins: [lazyLoadImagesMixin],
    props: {
        installations: Array,
    },
    computed: {
        PRESET_400: () => PRESET_400,
        PRESET_600: () => PRESET_600,
        PRESET_1200: () => PRESET_1200,
        BREAKPOINT_SM_MIN_WIDTH: () => BREAKPOINT_SM_MIN_WIDTH,
        empty_image: () => emptyDataImage(400, 180),
    },
    methods: {
        presets,
        preset,
        asset,
        name(installation) {
            return (
                (installation.customer.firstname ? installation.customer.firstname + ' ' : '') +
                (installation.customer.lastname ? installation.customer.lastname + '.' : '')
            )
        },
        formatDate(date) {
            return format(date, 'DD/MM/YYYY')
        },
    },
}
</script>
