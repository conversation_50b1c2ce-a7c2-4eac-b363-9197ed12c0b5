<template>
    <modal-loader
        v-if="selector_displayed"
        smallest
        title="Outils de développement"
        @close="closeSelector"
        data-context="developer-settings-modal"
    >
        <div class="dev-settings-container" id="developer-settings-modal">
            <div class="dev-settings-intro">
                <div class="dev-settings-intro-description">
                    Cette section regroupe différents outils de développement permettant d'activer des fonctionnalités
                    spécifiques ou de réinitialiser certains états de l'application. Ces options sont uniquement
                    disponibles dans les environnements non-productifs.
                </div>
                <div class="dev-settings-env-info">
                    Vous êtes sur l'environnement de <strong>{{ currentEnv }}</strong>
                </div>
            </div>

            <div class="dev-settings-group">
                <div class="dev-settings-group-title">Tracking & Analyse</div>
                <div class="dev-settings-section">
                    <div class="dev-settings-section-content">
                        <div class="dev-settings-section-header">
                            <div class="dev-settings-section-title">Bloomreach</div>
                            <div class="dev-settings-section-description">
                                <span
                                    >Activation du consentement Bloomreach pour le tracking et l'analyse
                                    comportementale</span
                                >
                                <span class="dev-settings-note"
                                    >Actuellement, le consentement Bloomreach est
                                    {{ hasConsent ? 'activé' : 'désactivé' }}</span
                                >
                            </div>
                        </div>
                        <div class="dev-settings-section-actions">
                            <svd-button
                                @click="toggleBloomreach"
                                :color="hasConsent ? 'secondary' : 'primary'"
                                :label="hasConsent ? 'Désactiver' : 'Activer'"
                                class="dev-settings-action-button"
                                small
                            />
                        </div>
                    </div>
                    <svd-message v-if="notifications.bloomreach" type="success" compact>
                        Bloomreach a été {{ hasConsent ? 'activé' : 'désactivé' }}
                    </svd-message>
                </div>
            </div>

            <div class="dev-settings-group">
                <div class="dev-settings-group-title">Debug</div>
                <div class="dev-settings-section">
                    <div class="dev-settings-section-content">
                        <div class="dev-settings-section-header">
                            <div class="dev-settings-section-title">Debug Recherche</div>
                            <div class="dev-settings-section-description">
                                <span>Affiche les scores de pertinence dans les résultats de recherche</span>
                                <span class="dev-settings-note"
                                    >Actuellement, le mode debug est
                                    {{ godModeEnabled ? 'activé' : 'désactivé' }}</span
                                >
                            </div>
                        </div>
                        <div class="dev-settings-section-actions">
                            <svd-button
                                @click="toggleGodMode"
                                :color="godModeEnabled ? 'secondary' : 'primary'"
                                :label="godModeEnabled ? 'Désactiver' : 'Activer'"
                                class="dev-settings-action-button"
                                small
                            />
                        </div>
                    </div>
                    <svd-message v-if="notifications.god_mode" type="success" compact>
                        Le mode debug de recherche a été {{ godModeEnabled ? 'activé' : 'désactivé' }}
                    </svd-message>
                </div>
            </div>

            <div class="dev-settings-group">
                <div class="dev-settings-group-title">Gestion des Popins</div>
                <div class="dev-settings-section">
                    <div class="dev-settings-section-content">
                        <div class="dev-settings-section-header">
                            <div class="dev-settings-section-title">Newsletter</div>
                            <div class="dev-settings-section-description">
                                <span>Supprime les infos lié à la fermeture de la popin de newsletter.</span>
                                <span class="dev-settings-note"
                                    >La popin s'affichera à nouveau lors de votre prochaine navigation. Selon les règles
                                    mises en place</span
                                >
                            </div>
                        </div>
                        <div class="dev-settings-section-actions">
                            <svd-button
                                @click="resetNewsletterPopin"
                                color="primary"
                                label="Réinitialiser"
                                class="dev-settings-action-button"
                                small
                            />
                        </div>
                    </div>
                    <svd-message v-if="notifications.newsletter" type="success" compact>
                        Les cookies de la newsletter ont été réinitialisés
                    </svd-message>
                </div>

                <div class="dev-settings-section">
                    <div class="dev-settings-section-content">
                        <div class="dev-settings-section-header">
                            <div class="dev-settings-section-title">Besoin d'aide ? (CallCenter)</div>
                            <div class="dev-settings-section-description">
                                <span>Réinitialise le délai d'affichage de la popin du service client.</span>
                                <span class="dev-settings-note"
                                    >La popin s'affichera à nouveau lors de votre prochaine navigation. Selon les règles
                                    mises en place</span
                                >
                            </div>
                        </div>
                        <div class="dev-settings-section-actions">
                            <svd-button
                                @click="resetPopinClosedTime"
                                color="primary"
                                label="Réinitialiser"
                                class="dev-settings-action-button"
                                small
                            />
                        </div>
                    </div>
                    <svd-message v-if="notifications.callcenter" type="success" compact>
                        Le timer du 'Besoin d'aide ?' a été réinitialisé
                    </svd-message>
                </div>
            </div>
        </div>
    </modal-loader>
</template>

<script>
import { mapActions, mapState } from 'pinia'
import { useDeveloperSettingsStore } from '@/developer_settings/stores'
import { useCookiesStore } from '@/cookies/stores'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import ModalLoader from '@/v5/components/modal/ModalLoader.vue'
import SvdMessage from '@/v5/components/message/SvdMessage.vue'
import { getEnvironment } from '@/shared/utils/environment'

export default {
    name: 'DeveloperSettings',
    components: { SvdButton, ModalLoader, SvdMessage },
    computed: {
        ...mapState(useDeveloperSettingsStore, ['selector_displayed']),
        ...mapState(useCookiesStore, ['consent_groups']),
        currentEnv() {
            return getEnvironment()
        },
        hasConsent() {
            const bloomreachGroup = this.consent_groups?.find((group) =>
                group.consents?.some((consent) => consent.key === 'bloomreach')
            )
            const bloomreachConsent = bloomreachGroup?.consents?.find((consent) => consent.key === 'bloomreach')
            return bloomreachConsent?.value || false
        },
    },
    data() {
        return {
            notifications: {
                bloomreach: false,
                newsletter: false,
                callcenter: false,
                god_mode: false,
            },
            godModeEnabled: false,
        }
    },
    mounted() {
        this.godModeEnabled = localStorage.getItem('god_mode') === 'true'
    },
    methods: {
        ...mapActions(useDeveloperSettingsStore, ['openSelector', 'closeSelector']),
        ...mapActions(useCookiesStore, ['setConsentKey', 'saveConsent']),
        toggleBloomreach() {
            const newValue = !this.hasConsent
            this.setConsentKey({ key: 'bloomreach', value: newValue })
            this.saveConsent()
            this.showNotification('bloomreach')
        },
        toggleGodMode() {
            this.godModeEnabled = !this.godModeEnabled
            localStorage.setItem('god_mode', this.godModeEnabled)
            this.showNotification('god_mode')
        },
        showNotification(type) {
            this.notifications[type] = true
            setTimeout(() => {
                this.notifications[type] = false
            }, 3000)
        },
        resetNewsletterPopin() {
            SonVideo.removeCookie('newsletter.modal_closed')
            SonVideo.removeCookie('pageViews')
            this.showNotification('newsletter')
        },
        resetPopinClosedTime() {
            SonVideo.removeCookie('popinClosedTime')
            this.showNotification('callcenter')
        },
    },
}
</script>

<style lang="scss">
@import '../../../../scss/style';

.dev-settings-container {
    padding: $space_24;

    .dev-settings-intro {
        margin-bottom: $space_24;
        padding-bottom: $space_16;
        border-bottom: 1px solid $color_grey_border_disabled;

        .dev-settings-intro-description {
            color: $color_grey_typo;
            font-size: $space_14;
            line-height: 1.5;
            margin-bottom: $space_12;
        }

        .dev-settings-env-info {
            color: $color_grey_typo;
            font-size: $space_14;
            padding: $space_8 $space_12;
            background: $color_grey_light;
            border-radius: $radius_4;
            display: inline-block;

            strong {
                color: $color_blue_default;
                font-weight: 600;
            }
        }
    }

    .dev-settings-group {
        margin-bottom: $space_24;

        &:last-child {
            margin-bottom: 0;
        }

        .dev-settings-group-title {
            color: $color_dark_default;
            font-size: $space_16;
            font-weight: 600;
            margin-bottom: $space_12;
            padding-bottom: $space_8;
            border-bottom: 1px solid $color_grey_border_disabled;
        }
    }

    .dev-settings-section {
        background: $color_white;
        border: 1px solid $color_grey_border_disabled;
        border-radius: $radius_8;
        padding: $space_16;
        margin-bottom: $space_12;
        transition: $transition_all_02;
        position: relative;
        overflow: hidden;

        &:last-child {
            margin-bottom: 0;
        }

        &:hover {
            border-color: $color_blue_default;
            box-shadow: 0 $space_4 $space_16 rgba($color_blue_default, 0.08);

            &::before {
                opacity: 1;
                transform: translateX(0);
            }
        }

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: $space_4;
            height: 100%;
            background: $color_blue_default;
            opacity: 0;
            transform: translateX(-100%);
            transition: $transition_all_02;
        }

        .dev-settings-section-content {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: $space_16;
        }

        .dev-settings-section-header {
            flex: 1;

            .dev-settings-section-title {
                color: $color_dark_default;
                font-size: $space_16;
                font-weight: 600;
                margin-bottom: $space_4;
            }

            .dev-settings-section-description {
                color: $color_grey_typo;
                font-size: $space_14;
                margin: 0;
                display: flex;
                flex-direction: column;
                gap: $space_4;

                .dev-settings-note {
                    color: $color_grey_typo_disabled;
                    font-size: $space_12;
                    font-style: italic;
                }
            }
        }

        .dev-settings-section-actions {
            padding-top: $space_4;

            .dev-settings-action-button {
                min-width: 100px;
            }
        }
    }
}
</style>
