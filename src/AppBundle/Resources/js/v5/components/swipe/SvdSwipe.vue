<script setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const cursorStart = ref()
const cursorMove = ref()
const marge = 20

const emit = defineEmits(['left', 'right', 'move:start', 'move:end'])
function touchStart(e) {
    cursorStart.value = e.targetTouches[0].clientX
}
function touchMove(e) {
    cursorMove.value = e.targetTouches[0].clientX
}

function actionEnd() {
    calculateEmit(cursorStart.value, cursorMove.value)
}

function clickStart(e) {
    e.preventDefault()
    cursorStart.value = e.clientX
}

function clickMove(e) {
    if (cursorStart.value) {
        cursorMove.value = e.clientX
    }
}

function arrowMove(e) {
    if (e.key === 'ArrowRight') {
        emit('right')
        resetCursor()
        return
    }
    if (e.key === 'ArrowLeft') {
        emit('left')
        resetCursor()
        return
    }
}
function calculateEmit(start, end) {
    if (start && end && start !== end) {
        if (start + marge < end) {
            emit('left')
            resetCursor()
            return
        }
        if (start - marge > end) {
            emit('right')
            resetCursor()
            return
        }
    }
    resetCursor()
}

function resetCursor() {
    cursorStart.value = null
    cursorMove.value = null
    emit('move:end')
}

const content_placement = computed(() => {
    const maxMove = 150
    let dist = 0
    let cursor = 'unset'

    if (cursorStart.value && cursorMove.value) {
        dist = cursorMove.value - cursorStart.value
    }
    if (Math.abs(dist) > maxMove) {
        dist = dist > 0 ? maxMove : -maxMove
    }
    if (isNaN(dist)) {
        dist = 0
    }
    if (dist !== 0) {
        cursor = 'grabbing'
    }

    return {
        position: 'relative',
        left: `${dist}px`,
        marginLeft: '0px',
        cursor,
    }
})
onMounted(() => {
    window.addEventListener('keydown', arrowMove)
})
onUnmounted(() => {
    window.removeEventListener('keydown', arrowMove)
})

watch(cursorStart, (newVal) => {
    if (newVal) {
        emit('move:start')
    }
})
</script>

<template>
    <div
        class="svd-swipe"
        @touchstart="touchStart"
        @touchmove="touchMove"
        @touchend="actionEnd"
        @mousedown="clickStart"
        @mousemove="clickMove"
        @mouseup="actionEnd"
    >
        <div class="slot" :style="content_placement">
            <slot />
        </div>
    </div>
</template>

<style scoped lang="scss">
.svd-swipe {
    width: 100%;
    height: 100%;
    .slot {
        width: 100%;
        height: 100%;
    }
}
</style>
