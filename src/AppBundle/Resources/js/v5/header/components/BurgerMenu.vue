<template>
    <side-panel :template="version" content-class="80vw" v-if="is_active" position="left" @close="deactivate" blank>
        <template v-if="'v5' === version">
            <nav class="relative w-[280px] top-0 left-0 z-10 bg-[#ffffff] scrollbar-hidden" data-context="burger-menu">
                <section class="flex h-[60px] w-[280px] bg-[#111216] items-center fixed z-[2]">
                    <div class="block w-[202px] h-[50px] ml-2 flex justify-center" data-context="logo">
                        <img
                            class="w-[170px]"
                            :src="asset('/images/ui/icons-svg/v5/logo-25-ans-mobile.svg', 'static_images')"
                            alt="Son-Vidéo.com"
                        />
                    </div>
                    <header-button
                        aria-label="Fermer"
                        class="text-white ml-auto mr-2"
                        :icon="Light.Xmark"
                        @click="deactivate"
                        data-context="leave-burger-menu"
                    />
                </section>
                <template v-for="l in max_level">
                    <transition
                        v-if="l === level"
                        mode="in-out"
                        :appear="level < 1"
                        :enter-class="computed_content_class"
                        :enter-to-class="computed_content_class"
                    >
                        <section
                            class="absolute duration-200 transition-all ease-in-out bg-[#ffffff] !h-[calc(100dvh-60px)] h-[calc(100vh-60px)] w-full mt-[60px]"
                            :data-context="`level-${level}`"
                            :data-position="position"
                            :key="`level-${level}`"
                        >
                            <section v-if="level === 1" class="flex flex-wrap pt-[21px] pb-[21px] pl-[7px]">
                                <a
                                    @click="contactUs"
                                    class="flex w-[266px] py-1.5 border-[1px] border-[#d0d0d0] rounded-[40px] items-center cursor-pointer hover:bg-[#eeeeee] duration-300"
                                    data-context="contact-us"
                                >
                                    <img
                                        class="w-[22px] h-[24px] ml-4 mt-0.5"
                                        :src="asset('/images/ui/icons-svg/v5/picto-contact.svg', 'static_images')"
                                        alt="Contact-Son-Video.com"
                                    />
                                    <p class="text-[#454545] ml-[14px] font-semibold text-[15px]">Contactez-nous</p>
                                </a>
                            </section>
                            <burger-menu-previous v-else />
                            <burger-menu-section
                                v-for="section in current_menu_sections"
                                :section="section"
                                :key="section.id"
                            />
                        </section>
                    </transition>
                </template>
            </nav>
        </template>
        <template v-else>
            <div class="base-burger-menu">
                <nav class="burger-menu" data-context="burger-menu">
                    <section class="top">
                        <div class="site-logo" data-context="logo">
                            <img
                                :src="asset('/images/ui/icons-svg/v5/logo-25-ans-mobile.svg', 'static_images')"
                                alt="Son-Vidéo.com"
                            />
                        </div>
                        <header-button
                            aria-label="Fermer"
                            class="icon-leave"
                            :icon="Light.Xmark"
                            @click="deactivate"
                            data-context="leave-burger-menu"
                        />
                    </section>
                    <template v-for="l in max_level">
                        <transition v-if="l === level" :appear="level > 1" name="slide-in-out" mode="in-out">
                            <section
                                class="white-mask slide-in-out-effect"
                                :class="`slide-${position}`"
                                :key="`level-${level}`"
                                :data-context="`level-${level}`"
                            >
                                <section v-if="level === 1" class="main-bloc border">
                                    <a
                                        aria-label="Contactez-nous"
                                        @click="contactUs"
                                        class="contact-us-section"
                                        data-context="contact-us"
                                    >
                                        <p>Contactez-nous</p>
                                    </a>
                                </section>
                                <burger-menu-previous v-else />
                                <burger-menu-section
                                    v-for="(section, index) in current_menu_sections"
                                    :section="section"
                                    :key="section.id"
                                    :class="{ border: index + 1 < current_menu_sections.length }"
                                />
                            </section>
                        </transition>
                    </template>
                </nav>
            </div>
        </template>
    </side-panel>
</template>

<script>
import { mapActions, mapState } from 'pinia'
import { useBurgerMenuStore } from '@/v5/header/stores'
import { asset } from '@/shared/filters'
import { Light } from '@/shared/referential/SvdIconsReferential'

export default {
    name: 'BurgerMenu',
    inject: ['version'],
    components: {
        SidePanel: () => import('@/shared/components/SidePanel.vue'),
        BurgerMenuEntry: () => import('@/v5/header/components/BurgerMenuEntry.vue'),
        BurgerMenuSection: () => import('@/v5/header/components/BurgerMenuSection.vue'),
        BurgerMenuPrevious: () => import('@/v5/header/components/BurgerMenuPrevious.vue'),
        HeaderButton: () => import('@/v5/header/components/HeaderButton.vue'),
    },
    computed: {
        ...mapState(useBurgerMenuStore, ['current_menu_sections', 'is_active', 'level', 'position', 'max_level']),

        computed_content_class() {
            if (this.position === 'right') {
                return 'translate-x-full'
            } else if (this.position === 'left') {
                return '-translate-x-full'
            }
        },
        Light() {
            return Light
        },
    },
    methods: {
        ...mapActions(useBurgerMenuStore, ['deactivate', 'loadMenuSections']),
        asset,

        contactUs() {
            this.deactivate()
            window.SonVideo.contact.open({ label: 'burger menu > contactez-nous', action: '' })
        },
    },
    mounted() {
        this.loadMenuSections()
    },
    watch: {
        is_active(value) {
            const body = document.querySelector('body')
            const scroll_lock_class =
                'v5' === this.version
                    ? ['!h-[calc(100dvh-60px)]', 'h-[calc(100vh-60px)]', 'overflow-y-hidden']
                    : ['scroll_lock']
            if (value === true) {
                body.classList.add(...scroll_lock_class)
            } else {
                body.classList.remove(...scroll_lock_class)
            }
        },
    },
}
</script>
