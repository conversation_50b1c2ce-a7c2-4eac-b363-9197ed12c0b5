<script setup>
import { useCheckoutProcessStore } from '@/checkout_process_v2/stores/checkout_process'

const store = useCheckoutProcessStore()
</script>

<template>
    <footer id="footer">
        <section class="reassurance">
            <div class="global-div" onMouseDown="startDrag()">
                <div id="reassurance">
                    <div
                        v-for="reassurance_item in store.funnel_footer_data.reassurances"
                        class="item"
                        :data-context="reassurance_item.context"
                    >
                        <div :class="reassurance_item.picto"></div>
                        <span v-html="reassurance_item.label" />
                    </div>
                </div>
            </div>
        </section>
        <section class="payment-method">
            <div class="link tunnel" data-context="secure-payment">
                <div aria-label="Lien modes de paiement" class="secure-link">
                    <div
                        v-for="payment in store.funnel_footer_data.payments"
                        :class="`background-${payment.icon}`"
                    ></div>
                </div>
            </div>
        </section>
    </footer>
</template>
