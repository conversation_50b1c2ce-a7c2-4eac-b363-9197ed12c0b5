import Vue from 'vue'
import store from './store/index'
import { mapState } from 'vuex'
import SearchSummary from './components/SearchSummary.vue'
import SearchResults from './components/SearchResults.vue'
import SearchFilterPriceRange from './components/SearchFilterPriceRange.vue'
import SearchFilter from './components/SearchFilter.vue'
import { trans, capitalize, formatPriceSvd, asset } from '../shared/filters'
import { cleanParam } from './helpers'
import { makeUrlQueryString, updateUrlQueryString } from '../shared/api/handler'

Vue.filter('trans', trans)
Vue.filter('capitalize', capitalize)
Vue.filter('formatPriceSvd', formatPriceSvd)
Vue.filter('asset', asset)

new Vue({
    name: 'SearchApp',
    el: '#search_app',
    store,
    computed: {
        ...mapState([
            'filters',
            'price_range',
            'selected_sorting_option',
            'default_selected_sorting_option',
            'offset',
            'default_offset',
            'display',
            'default_display',
        ]),
        url_params() {
            return (
                [
                    {
                        name: 'filtres',
                        value: cleanParam(this.filters),
                    },
                    {
                        name: 'prix',
                        value: cleanParam({
                            from: this.price_range.from === this.price_range.min ? '' : this.price_range.from,
                            to: this.price_range.to === this.price_range.max ? '' : this.price_range.to,
                        }),
                    },
                    {
                        name: 'tri',
                        value: this.selected_sorting_option,
                        default: this.default_selected_sorting_option,
                    },
                    {
                        name: 'page',
                        value: this.offset,
                        default: this.default_offset,
                    },
                    {
                        name: 'affichage',
                        value: this.display || this.default_display,
                        default: this.default_display,
                    },
                ]
                    // keep only relevant values
                    .filter((param) => {
                        if (typeof param.value === 'object') {
                            return Object.keys(param.value).length > 0
                        }
                        if (Array.isArray(param.value)) {
                            return param.value.length > 0
                        }

                        return param.value !== param.default
                    })
                    // merge in a single object
                    .reduce((acc, current) => {
                        let value = current.value
                        if (typeof current.value === 'object' || Array.isArray(current.value)) {
                            value = JSON.stringify(current.value)
                        }
                        acc[current.name] = value

                        return acc
                    }, {})
            )
        },
    },
    components: { SearchSummary, SearchResults, SearchFilterPriceRange, SearchFilter },
    created() {
        this.$store.dispatch('initializeData', window.search_data)

        // listen to moves in the browser history
        window.addEventListener('popstate', () => {
            this.$store.dispatch('restoreParamsFromUrl')
        })
    },
    watch: {
        url_params: (params) => {
            // get and clean actual url parameters
            let url_search = window.location.search
            url_search = url_search
                .replace(/[\?&]filtres=[^&]*/g, '')
                .replace(/[\?&]prix=[^&]*/g, '')
                .replace(/[\?&]tri=[^&]*/g, '')
                .replace(/[\?&]page=[^&]*/g, '')
                .replace(/[\?&]affichage=[^&]*/g, '')

            // convert dynamic params to url usage
            const url_qs = makeUrlQueryString(params)

            // concat
            const search = url_search + (url_search.length > 0 && url_qs.length > 0 ? `&` : '') + url_qs
            updateUrlQueryString(search)
        },
    },
})
