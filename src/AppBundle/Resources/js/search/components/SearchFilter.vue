<template>
    <div class="search-filters-block is-open" v-if="show_filter">
        <div class="d-flex justify-content-between search-filters-heading">
            <span class="search-filters-title">
                {{ label | trans({}, 'common') }}
            </span>

            <a
                :aria-label="`Supprimer le filtre ${filter_name}`"
                class="search-filters-remove"
                href=""
                @click.prevent="resetOneFilter(filter_name)"
            >
                {{ 'reinitialize' | trans({}, 'search') }}
            </a>
        </div>

        <div class="search-filters-body">
            <div v-if="internalFilter" class="internal-filter">
                <label for="search">
                    <span class="icon-search">
                        <span class="sr-only">
                            {{ 'search' | trans({}, 'question') }}
                        </span>
                    </span>
                </label>
                <input id="search" type="text" v-model="internal_search" />
            </div>

            <ul class="search-filters-checkboxes" :class="{ filters_expanded: expanded }">
                <search-filter-item
                    v-for="item of items"
                    :key="item.value"
                    :by="filter_name"
                    :item="item"
                ></search-filter-item>

                <transition-group
                    enter-active-class="animated faster slideInDown"
                    leave-active-class="animated delay-1s fadeOut"
                    tag="span"
                >
                    <template v-if="expanded">
                        <search-filter-item
                            v-for="item of extended_items"
                            :key="item.value"
                            :by="filter_name"
                            :item="item"
                        ></search-filter-item>
                    </template>
                </transition-group>
            </ul>

            <div
                class="filter-collapse collapse-brands text-center cursor-pointer d-flex align-items-center justify-content-center"
                v-if="extended_items.length > 0"
            >
                <div v-show="!expanded" @click="toggle(true)" class="expand-filters">
                    <div class="d-flex align-items-center">
                        {{ 'show_all' | trans({}, 'search') }} <i class="icon icon-chevron-bottom ml-2"></i>
                    </div>
                </div>
                <div v-show="expanded" @click="toggle(false)" class="expand-filters">
                    <div class="d-flex align-items-center">
                        {{ 'show_less' | trans({}, 'search') }} <i class="icon icon-chevron-top ml-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState, mapActions } from 'vuex'
import SearchFilterItem from './SearchFilterItem.vue'
import { applyFilter } from '../helpers'

const NUMBER_OF_ITEMS_TO_DISPLAY = 8
const orderItemsByValue = (sort_inverse) => (current, previous) => {
    let current_value = current.value
    let previous_value = previous.value

    // extract number from value beginning with number
    if (typeof current_value === 'string' && /^-?\d+/.test(current_value)) {
        current_value = parseFloat(current_value)
    }
    if (typeof previous_value === 'string' && /^-?\d+/.test(previous_value)) {
        previous_value = parseFloat(previous_value)
    }

    // do a string comparison if one is still a string
    if (typeof current_value === 'string' || typeof previous_value === 'string') {
        return sort_inverse
            ? previous_value.toString().toLowerCase().localeCompare(current_value.toString().toLowerCase())
            : current_value.toString().toLowerCase().localeCompare(previous_value.toString().toLowerCase())
    }

    // else do a number comparison
    return sort_inverse ? previous_value - current_value : current_value - previous_value
}
const orderItemsByTotal = (sort_inverse) => (current, previous) => {
    return sort_inverse ? previous.total - current.total : current.total - previous.total
}
const orderItemsBy = (type, sort_inverse) => {
    let callable = orderItemsByValue

    if (type === 'total') {
        callable = orderItemsByTotal
    }

    return callable(sort_inverse)
}

export default {
    props: {
        filter_name: String,
        label: String,
        search_key: String,
        sort_by: {
            type: String,
            default: 'value',
        },
        sort_inverse: {
            type: Boolean,
            default: false,
        },
        internalFilter: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            pool: [],
            expanded: false,
            internal_search: '',
        }
    },
    computed: {
        ...mapState(['articles', 'registered_filters', 'filters', 'show_all_data']),
        ...mapGetters(['filtered_articles']),
        collection() {
            // note: field = this.search_key taken from the store
            const field = this.registered_filters[this.filter_name].key
            let seen = {}

            return (
                this.articles
                    // Get filter values extracted from the article list (only filtered by price range)
                    .reduce((list, article) => {
                        if (Array.isArray(article[field])) {
                            for (let key in article[field]) {
                                list.push(article[field][key])
                            }
                        } else {
                            list.push(article[field])
                        }

                        return list
                    }, [])
                    // Remove duplicate entries
                    .filter((value) => {
                        if (seen[value] || !value) {
                            return false
                        }

                        seen[value] = true
                        return true
                    })
                    // Format item as an Object with "value" and "total" (= number of matches in the pool) properties
                    .map((value) => {
                        const total = this.pool
                            // To avoid duplicate calculation on brands, categories and review-scores filters (only count on default articles for those filters).
                            .filter((pool_item) => {
                                return (this.registered_filters[this.filter_name].label === 'brands' ||
                                    this.registered_filters[this.filter_name].label === 'review_score' ||
                                    this.registered_filters[this.filter_name].label === 'categories') &&
                                    !this.show_all_data
                                    ? pool_item.default_article
                                    : pool_item
                            })
                            // Apply filters
                            .filter((pool_item) => {
                                return Array.isArray(pool_item.key)
                                    ? pool_item.key.includes(value)
                                    : pool_item.key === value
                            }).length

                        return { value, total }
                    })
                    // Filter out items which have no entry in the available pool.
                    // Filter out items which does not match internal_search.
                    // Keep items already selected.
                    .filter((item) => {
                        return (
                            (item.total > 0 &&
                                (!this.internalFilter ||
                                    item.value.toLowerCase().indexOf(this.internal_search.toLowerCase()) !== -1)) ||
                            (typeof this.filters[this.filter_name] !== 'undefined' &&
                                this.filters[this.filter_name].includes(item.value))
                        )
                    })
                    // Order by value or total
                    .sort(orderItemsBy(this.sort_by, this.sort_inverse))
            )
        },
        items() {
            return this.collection.slice(0, NUMBER_OF_ITEMS_TO_DISPLAY)
        },
        extended_items() {
            return this.collection.length > NUMBER_OF_ITEMS_TO_DISPLAY
                ? this.collection.slice(NUMBER_OF_ITEMS_TO_DISPLAY)
                : []
        },
        show_filter() {
            const internally_filtered = this.internalFilter && this.internal_search.length > 0

            // We hide the filter if:
            //    - there is no filter element and internal filter is inactive
            //    - there is 1 filter element and it's not checked and internal filter is inactive
            if (this.items.length === 0) {
                return internally_filtered
            } else if (this.items.length === 1) {
                if (
                    typeof this.filters[this.filter_name] === 'undefined' ||
                    this.filters[this.filter_name].length === 0
                ) {
                    return internally_filtered
                }
            }

            return true
        },
    },
    methods: {
        ...mapActions(['registerFilter', 'resetOneFilter']),
        toggle(expand) {
            this.expanded = expand
        },
        updatePool() {
            const field = this.registered_filters[this.filter_name].key
            const current_filter_name = this.filter_name
            const other_filters = Object.keys(this.filters).filter((key) => {
                return key !== current_filter_name
            })

            this.pool = applyFilter(this.filtered_articles, this.filters, this.registered_filters, other_filters).map(
                (article) => {
                    return {
                        key: article[field],
                        default_article: article.is_default_article,
                    }
                }
            )
        },
        getFilterKey(filter_name) {
            return this.registered_filters[filter_name].key
        },
    },
    created() {
        this.registerFilter([this.filter_name, this.label, this.search_key])
        this.updatePool()
    },
    components: { SearchFilterItem },
    watch: {
        filtered_articles: 'updatePool',
        filters: {
            handler() {
                this.updatePool()
            },
            deep: true,
        },
    },
}
</script>
