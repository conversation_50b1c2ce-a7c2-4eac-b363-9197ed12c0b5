<script setup>
import SectionTitle from '@/checkout_process_v2/components/SectionTitle.vue'
import AddressBook from '@/checkout_process_v2/components/AddressBook.vue'
import { computed, ref } from 'vue'
import { useCheckoutProcessStore, STEPS, STATE_STEPS } from '@/checkout_process_v2/stores/checkout_process'

const mode = ref(null)
const store = useCheckoutProcessStore()

function changeMode(change) {
    mode.value = change.to

    if (change.from === 'list' && ['update', 'create'].includes(change.to)) {
        store.saveStateThenNavigate({ step: STATE_STEPS.before_address_form })
    }
}

const title = computed(() => {
    if (mode.value === 'create') {
        return `Ajouter une nouvelle adresse`
    }
    if (store.current_step === STEPS.addresses_missing) {
        return `Sélectionner mon adresse`
    }
    if (store.edition_adresses_type === 'shipping') {
        return `Sélectionner mon adresse de livraison`
    }
    if (store.edition_adresses_type === 'billing') {
        return `Sélectionner mon adresse de facturation`
    }
})
</script>

<template>
    <div class="step-address">
        <section-title>{{ title }}</section-title>
        <address-book
            use-history
            @cancel:error="
                store.current_step === STEPS.addresses_missing
                    ? store.retrieveBasketOrder()
                    : store.setIsEditingAddress(false)
            "
            @create="store.selectAddress"
            @select="store.selectAddress"
            @update="store.selectAddress"
            @setDefaultAddress="store.selectAddress"
            @mode:change="changeMode"
        />
    </div>
</template>

<style scoped lang="scss">
@import '../../../Resources/scss/style';
.step-address {
    display: flex;
    flex-direction: column;
    gap: $space_32;
}
</style>
