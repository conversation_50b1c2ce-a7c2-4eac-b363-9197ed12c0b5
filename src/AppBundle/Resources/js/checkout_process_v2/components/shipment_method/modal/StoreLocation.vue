<script setup>
import MapStoreLocation from '@/checkout_process_v2/components/shipment_method/MapStoreLocation.vue'
import { computed, onUnmounted, ref } from 'vue'
const elementHeight = ref(window.innerHeight)

const props = defineProps({
    store: {
        type: Object,
        required: true,
    },
})

const get_map_height = computed(() => {
    return elementHeight.value - 63
})

function resize() {
    elementHeight.value = window.innerHeight
}

window.addEventListener('resize', resize)

onUnmounted(() => {
    window.removeEventListener('resize', resize)
})
</script>

<template>
    <div data-context="store-location">
        <map-store-location :store="store" template="v5" :prefer-height="get_map_height" />
    </div>
</template>
