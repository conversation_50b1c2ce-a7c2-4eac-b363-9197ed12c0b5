<script setup>
import SvdLink from '@/v5/components/link/SvdLink.vue'
import { computed } from 'vue'
import format from 'date-fns/format'
import datefns_locale_fr from 'date-fns/locale/fr'
import { isChronopostPreciseShipmentMethod } from '@/checkout_process_v2/shipment_methods'
const emit = defineEmits(['edit'])
import { capitalize, optionalChaining } from '@/shared/filters'

const CIVILITIES = { Mr: 'M.', Ms: 'Mme.' }

const props = defineProps({
    summaryDisplayed: {
        type: Boolean,
        required: true,
    },
    isReadOnly: {
        type: Boolean,
        default: false,
    },
    shipmentMethod: {
        type: Object,
        required: false,
    },
    shippingAddress: {
        type: Object,
        required: false,
    },
    countries: {
        type: Array,
        required: false,
        default: () => [],
    },
})

const formatted_civility = computed(() => {
    return CIVILITIES[props.shippingAddress?.title] || props.shippingAddress?.title
})

const delivery_address = computed(() => {
    if (props.shipmentMethod?.is_store) {
        return store_shipping_address.value
    }

    if (props.shipmentMethod?.is_relay) {
        return relay_shipping_address.value
    }

    return props.shippingAddress
})
const store_shipping_address = computed(() => {
    const store_address = props.shipmentMethod?.tags.store.extra_data.address

    return {
        ...props.shippingAddress,
        address: store_address.street,
        postal_code: store_address.zip_code,
        city: store_address.city,
        company_name: 'Son-Vidéo.com',
        country_name: 'FRANCE (MÉTROPOLITAINE)', // data does not exist in carrier.shipment_method_tag
    }
})
const relay_shipping_address = computed(() => {
    const pickup_location = props.shipmentMethod?.extra_data || {}
    const country = props.countries.find((c) => c.country_id === pickup_location.country_code)

    return {
        ...props.shippingAddress,
        ...pickup_location,
        country_name: country?.name ?? pickup_location.country_code,
        company_name: pickup_location.name,
    }
})
const formatted_address_lines = computed(() => delivery_address.value?.address.replace(/\n/g, ', '))
</script>

<template>
    <div data-context="delivery-summary" class="delivery-summary" :class="{ hide: !summaryDisplayed }">
        <div data-context="shipping-address">
            <div>
                <div>
                    {{ formatted_civility }}
                    {{ optionalChaining(delivery_address, ['firstname']) }}
                    {{ optionalChaining(delivery_address, ['lastname']) }}
                </div>
                <div
                    v-if="
                        optionalChaining(delivery_address, ['company_name']) &&
                        optionalChaining(delivery_address, ['company_name']).length > 0
                    "
                >
                    {{ optionalChaining(delivery_address, ['company_name']) }}
                </div>
            </div>
            <div>
                <div>{{ formatted_address_lines }}</div>
                <div>
                    {{ optionalChaining(delivery_address, ['postal_code']) }}
                    {{ optionalChaining(delivery_address, ['city']) }}
                </div>
                <div>
                    {{ capitalize(optionalChaining(delivery_address, ['country_name'])) }}
                </div>
            </div>
            <div>
                {{ capitalize(optionalChaining(delivery_address, ['cellphone'])) }}
            </div>
        </div>

        <svd-link
            discreet
            data-context="change-shipment"
            v-if="!isReadOnly"
            class="edit"
            label="Modifier"
            @click="$emit('edit')"
        />
    </div>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.delivery-summary {
    display: flex;
    flex-direction: column;
    margin-right: $space_32;
    margin-left: $space_32;
    margin-bottom: $space_32;
    gap: $space_16;
    opacity: 1;
    max-height: 500px;
    transition: $transition_opacity, $transition_height;
    &.hide {
        position: fixed;
        opacity: 0;
        max-height: 0;
        transition: $transition_opacity, $transition_height;
        margin-bottom: unset;
    }
    div {
        color: $color_grey_typo;
        font-size: $font_size_15;
        line-height: $height_24;
        &.title {
            color: $color_dark_default;
            font-weight: $font_semi_bold;
        }
    }
    .edit {
        color: $color_grey_typo;
        font-size: $font_size_15;
        line-height: $height_24;
        font-weight: $font_normal;
    }
}
</style>
