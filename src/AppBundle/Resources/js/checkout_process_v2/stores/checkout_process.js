import { computed, ref, watch } from 'vue'
import { defineStore } from 'pinia'
import api from '@/shared/api/handler'
import { useCountries } from '@/v5/composable/useCountries'
import { useAddressSuggestion } from '@/v5/composable/useAddressSuggestion'

// CONSTANTS
export const STEPS = {
    loading: 0,
    addresses_missing: 1,
    shipment_missing: 2,
    payment_selection: 3,
}
export const STATE_STEPS = {
    before_payment: 1,
    before_address_selection: 2,
    before_address_form: 3,
    before_delivery_edit: 4,
    before_contact_us_opening: 5,
}

// Initialization
const customer_order_id = computed(() => window.SonVideo?.customer_order_id ?? null)
const funnel_footer_data = computed(() => window.SonVideo.funnel_footer_data)
const show_order_created_message = computed(() => window.SonVideo?.show_order_created_message ?? false)
const has_failure_message = computed(() => window.SonVideo.has_failed)
const endpoints = computed(() => {
    const customer_order_endpoints =
        customer_order_id.value !== null
            ? {
                  get_basket_order: `/mon-compte/ma-commande/${customer_order_id.value}/resume-v2`,
                  get_payments: `/mon-compte/ma-commande/${customer_order_id.value}/liste-paiements`,
                  post_confirm_order: `/mon-compte/ma-commande/${customer_order_id.value}/confirmer`,
                  put_add_payment: `/mon-compte/ma-commande/${customer_order_id.value}/ajouter-paiement`,
                  put_add_svd_gift_card: `/mon-compte/ma-commande/${customer_order_id.value}/ajouter-carte-cadeau`,
              }
            : {}

    return {
        get_basket_order: '/ma-commande/v2',
        get_my_addresses: '/mon-compte/mes-adresses',
        get_payments: '/ma-commande/liste-paiements',
        post_confirm_order: '/ma-commande/v2/confirmer',
        put_add_payment: '/ma-commande/ajouter-paiement',
        put_add_svd_gift_card: '/ma-commande/ajouter-carte-cadeau',
        put_update_basket_order: '/ma-commande/mise-a-jour-panier',
        update_address: '/mon-compte/mes-adresses/mettre-a-jour/0',
        ...customer_order_endpoints,
    }
})

// === error message handling ===
const error_message = ref(null)
function updateErrorMessage(message) {
    error_message.value = message
}

// === Basket workflow ===
const basket_order = ref(null)
const is_loading = ref(true)
const has_data_failure = ref(false)
const is_loading_submit = ref(false)
const has_error = ref(false)
const shipment_method_info = ref(null)
const current_step = computed(() => {
    if (basket_order.value === null) {
        return STEPS.loading
    }
    if (basket_order.value.shipping_address === null || basket_order.value.billing_address === null) {
        return STEPS.addresses_missing
    }
    if (basket_order.value.shipment_method === null) {
        return STEPS.shipment_missing
    }

    return STEPS.payment_selection
})
watch(
    current_step,
    async (new_value, old_value) => {
        if (old_value === STEPS.shipment_missing && new_value === STEPS.payment_selection) {
            saveStateThenNavigate({ step: STATE_STEPS.before_payment })
        }

        if (new_value === STEPS.addresses_missing) {
            await retrieveCustomerAddresses()
        }

        if (new_value === STEPS.payment_selection) {
            await fetchPayments()
        }
    },
    { immediate: true }
)

async function retrieveCustomerAddresses() {
    try {
        is_loading.value = true
        has_data_failure.value = false
        const response = await api.get(endpoints.value.get_my_addresses)

        const first_valid_address = response.addresses.find((address) => address.is_valid)
        if (first_valid_address) {
            if (current_step.value === STEPS.payment_selection) {
                await updateAdresses({
                    billing_address: first_valid_address,
                })
            } else {
                await updateAdresses({
                    billing_address: first_valid_address,
                    shipping_address: first_valid_address,
                })
            }
            setIsEditingAddress(false)
        }
    } catch (e) {
        has_data_failure.value = true
    } finally {
        is_loading.value = false
    }
}
async function updateBasketOrder(updates) {
    const shipment_method = basket_order.value?.shipment_method
    if (shipment_method?.extra_data) {
        shipment_method.selected_data = shipment_method.extra_data
    }

    const payload = {
        billing_address: basket_order.value?.billing_address,
        shipping_address: basket_order.value?.shipping_address,
        shipment_method,
        payments: payments.value,
        ...updates,
    }
    is_loading_submit.value = true
    await api
        .put(endpoints.value.put_update_basket_order, payload)
        .then(() => {
            has_error.value = false
        })
        .catch(() => {
            has_error.value = true
        })
        .finally(async () => {
            is_loading_submit.value = false
            await retrieveBasketOrder()
        })
}
async function retrieveBasketOrder() {
    try {
        is_loading.value = true
        has_data_failure.value = false

        basket_order.value = await api.get(endpoints.value.get_basket_order)

        if (basket_order.value.billing_address) {
            Object.assign(basket_order.value.billing_address, {
                country_name:
                    basket_order.value.countries.find(
                        (c) => c.country_id === basket_order.value.billing_address.country_code
                    )?.name ?? basket_order.value.billing_address.country_code,
            })
        }
        if (basket_order.value.shipping_address) {
            Object.assign(basket_order.value.shipping_address, {
                country_name:
                    basket_order.value.countries.find(
                        (c) => c.country_id === basket_order.value.shipping_address.country_code
                    )?.name ?? basket_order.value.shipping_address.country_code,
            })
        }

        if (current_step.value === STEPS.payment_selection && has_initial_load.value) {
            fetchPayments()
        }
    } catch (e) {
        has_data_failure.value = true
    } finally {
        is_loading.value = false
    }
}

const finalize_process_url = ref(null)
async function submitPaymentAndConfirm(with_payload, skip_submission) {
    if (is_loading_submit.value) {
        return
    }
    selected_payment_id.value = with_payload.payment_method_id
    try {
        is_loading_submit.value = true
        updateErrorMessage(null)
        if (!skip_submission) {
            await api.put(endpoints.value.put_add_payment, with_payload)
        }

        const response = await api.post(endpoints.value.post_confirm_order)
        if (response.status !== 'success') {
            throw 'Status not success'
        }

        finalize_process_url.value = response.url
    } catch (e) {
        updateErrorMessage(
            `Désolés, une erreur est survenue lors de la validation du paiement. Merci de nous contacter ou de ré-essayer ultérieurement.`
        )
    } finally {
        is_loading_submit.value = false
    }
}

// === Payments handling ===
const has_initial_load = ref(false)
const is_loading_payments = ref(true)
const payments = ref([])
const selected_payment_id = ref(null)
async function fetchPayments() {
    try {
        has_initial_load.value = true
        is_loading_payments.value = true
        payments.value = await api.get(endpoints.value.get_payments)

        // set first payment as selected
        if (null === selected_payment_id.value && !is_loading_submit.value) {
            selected_payment_id.value = payments.value?.[0]?.payment_method_id
        }
    } catch (e) {
        payments.value = []
    } finally {
        is_loading_payments.value = false
    }
}

// === Addresses handling ===
const is_editing_address = ref(null)
function setIsEditingAddress(value) {
    is_editing_address.value = value
}
const edition_adresses_type = ref(null)
function editAddress(payload) {
    saveStateThenNavigate({ step: STATE_STEPS.before_address_selection })
    setIsEditingAddress(true)
    edition_adresses_type.value = payload.name
}
async function selectAddress(address) {
    let updates = {}
    if ('billing' === edition_adresses_type.value) {
        updates.billing_address = address
    } else if ('shipping' === edition_adresses_type.value) {
        updates.shipping_address = address
        updates.shipment_method = null
    } else {
        updates.shipping_address = address
        updates.billing_address = address
        updates.shipment_method = null
    }
    await updateAdresses(updates)

    setIsEditingAddress(false)
}
async function updateAdresses(updates) {
    try {
        await updateBasketOrder(updates)
    } catch (e) {
        console.warn('address update silently fail => addresses are reloaded no matter what')
    } finally {
        await retrieveBasketOrder()
    }
}

// === Shipment Method handling
const initial_selected_shipment_method = ref(null)
async function selectShipmentMethod(shipment_method) {
    await updateBasketOrder({ shipment_method })
    await retrieveBasketOrder()
    setInitialSelectedShipmentMethod(shipment_method)
}
function resetShipmentMethod(should_save_step = false) {
    if (should_save_step) {
        saveStateThenNavigate({
            step: STATE_STEPS.before_delivery_edit,
            payload: { shipment_method: Object.assign({}, basket_order.value.shipment_method) },
        })
    }
    setInitialSelectedShipmentMethod()
    setShipmentMethod(null)
}
function setShipmentMethod(value) {
    basket_order.value.shipment_method = value
}
function setInitialSelectedShipmentMethod(value) {
    if (value) {
        initial_selected_shipment_method.value = value
    } else {
        initial_selected_shipment_method.value = Object.assign({}, basket_order.value.shipment_method)
    }
}

//
// Other GETTERS
//
const is_quote = computed(() => basket_order.value?.quote?.type === 'quotation')
const is_locked_to_payment = computed(() => is_quote.value || customer_order_id.value !== null)
const shipment_cost = computed(() => {
    if (!basket_order.value?.shipment_method) {
        return null
    }

    return basket_order.value.shipment_method.cost
})
const total_paid_amount = computed(() =>
    (basket_order.value?.payments ?? []).reduce((sum, payment) => sum + payment.amount, 0)
)

const total_price = computed(
    () => basket_order.value?.articles_price + (shipment_cost.value ?? 0.0) - total_paid_amount.value
)
const is_partially_paid = computed(() => total_paid_amount.value > 0 && total_paid_amount.value < total_price.value)
const shipment_method = computed(() => basket_order.value?.shipment_method)
const billing_address = computed(() => basket_order.value?.billing_address)
const shipping_address = computed(() => basket_order.value?.shipping_address)
const quote = computed(() => (is_quote.value ? basket_order.value.quote : null))

//
// Other ACTIONS
//
function saveStateThenNavigate(state) {
    window.history.replaceState(state, '')
    window.history.pushState(null, '')
}

// === Shipping Address Validation/Suggestions
const { isMetropolitanFrance } = useCountries()
const country_code = computed(() => basket_order.value.shipping_address.country_code)
const { raw_features: features_with_zipcode, fetchAddressSuggestions: fetchAddressSuggestionsWithZipCode } =
    useAddressSuggestion(country_code)
const { raw_features: features_without_zipcode, fetchAddressSuggestions: fetchAddressSuggestionsWithoutZipCode } =
    useAddressSuggestion(country_code)
const addressSuggestionList = computed(() => {
    return features_with_zipcode.value
        .concat(features_without_zipcode.value)
        .filter((f1, idx, features) => features.findIndex((f2) => f2.properties.id === f1.properties.id) === idx)
})
const is_shipping_address_suggested = computed(
    () =>
        !!addressSuggestionList.value.find((suggestion) => {
            const shipping_address = basket_order.value.shipping_address
            const formatted_shipping_address = formatString(
                `${shipping_address.address.split('\n')[0]} ${shipping_address.postal_code} ${shipping_address.city}`
            )

            return (
                formatString(
                    `${suggestion.properties.housenumber} ${suggestion.properties.street} ${suggestion.properties.postcode} ${suggestion.properties.city}`
                ) === formatted_shipping_address
            )
        })
)
watch(
    () => basket_order.value?.shipping_address,
    (newest, old) => {
        if (hasShippingAddressChange(old, newest)) {
            features_with_zipcode.value = []
            features_without_zipcode.value = []
            verifyAddress()
        }
    },
    { immediate: true }
)
function hasShippingAddressChange(old, newest) {
    if (!old && newest) {
        return true
    }

    return old && newest && ['address', 'postal_code', 'city', 'country_code'].some((key) => old[key] !== newest[key])
}
function formatString(value) {
    return value
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()
}

function isBold(reference, chaine, forceUpperCase = false) {
    const refWords = formatString(reference).split(' ')
    const chaineWords = formatString(chaine).split(' ')

    let resultat = ''
    for (let i = 0; i < refWords.length || i < chaineWords.length; i++) {
        let refWord = refWords[i] || ''
        let chaineWord = chaineWords[i] || ''

        if (forceUpperCase) {
            chaineWord = chaineWord.toUpperCase()
            refWord = refWord.toUpperCase()
        }

        if (refWord === chaineWord) {
            resultat += chaineWord + ' '
        } else {
            resultat += '<span class="spelling-error">' + chaineWord + '</span> '
        }
    }

    return resultat.trim()
}
function compareStrings(reference, compare) {
    return `${isBold(reference.address, compare.housenumber + ' ' + compare.street)} ${isBold(
        reference.zip_code,
        compare.postcode
    )} ${isBold(reference.city, compare.city, true)}`
}

const showAddressSuggestion = ref(false)
async function verifyAddress() {
    showAddressSuggestion.value = false

    if (!isMetropolitanFrance(country_code.value) || is_quote.value) {
        return
    }

    await fetchAddressSuggestionsWithZipCode(
        basket_order.value.shipping_address.address.split('\n')[0],
        basket_order.value.shipping_address.postal_code,
        1
    )
    await fetchAddressSuggestionsWithoutZipCode(basket_order.value.shipping_address.address.split('\n')[0], '', 1)
    showAddressSuggestion.value = !is_shipping_address_suggested.value
}
function setShipmentMethodInfo(value = null) {
    shipment_method_info.value = value
}

export const useCheckoutProcessStore = defineStore('checkout-process', () => ({
    // STATE
    basket_order,
    is_editing_address,
    initial_selected_shipment_method,
    payments,
    selected_payment_id,
    is_loading_payments,
    has_error,
    is_loading,
    has_data_failure,
    finalize_process_url,
    error_message,
    edition_adresses_type,
    shipment_method_info,
    // GETTERS
    customer_order_id,
    funnel_footer_data,
    show_order_created_message,
    has_failure_message,
    endpoints,
    current_step,
    is_quote,
    is_locked_to_payment,
    shipment_cost,
    total_price,
    is_partially_paid,
    shipment_method,
    billing_address,
    shipping_address,
    quote,
    showAddressSuggestion,
    addressSuggestionList,
    // ACTIONS
    setShipmentMethod,
    setIsEditingAddress,
    setInitialSelectedShipmentMethod,
    retrieveBasketOrder,
    resetShipmentMethod,
    saveStateThenNavigate,
    submitPaymentAndConfirm,
    updateErrorMessage,
    editAddress,
    selectAddress,
    selectShipmentMethod,
    compareStrings,
    setShipmentMethodInfo,
}))
