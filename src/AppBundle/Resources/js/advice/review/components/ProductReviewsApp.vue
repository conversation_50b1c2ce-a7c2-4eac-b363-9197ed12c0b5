<template>
    <div ref="reviews">
        <div v-if="initialization_status !== 'loaded'">{{ 'message.loading' | trans }}</div>
        <product-reviews-empty v-else-if="reviews.length === 0"></product-reviews-empty>
        <product-reviews-list :article_url="article_url" v-else></product-reviews-list>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import ProductReviewsEmpty from './ProductReviewsEmpty'
import ProductReviewsList from './ProductReviewsList'

export default {
    name: 'ProductReviewsApp',
    computed: {
        ...mapState(['reviews', 'initialization_status']),
    },
    props: {
        article_url: {
            type: String,
            required: true,
        },
    },
    components: { ProductReviewsEmpty, ProductReviewsList },
}
</script>
