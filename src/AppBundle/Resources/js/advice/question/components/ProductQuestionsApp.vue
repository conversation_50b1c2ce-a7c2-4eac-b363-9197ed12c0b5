<template>
    <div>
        <div v-if="initialization_status !== 'loaded'">{{ 'message.loading' | trans }}</div>
        <questions-groups-list></questions-groups-list>
        <div class="row flex-column flex-md-row pb-3 no-border gap-2">
            <div class="align-self-center">
                <svd-button
                    label="Poser une question"
                    :href="ask_question_link"
                    color="primary"
                />
            </div>
            <div class="align-self-center" v-if="questions_displayed.length !== 0">
                <svd-button
                    :href="all_questions_link"
                    color="primary"
                    type="outline"
                    label="Voir toutes les questions/réponses"
                />
            </div>
        </div>
        <product-questions-list
            :sorting_options="sorting_options"
            :selected_sorting_option="selected_sorting_option"
            :search_query="search_query"
            :pager="pager"
            :questions_displayed="questions_displayed"
        ></product-questions-list>
    </div>
</template>

<script>
import ProductQuestionsList from './ProductQuestionsList'
import QuestionsGroupsList from './QuestionsGroupsList'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import { mapGetters, mapState } from 'vuex'

export default {
    props: {
        ask_question_link: {
            type: String,
            required: true,
        },
        all_questions_link: {
            type: String,
            required: true,
        },
    },
    computed: {
        ...mapState(['initialization_status', 'sorting_options', 'selected_sorting_option', 'search_query']),
        ...mapGetters(['pager', 'questions_displayed']),
    },
    components: { ProductQuestionsList, QuestionsGroupsList, SvdButton },
}
</script>
