export default {
    props: {
        id: Number,
        can_be_removed: <PERSON><PERSON><PERSON>,
        prototype: String,
        delete_label: String,
        config: Object,
    },
    computed: {
        // Computed component template based on the prototype (from Twig)
        rendered_prototype() {
            let newForm = this.prototype
            newForm = newForm.replace(/__name__/g, this.id)

            return {
                template: newForm,
            }
        },
        // Each wrapper component should compute its errors based on its conf and data, to expose them
        errors() {
            return []
        },
    },
    methods: {
        /**
         * Ask parent component to delete the present component
         */
        deleteMe() {
            this.$emit('delete', this.id)
        },
        /**
         * Expose errors from the present component to its parent
         */
        reportErrors() {
            this.$emit('errors', { id: this.id, errors: this.errors })
        },
    },
    mounted() {
        // exposes errors right after fully initialized
        this.reportErrors()
    },
    watch: {
        errors: {
            handler() {
                // expose errors when a change occurs
                this.reportErrors()
            },
        },
    },
}
