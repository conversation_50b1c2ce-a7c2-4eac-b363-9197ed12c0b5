<template>
    <div class="d-flex flex-column align-items-center">
        <div
            class="section-bordered squared-placeholder d-flex flex-column justify-content-between align-items-center clickable"
            :class="{ 'has-errors': errors.length > 0 }"
            @click.prevent="browse"
        >
            <template v-if="!file">
                <span class="d-flex flex-column justify-content-center align-items-center flex-grow"
                    ><i class="icon icon-zoom icon-4x"></i
                ></span>
                <span class="text-on-bottom">{{ trans('open_file', {}, 'component') }}</span>
            </template>
            <div v-else class="associated-product-preview d-flex flex-column justify-content-center flex-grow">
                <img v-if="is_picture" :src="thumbnail" alt="" />
                <span v-else>{{ file.name }}</span>
            </div>
            <a v-if="file" href="" class="text-on-bottom" @click.prevent="deleteMe">{{ delete_label }}</a>
        </div>

        <component ref="upload_input" style="display: none" :is="rendered_prototype" @change.native="onChange" />
    </div>
</template>

<script>
import filesize from 'filesize'
import { trans } from '../../filters'
import WrapperMixin from './WrapperMixin'

export default {
    mixins: [WrapperMixin],
    data() {
        return {
            thumbnail: null,
            file: null,
        }
    },
    computed: {
        // Internal configuration based on default values and config prop
        conf() {
            return Object.assign(
                {
                    auto_open: true, // whether or not the file chooser is open on creation
                },
                this.config
            )
        },
        // Define if the internal file is a picture or not
        is_picture() {
            return this.file && /^image\//.test(this.file.type)
        },
        // Detected errors on the input file, based on conf
        errors() {
            let errors = []

            // check required file
            if (this.conf.required && !this.file) {
                errors.push(trans('file_required', {}, 'component'))
            }

            if (this.file) {
                // check file type
                if (this.conf.authorized_types && this.conf.authorized_types.indexOf(this.file.type) < 0) {
                    errors.push(
                        trans(
                            'file_type_unauthorized',
                            {
                                type: this.file.type,
                                authorized_types: this.conf.authorized_types.join(', '),
                            },
                            'component'
                        )
                    )
                }

                // check file size
                if (this.conf.max_file_size && this.file.size > this.conf.max_file_size) {
                    errors.push(
                        trans(
                            'file_too_big',
                            {
                                size: filesize(this.file.size),
                                limit: filesize(this.conf.max_file_size),
                            },
                            'component'
                        )
                    )
                }
            }

            return errors
        },
    },
    methods: {
        trans,
        /**
         * Trigger click on the wrapped input
         */
        browse() {
            if (this.$refs.upload_input) {
                this.$refs.upload_input.$el.click()
            }
        },
        /**
         * Handle file change in the wrapped input
         *
         * @param e Change event
         */
        onChange(e) {
            this.file = e.target.files[0]

            // update thumbnail
            const reader = new FileReader()
            reader.onload = (e) => {
                this.thumbnail = e.target.result
            }
            reader.readAsDataURL(this.file)
        },
    },
    mounted() {
        // automatically open the file chooser
        if (this.conf.auto_open === true) {
            this.browse()
        }
    },
}
</script>
