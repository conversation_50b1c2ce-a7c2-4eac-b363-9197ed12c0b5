import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/shared/api/handler'

const customer_info = ref(null)
const quotes_info = ref(null)

export const useCustomerInfoStore = defineStore('customer_info', () => {
    const fetchInfo = async () => {
        try {
            const response = await api.get('/api/customer-info')

            customer_info.value = response?.user ?? null
            quotes_info.value = response?.quotes ?? null

            sessionStorage.setItem('features', JSON.stringify(response?.features ?? []))
        } catch (Error) {
            // do nothing
        }
    }

    // on instantiation
    if (null === customer_info.value) {
        void fetchInfo()
    }

    return { customer_info, quotes_info }
})
