<template>
    <td class="center" :class="[delivery_slot ? 'available-slot' : 'unavailable-slot']" data-context="slot">
        <input type="radio" name="delivery_slot_radio" :checked="is_selected" v-if="delivery_slot" @input="onSelect" />
    </td>
</template>

<script>
export default {
    name: 'ShipmentMethodChronopostPreciceSlotInput',
    props: {
        value: {
            type: Object || null,
        },
        delivery_slot: {
            type: Object || null,
        },
    },
    computed: {
        is_selected() {
            return this.value && this.delivery_slot
                ? this.value.deliverySlotCode === this.delivery_slot.deliverySlotCode
                : false
        },
    },
    methods: {
        onSelect() {
            this.$emit('input', this.delivery_slot)
        },
    },
}
</script>
