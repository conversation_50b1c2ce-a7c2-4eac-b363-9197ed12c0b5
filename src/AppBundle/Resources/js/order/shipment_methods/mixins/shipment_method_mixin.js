export default {
    props: {
        /**
         * Shipment method to display/handle
         */
        shipment_method: {
            type: Object,
            required: true,
        },
        /**
         * Currently selected shipment method id
         */
        selected_shipment_method_id: {
            required: true,
        },
        /**
         * The customer delivery address for the customer
         */
        delivery_address: {
            type: Object,
        },
        /**
         * Extra data for shipment method
         */
        extra_data: {
            type: Object,
        },
        /**
         * The weight in gram on the overall shipment
         */
        shipment_weight: {
            type: Number,
        },
        /**
         * Shipment ids eligible list, used for control with multiple/shared handlers (eg: Colissimo pickup)
         */
        available_shipment_method_ids: {
            type: Array,
        },
    },
    computed: {
        is_selected() {
            return this.shipment_method.shipment_method_id === this.selected_shipment_method_id
        },
        is_recommended() {
            return !!this.shipment_method?.tags?.recommended
        },
        /**
         * Return boolean if shipment is guaranteed before christmas
         *
         * @returns {boolean}
         */
        before_christmas() {
            const shipment_method_dates = {
                13: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-21T11:59:59+01:00' }, // Novea Express
                15: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-22T11:59:59+01:00' }, // Chronopost (Chrono13)
                16: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-22T11:59:59+01:00' }, // Chronopost pickup
                24: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-19T11:59:59+01:00' }, // Mondial Relay
                35: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-21T11:59:59+01:00' }, // France Express
                65: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-21T11:59:59+01:00' }, // GLS
                68: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-22T11:59:59+01:00' }, // Chronopost sur RDV
                75: { start: '2022-12-01T00:00:00+01:00', end: '2022-12-20T11:59:59+01:00' }, // Novea Premium
            }
            const date_now = new Date()
            return (
                shipment_method_dates.hasOwnProperty(this.shipment_method.shipment_method_id) &&
                date_now > new Date(shipment_method_dates[this.shipment_method.shipment_method_id].start) &&
                date_now < new Date(shipment_method_dates[this.shipment_method.shipment_method_id].end)
            )
        },
    },
    methods: {
        /**
         * Default triggered Vue event when the shipment method is clicked
         */
        onClick() {
            this.$emit('shipment_method:click', this.shipment_method)
        },
        /**
         * Event emitted when shipment method extra data fro current carrier needs to be updated in parent application
         */
        whenExtraDataIsUpdated(extra_data) {
            this.$emit('shipment_method:extra_data_updated', [this.shipment_method.carrier_id, extra_data])
        },
        /**
         * Event emitted the we need to update a selected shipment method [key, value]
         */
        updateSelectedShipmentMethod([key, value]) {
            this.$emit('shipment_method:update', [key, value])
        },
        resetExtraData() {
            this.$emit('shipment_method:reset')
        },
    },
}
