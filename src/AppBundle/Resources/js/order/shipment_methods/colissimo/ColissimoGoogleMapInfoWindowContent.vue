<template>
    <div v-if="location">
        <p class="SVDv3_soCol_pointRetrait_titre iwc">{{ location.name }}</p>

        <p v-if="location.on_leave" class="SVDv3_soCol_pointRetrait_lien socol_rel_lnk">{{ 'on_leave' | trans }}</p>
        <p v-else="" class="SVDv3_soCol_pointRetrait_lien socol_rel_lnk">
            <a aria-label="Choisir ce lieu" class="btn btn-primary btn-sm" @click.prevent="submit"
                ><span>{{ 'select_this_location' | trans({}, 'order') }}</span></a
            >
        </p>

        <div class="SVDv3_soCol_pointRetrait_horaires">
            <table>
                <thead>
                    <tr>
                        <th>{{ 'day' | trans }}</th>
                        <th colspan="2">{{ 'opening_hours' | trans }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="day in location.opening_hours" v-html="formatDayLine(day)"></tr>
                </tbody>
            </table>
        </div>

        <p
            v-if="location.disabled_person"
            class="SVDv3_soCol_pointRetrait_handicap socol_handicap SVDv3_texteGris text-sm"
        >
            {{ 'with_disabled_access' | trans({}, 'order') }}
        </p>
    </div>
</template>

<script>
import { trans } from '@/shared/filters'

export default {
    props: {
        location: Object,
    },
    methods: {
        submit() {
            this.$emit('submit')
        },
        /**
         * return content of a line formatted in HTML
         * @param day
         * @return {string}
         */
        formatDayLine(day) {
            let result = `<td>${trans(day.day)}</td>`

            const hours = day.hours.split(' ')
            if (hours[0] === '00:00-00:00' && hours[1] === '00:00-00:00') {
                result += `<td colspan='2'>${trans('closed')}</td>`
            } else if (hours[0] !== '00:00-00:00' && hours[1] === '00:00-00:00') {
                result += `<td colspan='2'>${hours[0]}</td>`
            } else {
                result += `<td>${hours[0]}</td><td>${hours[1]}</td>`
            }

            return result
        },
    },
}
</script>
