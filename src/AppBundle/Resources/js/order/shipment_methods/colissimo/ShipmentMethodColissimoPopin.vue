<template>
    <div class="text-left">
        <!-- Search form (address ajustable by the customer) -->
        <div id="proximiteRelay">
            <div class="d-flex">
                <div class="d-none d-lg-block mr-2">
                    <img
                        :src="'/images/ui/icones/ui_icones_livraison_socolissimo.gif' | asset('static_images')"
                        alt="SoColissimo."
                    />
                </div>
                <div class="d-flex flex-column">
                    <p class="mb-2">{{ 'enter_a_location_near_your_wanted_pickup_location' | trans({}, 'order') }}</p>
                    <div class="d-block d-md-flex">
                        <input
                            type="text"
                            class="w-100 w-md-auto mr-md-2 mb-2 mb-md-0"
                            name="sc_street"
                            id="sc_street"
                            :placeholder="'street' | trans({}, 'account')"
                            v-model="search.street"
                        />
                        <input
                            type="text"
                            class="w-100 w-md-auto mr-md-2 mb-2 mb-md-0"
                            name="sc_postalcode"
                            id="sc_postalcode"
                            :placeholder="'account_addresses_postal_code' | trans({}, 'account')"
                            v-model="search.postal_code"
                        />
                        <input
                            type="text"
                            class="w-100 w-md-auto mr-0 mr-md-2 mb-2 mb-md-0"
                            name="sc_city"
                            :placeholder="'account_addresses_city' | trans({}, 'account')"
                            v-model="search.city"
                        />
                        <a
                            aria-label="Valider"
                            class="btn btn-default btn-sm"
                            @click.prevent="checkNearLocationAndUpdate"
                        >
                            <span>{{ 'Valider' | trans }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_error" v-if="error_message">
            <p>{{ error_message }}</p>
        </div>

        <!-- Relay point selection -->
        <div class="d-flex flex-column flex-md-row map-wrapper">
            <div v-if="loading" class="loading"></div>

            <!-- Map with markers -->
            <gmap-map ref="map" id="map_canvas" :center="gmap_settings.center" :zoom="gmap_settings.zoom">
                <gmap-info-window
                    :options="infowindow_options"
                    :position="infowindow_position"
                    :opened="show_infowindow"
                    @closeclick="show_infowindow = !show_infowindow"
                >
                    <colissimo-google-map-info-window-content
                        :location="selected_pickup_location_details"
                        @submit="submitLocation(selected_pickup_location_details)"
                    ></colissimo-google-map-info-window-content>
                </gmap-info-window>
                <gmap-marker
                    v-for="(marker, i) in markers"
                    :key="i"
                    :position="marker.position"
                    :icon="marker.icon"
                    :clickable="true"
                    @click="toggleInfoWindow(marker.relay_id)"
                ></gmap-marker>
            </gmap-map>

            <!-- Pickup list on the right -->
            <div class="d-flex flex-column pickup-selector-popin-column">
                <ul id="panTo" class="pickup-location">
                    <li
                        class="d-flex flex-column p-2"
                        v-for="address in pickup_locations"
                        :class="address.relay_id === selected ? 'pointRetrait_Selected' : 'pointRetrait_notSelected'"
                        @click.prevent="toggleInfoWindow(address.relay_id)"
                    >
                        <div class="nom"><img :src="getImage(address)" />{{ address.name }}</div>
                        <div class="adresse">{{ address.address }}</div>
                        <div class="adresse">{{ address.postal_code }} {{ address.city }}</div>
                    </li>
                </ul>

                <div
                    id="boutonChoisir_pointRetrait"
                    class="p-2 text-center"
                    v-if="selected_pickup_location_details && !selected_pickup_location_details.on_leave"
                >
                    <a
                        aria-label="Choisir ce lieu"
                        class="btn btn-primary w-100"
                        @click.prevent="submitLocation(selected_pickup_location_details)"
                    >
                        <span>{{ 'select_this_location' | trans({}, 'order') }}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Vue from 'vue'
import api from '@/shared/api/handler'
import { asset, trans } from '@/shared/filters'
import { isPostalCodeValid } from '@/shared/functions'
import ColissimoGoogleMapInfoWindowContent from './ColissimoGoogleMapInfoWindowContent'

const location_images = {
    2: asset('/images/ui/icones/icons_socol_map_city.png', 'static_images'),
    3: asset('/images/ui/icones/icons_socol_map.png', 'static_images'),
    4: asset('/images/ui/icones/icons_socol_map_comm.png', 'static_images'),
    7: asset('/images/ui/icones/icons_socol_map.png', 'static_images'),
    8: asset('/images/ui/icones/icons_socol_map_comm.png', 'static_images'),
}

export default {
    props: {
        shipment_method: {
            type: Object,
            required: true,
        },
        delivery_address: {
            type: Object,
            required: true,
        },
        shipment_weight: {
            type: Number,
            required: true,
        },
        available_shipment_method_ids: {
            type: Array,
            required: true,
        },
        selected_data: {
            type: Object,
            required: true,
        },
    },
    components: { ColissimoGoogleMapInfoWindowContent },
    data() {
        return {
            loading: false,
            error_message: null,
            search: {
                city: null,
                street: null,
                postal_code: null,
                country_code: null,
                weight: null,
            },
            bounds: null,
            markers: [],
            // Default centered on Champigny warehouse
            gmap_settings: {
                zoom: 13,
                center: {
                    lat: 48.8109,
                    lng: 2.54517,
                },
            },
            pickup_locations: [],
            selected: null,
            show_infowindow: false,
            infowindow_position: null,
            infowindow_options: {
                pixelOffset: {
                    width: 0,
                    height: -35,
                },
            },
        }
    },

    computed: {
        /**
         * Return the selected pickup location from the available list
         */
        selected_pickup_location_details() {
            return this.selected !== null ? this.pickup_locations.find((pl) => pl.relay_id === this.selected) : null
        },
    },
    methods: {
        /**
         * Select a pickup location and toggle the marker info window if needed
         */
        toggleInfoWindow(relay_id) {
            // If it's the same marker just toggle the info window
            this.show_infowindow = this.selected === relay_id ? !this.show_infowindow : true
            if (this.show_infowindow) {
                const marker = this.markers.find((m) => m.relay_id === relay_id)
                this.selected = marker.relay_id
                this.infowindow_position = marker.position
                // Center map to marker position
                this.$refs.map.panTo(marker.position)
            }
        },
        /**
         * Make an api call to fetch the list of nearest available pickup locations
         */
        loadPickupLocations() {
            this.loading = true
            this.show_infowindow = false
            this.error_message = null
            this.selected = null
            // remove all previous markers & pickup locations
            this.markers = []
            this.pickup_locations = []

            // Call to api_pickup_so_colissimo_get
            api.get('/api/points-retrait/so-colissimo', this.search)
                .then((data) => {
                    // Create new boundaries for the received result set
                    this.bounds = new google.maps.LatLngBounds()
                    // add markers for each address
                    this.pickup_locations = data.filter((address) =>
                        this.available_shipment_method_ids.includes(parseInt(address.method_id))
                    )
                    this.loading = false

                    if (this.pickup_locations.length === 0) {
                        this.error_message = trans('no_eligible_pickup_location_found_near_this_address', {}, 'order')
                    }

                    this.pickup_locations.forEach((address) => {
                        this.addMarker(address)
                    })

                    // fit map to all added boundaries
                    this.$refs.map.fitBounds(this.bounds)
                })
                .catch(() => {
                    this.error_message = trans('no_eligible_pickup_location_found_near_this_address', {}, 'order')
                    this.loading = false
                })
        },
        /**
         * Add markers to the loaded canvas map
         */
        addMarker(address) {
            const position = { lat: parseFloat(address.coords.lat), lng: parseFloat(address.coords.long) }
            // add current address within map boundaries
            this.bounds.extend(position)
            // add the marker config for current address
            this.markers.push({
                position,
                name: address.name,
                icon: location_images[address.method_id],
                relay_id: address.relay_id,
            })
        },
        /**
         * Return the pictogram of the address based on its type
         * @param address
         * @return {string|undefined}
         */
        getImage(address) {
            return location_images[address.method_id]
        },
        /**
         * Submit a selected address
         * @param address
         */
        submitLocation(address) {
            // Notify parent about the currebntly used shipment method id
            this.$emit('update', ['shipment_method_id', parseInt(address.method_id)])
            // And update the address in extra data
            this.$emit('submit', address)
        },
        /**
         * Check near location data and update map if ok
         */
        checkNearLocationAndUpdate() {
            this.error_message = null

            // check provided data are well formatted
            if (!isPostalCodeValid(this.search.postal_code, this.search.country_code)) {
                this.error_message = trans('enter_valid_zip_code', {}, 'order')

                return
            }
            if (!this.search.city) {
                this.error_message = trans('city_name_is_mandatory', {}, 'order')

                return
            }

            // if ok, update
            this.loadPickupLocations()
        },
        mapDeliveryAddressToForm({ city, address, postal_code, country_code }, weight, selected_data) {
            const search = Object.assign(
                {},
                {
                    city: selected_data.city || city,
                    street: selected_data.address || address,
                    postal_code: selected_data.postal_code || postal_code,
                    country_code: selected_data.country_code || country_code,
                }
            )
            search.weight = weight

            Object.keys(search).forEach((key) => {
                Vue.set(this.search, key, search[key])
            })
        },
    },
    mounted() {
        this.mapDeliveryAddressToForm(this.delivery_address, this.shipment_weight, this.selected_data)

        // Wait until gmap is loaded
        this.$refs.map.$mapPromise.then(() => {
            this.loadPickupLocations()
        })
    },
}
</script>
