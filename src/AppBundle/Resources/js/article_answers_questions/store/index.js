import Vue from 'vue'
import Vuex from 'vuex'
import * as types from './mutation-types'
import { actions } from './actions'
import { flattenAnswers, answersContainTerm } from '../../advice/question/functions'
import { generateSortingFromDefinition } from '../../shared/functions'

Vue.use(Vuex)

const debug = process.env.NODE_ENV !== 'production'

export default new Vuex.Store({
    state: {
        urls: {},
        questions: [],
        displayed_questions_count: 0,
        questions_per_page: 5,
        page: 1,
        answers_associated_products: [],
        sorting_options: [
            {
                label: 'most_answered',
                sort_direction: 'desc',
                sort_key: 'answers',
                key_type: 'array',
            },
            {
                label: 'without_answers',
                sort_direction: 'asc',
                sort_key: 'answers',
                key_type: 'array',
            },
            {
                label: 'latest',
                sort_direction: 'desc',
                sort_key: 'created_at',
                key_type: 'date',
            },
            {
                label: 'most_useful',
                sort_direction: 'desc',
                sort_key: 'vote_useful',
                key_type: 'float',
            },
        ],
        selected_sorting_option: 3,
        search_query: '',
    },
    getters: {
        // questions with the answers tree flattened
        flattened_questions: (state) =>
            state.questions.map((question) =>
                Object.assign({}, question, {
                    answers: flattenAnswers(question.answers, []),
                })
            ),
        // exploded search query strings
        search_strings: (state) => {
            return state.search_query
                .split(/(\s+)/)
                .map((word) => word.toLowerCase().trim())
                .filter((word) => word.length > 0)
        },
        // questions flattened where one or more search strings appear
        filtered_questions: (state, getters) => {
            return getters.search_strings.length === 0
                ? getters.flattened_questions
                : getters.flattened_questions.filter((question) => {
                      // search in titles and bodies
                      for (let i = 0; i < getters.search_strings.length; i++) {
                          if (
                              question.title.toLowerCase().indexOf(getters.search_strings[i]) !== -1 ||
                              question.question.toLowerCase().indexOf(getters.search_strings[i]) !== -1
                          ) {
                              return true
                          }
                      }

                      // search in answers, recursively
                      return question.answers.length > 0 && answersContainTerm(question.answers, getters.search_strings)
                  })
        },
        // filtered flattened questions sorted
        sorted_questions: (state, getters) => {
            const sort_by = state.sorting_options[state.selected_sorting_option]

            return getters.filtered_questions.slice().sort(generateSortingFromDefinition(sort_by))
        },
        // sorted flattened questions only for the selected page
        questions_displayed: (state, getters) =>
            getters.sorted_questions.slice(
                (state.page - 1) * state.questions_per_page,
                state.page * state.questions_per_page
            ),
        // computed pagination
        pager: (state, getters) => {
            return {
                count: getters.filtered_questions.length,
                item_per_page: state.questions_per_page,
                page: state.page,
            }
        },
    },
    mutations: {
        [types.SET_URLS](state, urls) {
            state.urls = urls
        },
        [types.UPDATE_ANSWERS](state, answers_associated_products) {
            state.answers_associated_products = answers_associated_products
        },
        [types.UPDATE_PAGE](state, page) {
            state.page = page
        },
        [types.UPDATE_SELECTED_SORTING_OPTION](state, value) {
            state.selected_sorting_option = value
            state.page = 1
        },
        [types.UPDATE_SEARCH_QUERY](state, value) {
            state.search_query = value
            state.page = 1
        },
        [types.RETRIEVE_ANSWERS_QUESTIONS_FROM_PROPS](state, questions) {
            state.questions = questions
        },
    },
    actions,
    strict: debug,
})
