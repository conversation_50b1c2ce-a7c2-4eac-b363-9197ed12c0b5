/**
 * This file is part of CMS-Front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/**
 * Manage actions in customer selection
 *
 * @package   CMS Front-Office
 * @copyright 2016 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
!(function ($) {
    /**
     * manageSelectionDelete
     *
     * Manage the deletion of a customer selection.
     * This action will be attached to all HTML elements with the class 'DelPdt'
     *
     * @param {string} selection_delete_url
     */
    $.fn.manageSelectionDelete = function (selection_delete_url) {
        var _this = this

        this.delegate('.DelPdt', 'click', function (event) {
            var delete_elt = $(this)
            var slug = delete_elt.attr('data-value')

            if (slug !== undefined) {
                $.ajax({
                    url: selection_delete_url,
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        slug: slug,
                    },
                    success: function (response) {
                        // Delete HTML elements (line and end of line tr)
                        var tr_delete = delete_elt.parents('tr.SVDv3_panier_article')
                        var tr_endline = tr_delete.next('tr.SVDv3_panier_article_finArticle')

                        tr_delete.remove()
                        if (tr_endline.length !== 0) {
                            tr_endline.remove()
                        }

                        // If selections list is empty, reload the page
                        var nb_selections = $('body').find('tr.SVDv3_panier_article').length
                        if (nb_selections == 0) {
                            location.reload()
                        }
                    },
                    error: function (xhr) {
                        // @TODO manage errors on selection delete here
                    },
                })
            }

            event.preventDefault()
        })
    }
})(jQuery)
