import Vue from 'vue'
import { asset } from '@/shared/filters'
import ReferencePrice from '@/article/components/ReferencePrice.vue'
;((svd) => {
    Vue.filter('asset', asset)

    // instantiate the Vue app which handles the side-panel
    const app = new Vue({
        name: 'ReferencePriceApp',
        el: '#reference-price-app',
        provide: {
            version: svd?.version ?? 'v3',
        },
        components: { ReferencePrice },
        data() {
            return {
                event: null,
            }
        },
        methods: {
            handle(event) {
                this.event = event
            },
        },
        template: `<reference-price :event="event" @closed="event = null" />`,
    })

    const open = (event) => {
        app.handle(event)
        event.preventDefault()

        return false
    }

    svd.reference_price = { open }
})(window.SonVideo)
