services:
    app.validation.validator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Validator\Validator
        arguments:
            - '@validator'

    app.validator.customer.title:
        class: SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer\TitleValidator
        arguments:
            - '@pomm'
        tags:
            - { name: validator.constraint_validator }

    app.validator.customer.country_code:
        class: SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer\CountryCodeValidator
        arguments:
            - '@pomm'
        tags:
            - { name: validator.constraint_validator }

    app.validator.customer.address_name_is_unique:
        class: SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer\AddressNameIsUniqueValidator
        arguments:
            - '@pomm'
        tags:
            - { name: validator.constraint_validator }

    app.validator.customer.email_is_unique:
        class: SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer\CustomerEmailIsUniqueValidator
        arguments:
            - '@pomm'
            - '@router.default'
        tags:
            - { name: validator.constraint_validator, alias: customer_email_is_unique }

    app.validator.phone_number_compliance_validator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\PhoneNumberComplianceValidator
        arguments:
            - '@logger'
        tags:
            - { name: validator.constraint_validator }

    app.validator.pseudonym_is_unique_validator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer\PseudonymIsUniqueValidator
        arguments: ["@=service('pomm').getDefaultSession()"]
        tags:
            - { name: validator.constraint_validator }
