@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local('Open Sans'), local('OpenSans'), url("/fonts/open-sans/open-sans-v15-latin-regular.woff2") format('woff2'), url("/fonts/open-sans/open-sans-v15-latin-regular.woff") format('woff');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url("/fonts/open-sans/open-sans-v15-latin-600.woff2") format('woff2'), url("/fonts/open-sans/open-sans-v15-latin-600.woff") format('woff');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: local('Open Sans Bold'), local('OpenSans-Bold'), url("/fonts/open-sans/open-sans-v15-latin-700.woff2") format('woff2'), url("/fonts/open-sans/open-sans-v15-latin-700.woff") format('woff');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

@font-face {
    font-family: 'Montserrat bold';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: local('Montserrat Bold'), local('Montserrat-Bold'), url("/fonts/montserrat/Montserrat-Bold.woff2") format('woff2'), url("/fonts/montserrat/Montserrat-Bold.woff") format('woff');
}
