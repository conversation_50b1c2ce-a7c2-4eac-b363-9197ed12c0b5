i.icon
{
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon-svd' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    display: inline-block;
    vertical-align: -15%;

    /* Better Font Rendering =========== */
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-angle-bottom:before {
    content: "\e900";
}
.icon-angle-left:before {
    content: "\e901";
}
.icon-angle-right:before {
    content: "\e902";
}
.icon-angle-top:before {
    content: "\e903";
}
.icon-chevron-bottom:before {
    content: "\e93c";
}
.icon-chevron-left:before {
    content: "\e93d";
}
.icon-chevron-right:before {
    content: "\e93e";
}
.icon-chevron-top:before {
    content: "\e93f";
}
.icon-arrow-bottom:before {
    content: "\e904";
}
.icon-arrow-left:before {
    content: "\e905";
}
.icon-arrow-right:before {
    content: "\e906";
}
.icon-arrow-top:before {
    content: "\e907";
}
.icon-bars:before {
    content: "\e908";
}
.icon-basket:before {
    content: "\e909";
}
.icon-user:before {
    content: "\e90a";
}
.icon-zoom:before {
    content: "\e90b";
}
.icon-check:before {
    content: "\e90c";
}
.icon-check-circle:before {
    content: "\e90d";
}
.icon-remove:before {
    content: "\e90e";
}
.icon-remove-circle:before {
    content: "\e90f";
}
.icon-info:before {
    content: "\e910";
}
.icon-info-circle:before {
    content: "\e911";
}
.icon-exclamation:before {
    content: "\e912";
}
.icon-exclamation-circle:before {
    content: "\e913";
}
.icon-question:before {
    content: "\e914";
}
.icon-question-circle:before {
    content: "\e915";
}
.icon-eur:before {
    content: "\e916";
}
.icon-dollar:before {
    content: "\e917";
}
.icon-gbp:before {
    content: "\e918";
}
.icon-home:before {
    content: "\e919";
}
.icon-envelope:before {
    content: "\e91a";
}
.icon-phone:before {
    content: "\e91b";
}
.icon-globe:before {
    content: "\e91c";
}
.icon-compass:before {
    content: "\e91e";
}
.icon-clock-o:before {
    content: "\e91d";
}
.icon-power-off:before {
    content: "\e933";
}
.icon-credit-card:before {
    content: "\e91f";
}
.icon-headset:before {
    content: "\e920";
}
.icon-laurel-wreath:before {
    content: "\e921";
}
.icon-life-ring:before {
    content: "\e922";
}
.icon-lightning:before {
    content: "\e923";
}
.icon-lock:before {
    content: "\e924";
}
.icon-map-marker:before {
    content: "\e925";
}
.icon-star:before {
    content: "\e926";
}
.icon-tag:before {
    content: "\e927";
}
.icon-tag-percent:before {
    content: "\e928";
}
.icon-thumb-up:before {
    content: "\e929";
}
.icon-truck:before {
    content: "\e92a";
}
.icon-radio:before {
    content: "\e92b";
}
.icon-rss:before {
    content: "\e92c";
}
.icon-facebook:before {
    content: "\e92d";
}
.icon-twitter:before {
    content: "\e92e";
}
.icon-pinterest:before {
    content: "\e92f";
}
.icon-youtube:before {
    content: "\e930";
}
.icon-google-plus:before {
    content: "\e931";
}
.icon-instagram:before {
    content: "\e932";
}
.icon-comment:before {
    content: "\e934";
}
.icon-comments:before {
    content: "\e935";
}
.icon-apple:before {
    content: "\e936";
}
.icon-skype:before {
    content: "\e937";
}
.icon-android:before {
    content: "\e938";
}
.icon-tumblr:before {
    content: "\e939";
}
.icon-windows:before {
    content: "\e93a";
}
.icon-waves:before {
    content: "\e93b";
}
.icon-hand-pointer:before {
    content: "\e940";
}
.icon-pencil-square:before {
    content: "\e941";
}
.icon-calendar:before {
    content: "\e942";
}
.icon-wheelchair:before {
    content: "\e943";
}
.icon-megaphone:before {
    content: "\e944";
}
.icon-quote-left:before {
    content: "\e945";
}
.icon-quote-right:before {
    content: "\e946";
}
.icon-france:before {
    content: "\e947";
}
.icon-th:before {
    content: "\e948";
}
.icon-th-large:before {
    content: "\e949";
}
.icon-book:before {
    content: "\e94a";
}
.icon-graduation-cap:before {
    content: "\e94b";
}
.icon-eye:before {
    content: "\e94c";
}
.icon-graduation-cap:before {
    content: "\e94b";
}
.icon-eye:before {
    content: "\e94c";
}
.icon-music:before {
    content: "\e94d";
}
.icon-television:before {
    content: "\e94e";
}
.icon-pencil:before {
    content: "\e94f";
}
.icon-add:before {
    content: "\e950";
}

.icon-1-4x
{
    font-size: 0.25em;
}

.icon-1-2x
{
    font-size: 0.5em;
}

.icon-3-4x
{
    font-size: 0.75em;
    vertical-align: -10% !important;
}

/* makes the font 33% larger relative to the icon container */
.icon-lg
{
    font-size: 1.33333333em;
    line-height: 0.75em !important;
    vertical-align: -30% !important;
}

.icon-2x
{
    font-size: 2em;
    vertical-align: -30% !important;
}

.icon-3x
{
    font-size: 3em;
    vertical-align: -30% !important;
}

.icon-4x
{
    font-size: 4em;
    vertical-align: -30% !important;
}

.icon-5x
{
    font-size: 5em;
}

.icon-fw
{
    width: 1.28571429em;
    text-align: center;
}
