.swal2-modal {

    font-family: 'Open Sans', sans-serif;

    @media (max-width: 991px) {
        max-height: 90%;
    }

    .swal2-cancel,
    .swal2-confirm
    {
        font-size: 14px;
        touch-action: manipulation;
        cursor: pointer;
        width: 49%;
        font-weight: 600;
        font-family: 'Open Sans', sans-serif;
        box-sizing: border-box;
    }

    .swal2-styled {
        &:focus{
            box-shadow: none;
            outline: 5px auto -webkit-focus-ring-color;
        }
    }

    .popin-small-btn
    {
        width: 48.9%;
        margin-top: 0!important;
    }

    .swal2-cancel
    {
        color: #444;
        border: 1px solid #d0d0d0;
        border-radius: 3px;
        margin: 0 5px 0 0;
    }

    .swal2-confirm
    {
        border: 1px solid transparent;
        border-radius: 3px;
        margin: 0 0 0 5px;
    }

    button.swal2-styled .icon
    {
        text-align: left;
    }

    @media (max-width: 560px) {
        .swal2-cancel,
        .swal2-confirm
        {
            display: block!important;
            width: 100%;
            margin: 0 auto 10px;
        }
    }

    .swal2-modal .swal2-cancel
    {
        background-color: #fff;
        color: #444;
    }

    .swal2-title
    {
        padding: 10px 40px 10px 20px;
        margin: -20px -20px 20px;
        text-align: left;
        background: #f0f0f0;
        line-height: 26px;
        font-size: 18px;
        font-weight: 600;
        color: #444;
        border-radius: 5px 5px 0 0;
    }

    .swal2-close
    {
        color: #444;
        font-size: 38px;
        font-weight: 700;
    }

    .swal2-close:hover
    {
        color: #000;
    }

    .swal2-content
    {
        font-size: 16px;
        font-weight: 400;
    }

    .swal2-content h3
    {
        font-size: 16px;
        line-height: 24px;
        color: #000;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .SVDv3_ajoutPanier_garanties_wrapper
    {
        font-size: 12px;
        text-align: left;
        font-family: 'Helvetica Neue', 'Helvetica', 'Arial', 'sans-serif';
        display: inline-block;
        width: 70%;
    }

    /* Saving basket popin */

    .save-basket-name
    {
        font-family: "Open Sans", sans-serif;
        font-size: 14px;
        height: 40px;
        font-weight: 600;
        border: 1px solid #d0d0d0;
        box-sizing: border-box;
        border-radius: 0;
        box-shadow: none;
        margin: 20px 0 10px;
    }

    .save-basket-name:focus
    {
        border: 1px solid #3366cc;
    }

    .swal2-validationerror
    {
        padding: 0;
        color: #e2020d;
        font-size: 12px;
        text-align: left;
        background: none;
        margin: 0;
    }

    .swal2-validationerror::before
    {
        display:none;
    }

    .complementary_article {

        &.col-12 {
            width: 100%
        }

        &.col-6 {
            max-width: 309px;

        }

        &.col-4 {
            max-width: 192px;
        }

        @media (max-width: 650px) {
            &.col-4:last-child {
                display: none;
            }
        }

        @media (max-width: 450px) {
            &.col-4:nth-child(2) {
                display: none;
            }

            &.col-6:last-child {
                display: none;
            }
        }

        .SVDv3_produit_modele {
            margin-bottom: 3px;
        }

        .SVDv3_produit_description {
            a {
                color: #999;
                text-decoration: underline;
                &:hover {
                    color: #666;
                }
            }
        }
    }
}

.cetelem-popin,
.presto-popin {
    .swal2-content {
        text-align: left;
        font-size: 13px;
        line-height: 20px;

        p,
        ul {
            margin-bottom: 10px;
        }

        h2, h3, h4, h5, h6 {
            color: #444;
        }

        h3 {
            font-size: 18px;
            line-height: 26px;

            @media (min-width: 640px) {
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}

.form-submission-conditions-popin {

    .swal2-content {
        text-align: left;
        font-size: 12px !important;
        line-height: 19px !important;

        p,
        ul {
            margin-bottom: 10px;
        }

        ul {
            padding-left: 1.5em;

            li {
                padding: 0 0 0 16px;
                list-style-type: none;
                position: relative;

                &:before {
                    font-size: 75%;
                    font-family: 'icomoon-svd' !important;
                    content: "\E902";
                    position: absolute;
                    display: block;
                    top: 0;
                    left: 2px;
                }
            }
        }
    }
}
