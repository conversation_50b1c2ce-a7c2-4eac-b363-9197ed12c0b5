<?php
/*
 * This file is part of fo-cms package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Client;

use Psr\Log\LoggerInterface;

/**
 * Class ChronopostSoapClient
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Client
 */
class ChronopostSoapClient
{
    private string $wdsl;

    private string $account;

    private string $password;

    /**
     * @var \SoapClient
     */
    protected $client;

    /**
     * Used as placeholder as the fields order must be kept upon the API call
     */
    public const SEARCH_PARAMS = [
        'accountNumber' => '',
        'password' => '',
        'address' => '',
        'zipCode' => '',
        'city' => '',
        'countryCode' => 'FR',
        'type' => 'T',
        'productCode' => '86',
        'service' => 'T',
        'weight' => 0,
        'shippingDate' => '',
        'maxPointChronopost' => 15,
        'maxDistanceSearch' => 40,
        'holidayTolerant' => 1,
        'language' => 'FR',
        'version' => '2.0',
    ];

    private LoggerInterface $logger;

    /**
     * ChronopostSoapClient constructor.
     *
     *
     * @throws \Exception
     */
    public function __construct(string $wdsl, string $account, string $password, LoggerInterface $logger)
    {
        $this->wdsl = $wdsl;
        $this->account = $account;
        $this->password = $password;
        $this->initClient();
        $this->logger = $logger;
    }

    /**
     * initClient
     *
     * @throws \Exception
     */
    private function initClient(): void
    {
        try {
            $params = [
                'trace' => true,
                'exceptions' => true,
            ];

            $this->client = new \SoapClient($this->wdsl, $params);
        } catch (\Exception $e) {
            throw new \Exception(
                sprintf('Error while connecting via SOAP to %s : %s', $this->wdsl, $e->getMessage()),
                $e->getCode(),
                $e,
            );
        }
    }

    /**
     * searchPickupPointWith
     *
     *
     * @throws \Exception
     */
    public function searchPickupPointWith(array $params): array
    {
        $base_params = static::SEARCH_PARAMS;
        $base_params['accountNumber'] = $this->account;
        $base_params['password'] = $this->password;
        $base_params['shippingDate'] = (new \DateTime('tomorrow'))->format('d/m/Y');

        try {
            $result = $this->client->recherchePointChronopostInter(array_merge($base_params, $params));

            // Still useful for debugging purpose
            $this->logRequestAndResponse();
        } catch (\Exception $exception) {
            $this->logRequestAndResponse('error');

            throw $exception;
        }

        if ($result->return->errorCode !== 0) {
            throw new \Exception(
                sprintf(
                    '[CHRONOPOST API] An error occurred : "%d - %s"',
                    $result->return->errorCode,
                    $result->return->errorMessage,
                ),
            );
        }

        return $result->return->listePointRelais ?? [];
    }

    /**
     * logRequestAndResponse
     *
     *
     * @return $this
     */
    protected function logRequestAndResponse(string $status = 'success'): self
    {
        $this->logger->info(
            sprintf('[CHRONOPOST API] REQUEST (%s)', $status),
            (array) $this->client->__getLastRequest(),
        );
        $this->logger->info(
            sprintf('[CHRONOPOST API] RESPONSE (%s)', $status),
            (array) $this->client->__getLastResponse(),
        );

        return $this;
    }
}
