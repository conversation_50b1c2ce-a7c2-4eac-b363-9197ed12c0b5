<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule;

use SonVideo\Cms\FrontOffice\AppBundle\Exception\PromoCodeException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoCodeInterface;

/**
 * Interface PromoCodeRuleInterface
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
interface PromoCodeRuleInterface
{
    /**
     * isEligible
     *
     * @throws PromoCodeException
     */
    public function isEligible(array $cart_summary, PromoCodeInterface $promo_code): bool;

    /**
     * getErrorMessage
     */
    public function getErrorMessage(): string;
}
