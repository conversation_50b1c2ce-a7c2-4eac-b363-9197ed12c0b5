<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule;

use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\PromoCodeException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoCodeInterface;

/**
 * Class PromoCodeGiftedArticleIsInStockRule
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule
 */
class PromoCodeGiftedArticleIsInStockRule implements PromoCodeRuleInterface
{
    private PommSession $pomm_session;

    /**
     * PromoCodeGiftedArticleIsInStockRule constructor.
     */
    public function __construct(PommSession $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * isEligible
     *
     * @throws PromoCodeException
     */
    public function isEligible(array $cart_summary, PromoCodeInterface $promo_code): bool
    {
        $is_eligible = true;

        foreach ($promo_code->getPromoCodeRules() as $rule) {
            if (isset($rule['gift'])) {
                $is_eligible = $this->pomm_session->getModel(ArticleModel::class)->hasStock($rule['gift']['sku']);

                if ($is_eligible === false) {
                    break;
                }
            }
        }

        return $is_eligible;
    }

    /**
     * getErrorMessage
     */
    public function getErrorMessage(): string
    {
        return PromoCodeException::GIFTED_ARTICLE_HAS_NO_STOCK;
    }
}
