<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\EventListener;

use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\FilterResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Class CacheControlListenerBefore
 *
 * This listener store the "cache-control" header before symfony erase it
 * @see https://github.com/symfony/symfony/issues/25736
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\EventListener
 * <AUTHOR> <<EMAIL>>
 */
class CacheControlListenerBefore implements EventSubscriberInterface
{
    /**
     * @var array
     */
    public $cache_control_header = [];

    /**
     * @var bool
     */
    public $is_load_header = false;

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::RESPONSE => ['onKernelResponse', -999],
        ];
    }

    /**
     * Apply the header rules if the request matches.
     */
    public function onKernelResponse(FilterResponseEvent $event): void
    {
        $this->cache_control_header = $event->getResponse()->headers->get('cache-control');
        $this->is_load_header = true;
    }
}
