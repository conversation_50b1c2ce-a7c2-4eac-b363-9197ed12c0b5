<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\EventListener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\FilterResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Class CacheControlListenerAfter
 *
 * This listener restore the "cache-control" header after symfony erase it
 * @see https://github.com/symfony/symfony/issues/25736
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\EventListener
 * <AUTHOR> <<EMAIL>>
 */
class CacheControlListenerAfter implements EventSubscriberInterface
{
    private CacheControlListenerBefore $cache_control_listener_before;

    private SessionInterface $session;

    /**
     * CacheControlListenerAfter constructor.
     */
    public function __construct(CacheControlListenerBefore $cache_control_listener_before, SessionInterface $session)
    {
        $this->cache_control_listener_before = $cache_control_listener_before;
        $this->session = $session;
    }

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::RESPONSE => ['onKernelResponse', -1001],
        ];
    }

    /**
     * Apply the header rules if the request matches.
     */
    public function onKernelResponse(FilterResponseEvent $event): void
    {
        $response = $event->getResponse();

        if ($this->cache_control_listener_before->is_load_header && $this->cacheMustBeRestore()) {
            $response->headers->set('cache-control', $this->cache_control_listener_before->cache_control_header);
        }
    }

    /**
     * cacheMustBeRestore
     *
     * Cache must be restore if:
     *      - There is no flash message
     *
     * To complete?
     */
    protected function cacheMustBeRestore(): bool
    {
        return (is_countable($this->session->getFlashBag()->peekAll()) ? count($this->session->getFlashBag()->peekAll()) : 0) === 0;
    }
}
