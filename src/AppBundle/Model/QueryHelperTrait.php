<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Model;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleCategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleTagModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ColorModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\HighlightModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoOfferModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\Tag;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\SearchQueryProvider;

/**
 * Trait QueryHelperTrait
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Model
 */
trait QueryHelperTrait
{
    use HelperModelTrait;

    /**
     * getSearchQueryProvider
     */
    protected function getSearchQueryProvider(bool $search_only_basketable = true): SearchQueryProvider
    {
        return new SearchQueryProvider($this->getSession(), $search_only_basketable);
    }

    /**
     * makeMediaJoinQuery
     *
     * Format query used to find first media of each article.
     * Article table is used to search media only on tbale specified in argument.
     * If empty the table used is article.article, that's mean request will be slower.
     */
    private function makeMediaJoinQuery(string $article_table = ''): string
    {
        $sql = <<<SQL
        WITH parent_destock AS (
          SELECT
            sr.article_id,
            min(subltree(at.tag_path, {article_id_position_start}, {article_id_position_end})::TEXT)::int AS parent_article_id
          FROM
            {article} sr
            INNER JOIN {article_tag} at ON at.article_id = sr.article_id
          WHERE
            at.tag_path <@ '{destock_tag}'::ltree
          GROUP BY sr.article_id
        )
        SELECT
          sr.article_id,
          COALESCE({largest_image_url}, {parent_largest_image_url}) AS media_uri,
          COALESCE(ami18n.meta, ami18n2.meta) AS meta
        FROM
          {article} sr
          LEFT JOIN parent_destock pd            ON pd.article_id = sr.article_id
          LEFT JOIN {article_media_i18n} ami18n  ON ami18n.article_id = sr.article_id
                                                 AND ami18n.supported_culture_id = 'fr'
                                                 AND ami18n.display_order = 1
                                                 AND ami18n.type = 'IMAGE'::article.article_media_type
          LEFT JOIN {article_media} am           ON am.media_id = ami18n.media_id
          LEFT JOIN {article_media_i18n} ami18n2 ON ami18n2.article_id = pd.parent_article_id
                                                 AND ami18n2.supported_culture_id = 'fr'
                                                 AND ami18n2.display_order = 1
                                                 AND ami18n2.type = 'IMAGE'::article.article_media_type
          LEFT JOIN {article_media} am2          ON am2.media_id = ami18n2.media_id
        SQL;

        return strtr($sql, [
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{article}' => $article_table === '' ? $this->getModelRelation(ArticleModel::class) : $article_table,
            '{destock_tag}' => Tag::DESTOCK_BASE_TAG,
            '{article_id_position_start}' => count(explode('.', Tag::DESTOCK_BASE_TAG)),
            '{article_id_position_end}' => count(explode('.', Tag::DESTOCK_BASE_TAG)) + 1,
            '{largest_image_url}' => ArticleMediaModel::getSqlForLargestUsingAlias('am'),
            '{parent_largest_image_url}' => ArticleMediaModel::getSqlForLargestUsingAlias('am2'),
        ]);
    }

    /**
     * makeStandJoinQuery
     *
     * Format query used to find one unique stand of each article
     */
    private function makeStandJoinQuery(string $culture, Where $where): string
    {
        $sql = <<<SQL
        WITH
          article_stand AS (
          SELECT
            sjqat.article_id   AS article_id,
            min(sjqs.stand_id) AS stand_id
          FROM
            {article_tag} sjqat
            INNER JOIN {stand} sjqs
              ON sjqs.tag_path = sjqat.tag_path
          WHERE
            sjqat.article_id = ai18n.article_id
          GROUP BY article_id
        )
        SELECT
          ast.stand_id  AS stand_id,
          sjqs.slug     AS slug,
          COALESCE(sjqsi.editorial_content, sjqsi_default.editorial_content)
                        AS editorial_content,
          COALESCE(sjqsi.name, sjqsi_default.name)
                        AS name
        FROM
          article_stand ast
          INNER JOIN {stand} sjqs
            ON sjqs.stand_id = ast.stand_id
          LEFT JOIN {stand_i18n} sjqsi
            ON sjqsi.stand_id = ast.stand_id AND sjqsi.supported_culture_id = $*
          INNER JOIN {stand_i18n} sjqsi_default
            ON sjqsi_default.stand_id = ast.stand_id AND sjqsi.supported_culture_id = $*
        SQL;

        $where->andWhere('true', [$culture, APP_DEFAULT_LOCALE]);

        return strtr($sql, [
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{stand_i18n}' => $this->getModelRelation(StandI18nModel::class),
        ]);
    }

    /**
     * makeHighlightJoinQuery
     */
    private function makeHighlightJoinQuery(string $article_table): string
    {
        $sql = <<<SQL
        WITH
          promo_offer_highlight AS (
            SELECT
              base.article_id AS article_id,
              h.highlight_id
            FROM
              {table_name} base
              INNER JOIN {promo_offer} po ON base.article_id = ANY (po.computed_article_ids) AND po.lifetime @> now()
              INNER JOIN {highlight} h    ON h.promo_offer_id = po.promo_offer_id
          ),
          other_highlight AS (
            SELECT
              base.article_id AS article_id,
              h.highlight_id
            FROM
              {table_name} base
              INNER JOIN {article_tag} at ON at.article_id = base.article_id
              INNER JOIN {highlight} h    ON h.tag_path = at.tag_path AND h.promo_offer_id IS NULL
          )
        SELECT
          base.article_id,
          COALESCE(
            ARRAY_AGG(h.tag_path :: text ORDER BY h.display_order) FILTER (WHERE h.tag_path IS NOT NULL),
            '{}' :: TEXT[]
          ) AS highlight_tags,
          COALESCE(
              JSONB_AGG((SELECT highlight_projection FROM (SELECT
                h.highlight_id AS highlight_id,
                h.tag_path::text,
                h.display_order,
                h.media->(p.culture) AS media
              ) highlight_projection) ORDER BY h.display_order) FILTER (WHERE h.highlight_id IS NOT NULL),
              '[]' :: jsonb
          ) AS highlights
        FROM
          {table_name} base
          CROSS JOIN parameters p
          LEFT JOIN promo_offer_highlight poh ON poh.article_id = base.article_id
          LEFT JOIN other_highlight oh ON oh.article_id = base.article_id
          LEFT JOIN {highlight} h ON h.highlight_id = oh.highlight_id OR h.highlight_id = poh.highlight_id
        GROUP BY
          base.article_id
        SQL;

        return strtr($sql, [
            '{table_name}' => $article_table,
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{highlight}' => $this->getModelRelation(HighlightModel::class),
            '{promo_offer}' => $this->getModelRelation(PromoOfferModel::class),
        ]);
    }

    /**
     * makeColorJoinQuery
     */
    public function makeColorJoinQuery(string $article_table): string
    {
        $sql = <<<SQL
        WITH
          article_declinations AS (
              SELECT
                sr.article_id,
                a2.article_id AS article_declination_id,
                col2.label_i18n->(p.culture) as color_index,
                col.label_i18n->(p.culture) as color_label
              FROM {article_table} sr
                CROSS JOIN parameters p
                INNER JOIN {article} a ON a.article_id = sr.article_id
                INNER JOIN {article} a2 ON a2.common_content_id = a.common_content_id
                INNER JOIN {article_category} ac ON ac.article_id = a2.article_id
                INNER JOIN {color} col ON col.color_path::text = ac.attributes ->(p.culture)->> 'couleur'
                INNER JOIN {color} col2 ON col2.color_path = subltree(col.color_path, 0, 1)
              WHERE a2.unbasketable_reason IS NULL
          ),
          article_declinations_without_destock AS (
              SELECT DISTINCT
                ad.article_id, ad.color_index, ad.color_label
              FROM article_declinations ad
                LEFT JOIN article.article_tag at ON at.article_id = ad.article_declination_id
                                                 AND at.tag_path <@ '{destock_base_tag}'::public.ltree
              WHERE at.tag_path IS NULL
          )
        SELECT
          article_id,
          ARRAY_AGG(color_index) as color_indexes,
          ARRAY_AGG(color_label) as color_labels
        FROM article_declinations_without_destock
        GROUP BY article_id
        SQL;

        return strtr($sql, [
            '{destock_base_tag}' => Tag::DESTOCK_BASE_TAG,
            '{article_table}' => $article_table,
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{color}' => $this->getModelRelation(ColorModel::class),
        ]);
    }

    /**
     * makeLengthsJoinQuery
     */
    public function makeLengthsJoinQuery(string $article_table): string
    {
        $sql = <<<SQL
        WITH lengths AS (
          SELECT
            a.common_content_id,
            ac.attributes -> (parameters.culture) ->> 'longueur' AS length
          FROM {article_table} sr
            INNER JOIN {article} a ON a.article_id = sr.article_id
            INNER JOIN {article_category} ac ON a.article_id = ac.article_id
            CROSS JOIN parameters
          GROUP BY 1, 2
        ),
        category_attribute AS (
          WITH RECURSIVE
            article_category AS (
              SELECT category_id FROM {article_category} INNER JOIN {article_table} sr USING(article_id)
            ),
            all_article_categories AS (
              SELECT
                c.category_id,
                c.category_parent_id,
                c.attributes
              FROM {category} c
                INNER JOIN article_category ac USING (category_id)
              UNION
              SELECT
                c.category_id,
                c.category_parent_id,
                c.attributes
              FROM {category} c
                INNER JOIN all_article_categories ac ON ac.category_parent_id = c.category_id
          )
          SELECT
            category_id,
            jsonb_array_elements(attributes -> 'fr') AS one_attribute
          FROM all_article_categories
          ORDER BY category_id DESC
        ),
        category_length_unit AS (
          SELECT
            category_id,
            one_attribute->>'code' AS attribute_code,
            one_attribute->>'unit' AS attribute_unit
          FROM category_attribute
          where one_attribute->>'code' = 'longueur' AND one_attribute?'unit'
          LIMIT 1
        )
        SELECT
          sr.article_id,
          COALESCE(json_agg(trim(l.length::text || ' ' || coalesce(clu.attribute_unit, ''))
               ORDER BY
                 l.length :: FLOAT4 ASC)
                 FILTER (WHERE l.length IS NOT NULL AND l.length != ''), '[]'):: JSONB AS article_lengths
        FROM {article_table} sr
          INNER JOIN {article} aa            ON aa.article_id = sr.article_id
          LEFT JOIN lengths l                ON aa.common_content_id = l.common_content_id
          LEFT JOIN {article_category} ac    ON ac.article_id = aa.article_id
          CROSS JOIN category_length_unit clu
        GROUP BY sr.article_id
        SQL;

        return strtr($sql, [
            '{article_table}' => $article_table,
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
        ]);
    }

    /**
     * makeLengthQuery
     */
    public function makeLengthQuery(string $article_table): string
    {
        $sql = <<<SQL
        WITH
        category_attribute AS (
          WITH RECURSIVE
            article_category AS (
              SELECT category_id FROM {article_category} INNER JOIN {article_table} sr USING(article_id)
            ),
            all_article_categories AS (
              SELECT
                c.category_id,
                c.category_parent_id,
                c.attributes
              FROM {category} c
                INNER JOIN article_category ac USING (category_id)
              UNION
              SELECT
                c.category_id,
                c.category_parent_id,
                c.attributes
              FROM {category} c
                INNER JOIN all_article_categories ac ON ac.category_parent_id = c.category_id
          )
          SELECT
            category_id,
            jsonb_array_elements(attributes -> 'fr') AS one_attribute
          FROM all_article_categories
          ORDER BY category_id
        ),
        category_length_unit AS (
          SELECT
            category_id,
            one_attribute->>'code' AS attribute_code,
            one_attribute->>'unit' AS attribute_unit
          FROM category_attribute
          where one_attribute->>'code' = 'longueur' AND one_attribute?'unit'
          LIMIT 1
        )
        SELECT
          sr.article_id,
          coalesce(ac.attributes -> (parameters.culture) ->> 'longueur', '') || ' ' || coalesce(clu.attribute_unit, '') AS article_length
        FROM {article_table} sr
          INNER JOIN {article} aa            ON aa.article_id = sr.article_id
          LEFT JOIN {article_category} ac    ON ac.article_id = aa.article_id
          CROSS JOIN category_length_unit clu
          CROSS JOIN parameters
        WHERE ac.attributes -> (parameters.culture) ->> 'longueur' IS NOT NULL
          AND ac.attributes -> (parameters.culture) ->> 'longueur' <> ''
        SQL;

        return strtr($sql, [
            '{article_table}' => $article_table,
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
        ]);
    }

    /**
     * makePromoCodeQuery
     */
    public function makePromoCodeQuery(string $article_table): string
    {
        $sql = <<<SQL
            SELECT
              a.article_id,
              array_agg(po.article_short_description_i18n -> 'fr' ORDER BY upper(po.lifetime) DESC)
              filter (WHERE po.article_short_description_i18n -> p.culture IS NOT NULL) AS promo_short_description
            FROM
              {article_table} a
              INNER JOIN {promo_offer} po ON a.article_id = ANY(po.computed_article_ids)
              CROSS JOIN parameters p
            WHERE
              po.lifetime @> now()
              AND po.article_short_description_i18n ? p.culture
              AND po.deactivated_reason IS NULL
            GROUP BY a.article_id
        SQL;

        return strtr($sql, [
            '{article_table}' => $article_table,
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{promo_offer}' => $this->getModelRelation(PromoOfferModel::class),
        ]);
    }
}
