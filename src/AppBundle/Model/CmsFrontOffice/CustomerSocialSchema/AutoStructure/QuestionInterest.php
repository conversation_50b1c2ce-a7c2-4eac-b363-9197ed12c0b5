<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * QuestionInterest
 *
 * Structure class for relation customer_social.question_interest.
 * Customer interested by a question.
 *
 *
 *
 * @see RowStructure
 */
class QuestionInterest extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('customer_social.question_interest')
            ->setPrimaryKey(['customer_id', 'common_content_question_id'])
            ->addField('common_content_question_id', 'uuid')
            ->addField('customer_id', 'int4');
    }
}
