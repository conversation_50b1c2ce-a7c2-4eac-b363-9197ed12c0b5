<?php
/*
 * This file is part of FO-CMS package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice;

use SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\ArticleEavHelper;

trait ArticleEavDecoratorForFlexibleEntityTrait
{
    /**
     * Very useful when the entity is within an iterator
     * Note: don't try to iterate through the iterator and write each in each entity
     *       it will sometimes write it, and sometimes it will not. And you won't know why (Exemple: shopping cart in session).
     *       Hence, this method allow to add the fields when calling extract on the entity,
     *       Which is also done in a predictable way in CollectionIterator/ResultIterator
     *
     * @return array|array[]|bool[]|float[]|int[]|mixed|string[]
     */
    public function extract()
    {
        if (!$this->has('eav_attributes')) {
            return parent::extract();
        }

        ArticleEavHelper::mergeDestockParentAttributesInArticle($this);
        ArticleEavHelper::extractRegulatedAttributesAndAssignTo($this);

        return parent::extract();
    }
}
