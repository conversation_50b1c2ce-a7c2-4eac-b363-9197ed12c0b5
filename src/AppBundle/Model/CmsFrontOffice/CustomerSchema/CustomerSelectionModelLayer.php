<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema;

use PommProject\Foundation\Where;
use PommProject\ModelManager\ModelLayer\ModelLayer;

/**
 * CustomerSelectionModelLayer
 *
 * Transaction class for operations on customer selection.
 *
 * @package CMS Front-Office
 * <AUTHOR> Audic <<EMAIL>>
 * @see     ModelLayer
 */
class CustomerSelectionModelLayer extends ModelLayer
{
    /**
     * copyBasketToCustomerSelection
     *
     * Copy basket of a customer into a new selection.
     *
     *
     * @throws \Exception
     */
    public function copyBasketToCustomerSelection(int $customer_id, string $selection_name): CustomerSelection
    {
        $this->startTransaction();

        try {
            if (preg_match('/^\./i', $selection_name) === 1) {
                throw new \InvalidArgumentException('Selection name is invalid.');
            }

            // Create customer selection
            $customer_selection = $this->getModel(CustomerSelectionModel::class)->createNewSelection(
                $selection_name,
                $customer_id,
            );

            // Copy basket articles into new selection
            $this->getModel(ArticleCustomerSelectionModel::class)->mergeFromBasket($customer_selection);

            $this->commitTransaction();
        } catch (\Exception $ex) {
            $this->rollbackTransaction();

            throw $ex;
        }

        return $customer_selection;
    }

    /**
     * copyCustomerSelectionToBasket
     *
     *
     * @throws \Exception
     */
    public function copyCustomerSelectionToBasket(int $customer_id, string $selection_slug): Basket
    {
        $this->startTransaction();

        try {
            // Retrieve selection from slug
            $selection = $this->getModel(CustomerSelectionModel::class)
                ->findWhere(Where::create('customer_id = $* AND slug = $*', [$customer_id, $selection_slug]))
                ->current();

            if (!$selection instanceof CustomerSelection) {
                throw new \Exception(
                    sprintf('Customer selection %s not exists for selected customer', $selection_slug),
                );
            }

            // Retrieve basket and if not exists, create it
            $basket = $this->getModel(BasketModel::class)->fetchOne($customer_id);

            // Merge selection articles into basket lines
            $this->getModel(BasketLineModel::class)->mergeFromCustomerSelection($selection);

            $this->commitTransaction();
        } catch (\Exception $ex) {
            $this->rollbackTransaction();

            throw $ex;
        }

        return $basket;
    }

    /**
     * fullDelete
     *
     * Full delete of a customer selection with articles selected.
     *
     * @return  CustomerSelection|null
     * @throws  \Exception
     */
    public function fullDelete(string $selection_slug, int $customer_id)
    {
        $this->startTransaction();

        try {
            // Find customer selection from slug and customer id
            $customer_selection = $this->getModel(CustomerSelectionModel::class)
                ->findWhere('slug = $* AND customer_id = $*', [$selection_slug, $customer_id])
                ->current();

            if ($customer_selection instanceof CustomerSelection) {
                // Delete articles from customer selection
                $this->getModel(ArticleCustomerSelectionModel::class)->deleteWhere('name = $* AND customer_id = $*', [
                    $customer_selection->get('name'),
                    $customer_id,
                ]);

                // Delete customer selection
                $this->getModel(CustomerSelectionModel::class)->deleteOne($customer_selection);
            }

            $this->commitTransaction();
        } catch (\Exception $ex) {
            $this->rollbackTransaction();

            throw $ex;
        }

        return $customer_selection;
    }
}
