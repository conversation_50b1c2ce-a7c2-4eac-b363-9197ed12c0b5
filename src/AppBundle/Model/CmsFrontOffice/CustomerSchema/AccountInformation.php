<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema;

use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Model\FlexibleEntity;

/**
 * AccountInformation
 *
 * Flexible entity for relation
 * customer.account_information
 *
 * @see FlexibleEntity
 */
class AccountInformation extends FlexibleEntity
{
    public const EMAIL_IS_VALID = 'email_valid';

    /**
     * catalogInBasketIsSet
     */
    public function catalogInBasketIsSet(int $catalog_year): bool
    {
        $preferences = $this->get('preferences');
        if (!is_array($preferences)) {
            return false;
        }

        return array_key_exists('catalog_in_basket', $preferences) &&
            array_key_exists((string) $catalog_year, $preferences['catalog_in_basket']);
    }

    /**
     * setCatalogInBasket
     */
    public function setCatalogInBasket(int $catalog_year, bool $value): self
    {
        $preferences = $this->get('preferences');
        $catalog_year = (string) $catalog_year;
        if (!is_array($preferences)) {
            $preferences = [];
        }

        if (!$value) {
            if (
                array_key_exists('catalog_in_basket', $preferences) &&
                array_key_exists($catalog_year, $preferences['catalog_in_basket'])
            ) {
                unset($preferences['catalog_in_basket'][$catalog_year]);
            }
        } else {
            if (!array_key_exists('catalog_in_basket', $preferences)) {
                $preferences['catalog_in_basket'] = [];
            }

            $preferences['catalog_in_basket'][$catalog_year] = date('c');
        }

        $this->set('preferences', $preferences);

        return $this;
    }

    /**
     * magazineInBasketIsSet
     */
    public function magazineInBasketIsSet(int $magazine_number): bool
    {
        $preferences = $this->get('preferences');
        if (!is_array($preferences)) {
            return false;
        }

        return array_key_exists('magazine_in_basket', $preferences) &&
            array_key_exists((string) $magazine_number, $preferences['magazine_in_basket']);
    }

    /**
     * setMagazineInBasket
     *
     *
     */
    public function setMagazineInBasket(int $magazine_number, bool $value): self
    {
        $preferences = $this->get('preferences');
        $magazine_number = (string) $magazine_number;
        if (!is_array($preferences)) {
            $preferences = [];
        }

        if (!$value) {
            if (
                array_key_exists('magazine_in_basket', $preferences) &&
                array_key_exists($magazine_number, $preferences['magazine_in_basket'])
            ) {
                unset($preferences['magazine_in_basket'][$magazine_number]);
            }
        } else {
            if (!array_key_exists('magazine_in_basket', $preferences)) {
                $preferences['magazine_in_basket'] = [];
            }

            $preferences['magazine_in_basket'][$magazine_number] = date('c');
        }

        $this->set('preferences', $preferences);

        return $this;
    }

    /**
     * setConfirmEmail
     *
     *
     * @throws ModelException
     */
    public function setConfirmEmail(bool $is_active = true): self
    {
        $preferences = $this->get('preferences');
        if (!is_array($preferences)) {
            $preferences = [];
        }

        if (!$is_active) {
            if (array_key_exists(self::EMAIL_IS_VALID, $preferences)) {
                unset($preferences[self::EMAIL_IS_VALID]);
            }
        } else {
            $preferences[self::EMAIL_IS_VALID] = date('Y-m-d H:i:s');
        }

        $this->set('preferences', $preferences);

        return $this;
    }

    /**
     * getAge
     *
     * @throws ModelException
     */
    public function getAge(): ?int
    {
        $birthday = $this->get('birthday');

        return $birthday === null ? null : (int) $birthday->diff(new \DateTime())->format('%y');
    }
}
