<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * BasketOrderLine
 *
 * Structure class for relation customer.basket_order_line.
 * Store lines of a basket order. Lines could describe article, shipment
 * method, payment method, etc.
 *
 * type:
 * The type of basket order line must be an article, a quotation, a shipment
 * method, a payment method or a promo code.
 * id:
 * The id of the targeted element.
 *
 * @see RowStructure
 */
class BasketOrderLine extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this
            ->setRelation('customer.basket_order_line')
            ->setPrimaryKey(['order_line_no', 'customer_id'])
            ->addField('customer_id', 'int4')
            ->addField('order_line_no', 'int4')
            ->addField('type', 'regclass')
            ->addField('id', 'int4')
            ->addField('quantity', 'int4')
            ->addField('price', 'numeric')
            ->addField('price_vat_excluded', 'numeric')
            ->addField('description', 'text')
            ->addField('extra_data', 'jsonb')
            ;
    }
}
