<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\NavigationSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\NavigationSchema\AutoStructure\MegaMenu as MegaMenuStructure;

/**
 * MegaMenuModel
 *
 * Model class for table mega_menu.
 *
 * @see Model
 */
final class MegaMenuModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new MegaMenuStructure();
        $this->flexible_entity_class = MegaMenu::class;
    }

    public function cleanEntries(array $entries): array
    {
        return array_reduce(
            $entries,
            function ($kept_entries, array $entry): array {
                $entry['content'] = array_map([self::class, 'filterColumn'], $entry['content']);
                $entry['content'] = $this->getColumnsIfNotEmpty($entry['content']);

                return $this->keepRelevantEntryData($kept_entries, $entry);
            },
            [],
        );
    }

    private function filterColumn($column): array
    {
        return array_values(
            array_filter(
                $column,
                static fn($item): bool => $item['is_active'] && ($item['type'] !== 'item' || $item['url'] !== null),
            ),
        );
    }

    private function getColumnsIfNotEmpty(array $columns): array
    {
        foreach ($columns as $column) {
            if ((is_countable($column) ? count($column) : 0) > 0) {
                return $columns;
            }
        }

        return [];
    }

    private function keepRelevantEntryData(array &$kept_entries, array $entry): array
    {
        if (
            !$entry['is_active'] ||
            ((is_countable($entry['content']) ? count($entry['content']) : 0) === 0 && $entry['url'] === null)
        ) {
            return $kept_entries;
        }

        $kept_entries[] = [
            'label' => $entry['label'],
            'url' => $entry['url'],
            'meta' => $entry['meta'],
            'content' => $entry['content'],
        ];

        return $kept_entries;
    }
}
