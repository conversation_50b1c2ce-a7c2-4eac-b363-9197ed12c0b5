<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\AutoStructure\Carrier as CarrierStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\Carrier;

/**
 * CarrierModel
 *
 * Model class for table carrier.
 *
 * @see Model
 */
class CarrierModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new CarrierStructure();
        $this->flexible_entity_class = Carrier::class;
    }
}
