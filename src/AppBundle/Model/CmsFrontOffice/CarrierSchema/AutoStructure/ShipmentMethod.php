<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * ShipmentMethod
 *
 * Structure class for relation carrier.shipment_method.
 * Carriers shipment methods
 *
 * is_online:
 * if true, shipment method is displayed in front office
 * title_i18n:
 * Shipment method's title in different cultures
 * description_i18n:
 * Description of Shipment method in different cultures
 * no_delay_fallback_i18n:
 * Generic delay text when no delay has been computed, in different cultures
 *
 * @see RowStructure
 */
class ShipmentMethod extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('carrier.shipment_method')
            ->setPrimaryKey(['shipment_method_id'])
            ->addField('shipment_method_id', 'int4')
            ->addField('carrier_id', 'int4')
            ->addField('is_online', 'bool')
            ->addField('title_i18n', 'public.hstore')
            ->addField('description_i18n', 'public.hstore')
            ->addField('logo', 'public.citext')
            ->addField('name', 'text')
            ->addField('no_delay_fallback_i18n', 'public.hstore');
    }
}
