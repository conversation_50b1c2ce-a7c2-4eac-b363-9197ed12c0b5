<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\ArticleStandHeadline as ArticleStandHeadlineStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleStandHeadline;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\QueryHelperTrait;

/**
 * ArticleStandHeadlineModel
 *
 * Model class for table article_stand_headline.
 *
 * @see Model
 */
class ArticleStandHeadlineModel extends Model
{
    use WriteQueries;
    use QueryHelperTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ArticleStandHeadlineStructure();
        $this->flexible_entity_class = ArticleStandHeadline::class;
    }

    /**
     * findAllByStandId
     *
     * Return $limit articles following those sort:
     *   - is specified as headline
     *   - is a main article
     *   - has maximum stock
     *
     * Then the n first articles are re-order by selling price
     */
    public function findAllByStandId(int $stand_id, string $culture, int $limit = 3): CollectionIterator
    {
        $sql = <<<SQL
        SELECT
          a.article_id               AS article_id,
          ash.article_id IS NOT NULL AS headline,
          p.informational_stock      AS informational_stock,
          p.informational_stock != 0 AS has_stock
        FROM
          {stand} s
          INNER JOIN {article_tag} at            ON s.tag_path = at.tag_path
          LEFT JOIN {article_stand_headline} ash ON at.article_id = ash.article_id
                                                 AND ash.stand_id = s.stand_id
          INNER JOIN {article} a                 ON at.article_id = a.article_id
          INNER JOIN {product} p                 ON a.sku = p.sku
          LEFT JOIN {common_content} cc          ON a.common_content_id = cc.common_content_id
                                                 AND a.article_id = cc.default_article_id
          INNER JOIN {article_media} am          ON a.article_id = am.article_id
          INNER JOIN {article_media_i18n} ami18n ON am.media_id = ami18n.media_id
                                                 AND ami18n.supported_culture_id = $*
                                                 AND ami18n.display_order = 1
                                                 AND ami18n.type = 'IMAGE'::article.article_media_type
        WHERE
          s.stand_id = $* AND a.unbasketable_reason IS NULL
        ORDER BY headline DESC, cc.default_article_id IS NOT NULL DESC, informational_stock DESC, price->'selling_price'
        LIMIT $*
        SQL;
        $sql = strtr($sql, [
            '{article_stand_headline}' => $this->structure->getRelation(),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
        ]);

        return $this->getCommonArticleInformationFromStandHeadlineArticles(
            $sql,
            $culture,
            [$culture, $stand_id, $limit],
            'ORDER BY price->\'selling_price\'',
        );
    }

    /**
     * getCommonArticleInformationFromStandHeadlineArticles
     *
     * @param string|null $suffix
     *
     */
    protected function getCommonArticleInformationFromStandHeadlineArticles(
        string $sql_query,
        string $culture,
        array $values = [],
        string $suffix = null
    ): CollectionIterator {
        $sql = <<<SQL
        WITH
          parameters as (
            SELECT $*::text as culture
          ),
          selected_article as (
            {sql_query}
          ),
          highlight AS ({highlight_query})
        SELECT
          {projection}
        FROM
          selected_article s
          CROSS JOIN parameters param
          INNER JOIN {article} a
            ON s.article_id = a.article_id
          INNER JOIN {article_i18n} ai
            ON a.article_id = ai.article_id
            AND ai.supported_culture_id = param.culture
          INNER JOIN {brand} ab
            ON a.brand_id = ab.brand_id
          INNER JOIN {product} p USING (sku)
          INNER JOIN {common_content} cc
            ON a.common_content_id = cc.common_content_id
          INNER JOIN {common_content_i18n} cci18n
            ON cc.common_content_id = cci18n.common_content_id
            AND cci18n.supported_culture_id = param.culture
          INNER JOIN {article_media} am
            ON a.article_id = am.article_id
          INNER JOIN {article_media_i18n} ami18n
            ON am.media_id = ami18n.media_id
            AND ami18n.supported_culture_id = param.culture
            AND ami18n.display_order = 1
            AND ami18n.type = 'IMAGE'::article.article_media_type
          INNER JOIN {article_category} ac
            ON a.article_id = ac.article_id
          INNER JOIN {category} c
            ON ac.category_id = c.category_id
          INNER JOIN {category_i18n} ci18n
            ON ac.category_id = ci18n.category_id
            AND ci18n.supported_culture_id = param.culture
          LEFT JOIN highlight h ON h.article_id = s.article_id
        {suffix}
        SQL;

        $projection = $this->getModel(ArticleModel::class)
            ->createProjection()
            ->unsetField('brand_id')
            ->unsetField('common_content_id')
            ->unsetField('internal_unbasketable_comment')
            ->unsetField('weight_g')
            ->unsetField('batch_sale')
            ->unsetField('created_at')
            ->unsetField('packaged_articles')
            ->setField('brand_name', 'ab.name', 'text')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('editorial_content', 'ai.editorial_content', 'jsonb')
            ->setField('cc_short_description', 'cci18n.editorial_content->>\'short_description\'', 'text')
            ->setField('image_url', ArticleMediaModel::getSqlForLargestUsingAlias('am'), 'text')
            ->setField('category_name', 'ci18n.name', 'text')
            ->setField('category_slug', 'c.slug', 'text')
            ->setField('display_mode', 'cc.display_mode', 'article.display_mode')
            ->setField(
                'note',
                'CASE WHEN cc.review_score IS NOT NULL THEN ROUND(cc.review_score, 1) ELSE null END',
                'int',
            )
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.display_mode')
            ->setField('highlights', 'h.highlights', 'jsonb')
            ->setField('common_content_name', 'cci18n.name', 'text')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{article_stand_head_line}' => $this->structure->getRelation(),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_i18n}' => $this->getModelRelation(ArticleI18nModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{default_culture}' => APP_DEFAULT_LOCALE,
            '{sql_query}' => $sql_query,
            '{suffix}' => $suffix ?? '',
            '{highlight_query}' => $this->makeHighlightJoinQuery('selected_article'),
        ]);

        return $this->query($sql, array_merge([$culture], $values), $projection);
    }
}
