<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\Foundation\ResultIterator;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\ArticleMedia as ArticleMediaStructure;

/**
 * ArticleMediaModel
 *
 * Model class for table article_media.
 *
 * @see Model
 */
class ArticleMediaModel extends Model
{
    use WriteQueries;

    public static function getSqlForLargestUsingAlias(string $alias): string
    {
        $sql = <<<SQL
            COALESCE({alias}.media_variation->'image'->>'source', jsonb_extract_path_text({alias}.media_variation->'image'->'referential', {alias}.media_variation->'image'->>'largest'))
        SQL;

        return strtr($sql, ['{alias}' => $alias]);
    }

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ArticleMediaStructure();
        $this->flexible_entity_class = ArticleMedia::class;
    }

    public function findMainImageMediaForArticles(
        array $article_id_list,
        string $culture = APP_DEFAULT_LOCALE
    ): ResultIterator {
        if ($article_id_list === []) {
            throw new \UnexpectedValueException('You must provide some articles id or handle the case in your code.');
        }

        $sql = <<<SQL
        SELECT
          am.article_id,
          {largest_image_url} AS url
        FROM
          article.article_media am
          INNER JOIN article.article_media_i18n ami18n USING (media_id)
        WHERE
          {conditions}
        SQL;

        $conditions = Where::createWhereIn('am.article_id', $article_id_list)
            ->andWhere('display_order = 1')
            ->andWhere('ami18n.supported_culture_id = $*', [$culture])
            ->andWhere("ami18n.type = 'IMAGE'::article.article_media_type");

        $sql = strtr($sql, [
            '{conditions}' => $conditions,
            '{largest_image_url}' => ArticleMediaModel::getSqlForLargestUsingAlias('am'),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, $conditions->getValues());
    }
}
