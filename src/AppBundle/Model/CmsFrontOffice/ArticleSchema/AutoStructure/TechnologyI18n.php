<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * TechnologyI18n
 *
 * Structure class for relation article.technology_i18n.
 * international elements of technology
 *
 * technology_id:
 * Unique identifier on technology
 * medias:
 * Set of medias associated by culture
 *
 * @see RowStructure
 */
class TechnologyI18n extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.technology_i18n')
            ->setPrimaryKey(['supported_culture_id', 'technology_id'])
            ->addField('technology_id', 'int4')
            ->addField('supported_culture_id', 'bpchar')
            ->addField('expression', 'text')
            ->addField('definition', 'text')
            ->addField('logo_caption', 'text')
            ->add<PERSON>ield('medias', 'jsonb');
    }
}
