<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * RelatedCommonContent
 *
 * Structure class for relation article.related_common_content.
 * Relations between common contents
 *
 * common_content_id:
 * Common content on which we want to link another one
 * related_common_content_id:
 * Reference to the common content we want to link to the current one
 * relation_type:
 * Type of relation between the two common contents
 *
 * @see RowStructure
 */
class RelatedCommonContent extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.related_common_content')
            ->setPrimaryKey(['relation_type', 'related_common_content_id', 'common_content_id'])
            ->addField('common_content_id', 'int4')
            ->addField('related_common_content_id', 'int4')
            ->addField('relation_type', 'article.related_common_content_type');
    }
}
