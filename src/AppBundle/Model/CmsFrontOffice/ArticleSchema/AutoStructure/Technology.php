<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * Technology
 *
 * Structure class for relation article.technology.
 * Specialized expression in the audio / video market
 *
 * technology_id:
 * Unique identifier for technology entries, using an integer for legacy
 * compatibility
 * is_displayed_in_lexicon:
 * Determine if technology must appears in lexicon.
 *
 * @see RowStructure
 */
class Technology extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.technology')
            ->setPrimaryKey(['technology_id'])
            ->addField('technology_id', 'int4')
            ->addField('logo_uri', 'public.citext')
            ->addField('tag_path', 'public.ltree')
            ->addField('is_displayed_in_lexicon', 'bool');
    }
}
