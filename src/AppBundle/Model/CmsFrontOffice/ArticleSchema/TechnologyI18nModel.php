<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\TechnologyI18n as TechnologyI18nStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\TechnologyI18n;

/**
 * TechnologyI18nModel
 *
 * Model class for table technology_i18n.
 *
 * @see Model
 */
class TechnologyI18nModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new TechnologyI18nStructure();
        $this->flexible_entity_class = TechnologyI18n::class;
    }
}
