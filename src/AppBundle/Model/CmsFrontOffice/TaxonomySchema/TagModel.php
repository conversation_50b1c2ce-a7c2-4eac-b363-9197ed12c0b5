<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\AutoStructure\Tag as TagStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\Tag;

/**
 * TagModel
 *
 * Model class for table tag.
 *
 * @see Model
 */
class TagModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new TagStructure();
        $this->flexible_entity_class = Tag::class;
    }
}
