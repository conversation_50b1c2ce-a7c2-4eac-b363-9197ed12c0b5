<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * StandI18n
 *
 * Structure class for relation taxonomy.stand_i18n.
 * decline stands in several supported cultures
 *
 * stand_id:
 * stand identifier
 * supported_culture_id:
 * unique identifier for culture
 * name:
 * Stand name in supported culture
 * editorial_content:
 * Stand presentation
 * keywords:
 * Keywords for SEO
 * description:
 * Meta-description used for SEO
 * title:
 * Alternative title to use for the stand
 *
 * @see RowStructure
 */
class StandI18n extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('taxonomy.stand_i18n')
            ->setPrimaryKey(['supported_culture_id', 'stand_id'])
            ->addField('stand_id', 'int4')
            ->addField('supported_culture_id', 'bpchar')
            ->addField('name', 'text')
            ->addField('editorial_content', 'jsonb')
            ->addField('keywords', 'text[]')
            ->addField('description', 'text')
            ->addField('title', 'text');
    }
}
