<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\Projection;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use PommProject\Foundation\Where;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\AutoStructure\MainProduct as MainProductStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\MainProduct;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * MainProductModel
 *
 * Model class for table main_product.
 *
 * @see Model
 */
class MainProductModel extends Model
{
    use WriteQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new MainProductStructure();
        $this->flexible_entity_class = MainProduct::class;
    }

    /**
     * findByCatalog
     *
     *
     * @return MainProduct|null
     */
    public function findByCatalog(string $product_path, string $catalog_path)
    {
        $where = Where::create('b.path ~ $* AND mp.path ~ $*', [
            sprintf('%s.*', $catalog_path),
            sprintf('%s.*.%s', $catalog_path, $product_path),
        ]);

        return $this->findCompletedMainProducts($where)->current();
    }

    /**
     * findAllByCatalog
     *
     *
     */
    public function findAllByCatalog(string $catalog_path): CollectionIterator
    {
        $where = Where::create('b.path ~ $*', [sprintf('%s.*', $catalog_path)]);

        return $this->findCompletedMainProducts($where);
    }

    /**
     * findByCatalogAndBrand
     *
     *
     */
    public function findByCatalogAndBrand(string $catalog_path, string $brand_path): CollectionIterator
    {
        $where = Where::create('b.path ~ $*', [sprintf('%s.*', $catalog_path)])->andWhere('mp.brand_path = $*', [
            $brand_path,
        ]);

        return $this->findCompletedMainProducts($where);
    }

    /**
     * findCompletedMainProducts
     *
     *
     */
    protected function findCompletedMainProducts(Where $where): CollectionIterator
    {
        $sql = <<<SQL
        SELECT
          {projection}
        FROM {brand} b
          INNER JOIN {main_product} mp
            ON b.path = mp.brand_path
          LEFT JOIN {article} a
            ON mp.article_id = a.article_id
        WHERE
        {condition}
        SQL;

        $projection = $this->getMainProductArticleProjection();

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('mp'),
            '{main_product}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, $where->getValues(), $projection);
    }

    /**
     * getMainProductArticleProjection
     */
    protected function getMainProductArticleProjection(): Projection
    {
        return $this->createProjection()
            ->unsetField('name')
            ->unsetField('article_id')
            ->unsetField('draft_article_id')
            ->setField('name', 'coalesce(a.name, mp.name)', 'text')
            ->setField('article_url', 'a.article_url', 'text');
    }
}
