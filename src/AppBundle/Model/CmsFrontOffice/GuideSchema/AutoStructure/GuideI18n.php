<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * GuideI18n
 *
 * Structure class for relation guide.guide_i18n.
 * Decline families in several supported cultures.
 *
 * title:
 * Alternative title to use for the guide
 * editorial_content:
 * Editorial content of guide language variation. Contains resume and content.
 * keywords:
 * List of words used to define guide language variation.
 * description:
 * Meta-data of guide language variation.
 *
 * @see RowStructure
 */
class GuideI18n extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('guide.guide_i18n')
            ->setPrimaryKey(['supported_culture_id', 'guide_id'])
            ->addField('guide_id', 'int4')
            ->addField('supported_culture_id', 'bpchar')
            ->addField('name', 'text')
            ->addField('title', 'text')
            ->addField('editorial_content', 'jsonb')
            ->addField('keywords', 'text[]')
            ->addField('description', 'text')
            ->addField('updated_at', 'timestamptz');
    }
}
