<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\ReadQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure\GuidePressMagazine as GuidePressMagazineStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\GuidePressMagazine;

/**
 * GuidePressMagazineModel
 *
 * Model class for table guide_press_magazine.
 *
 * @see Model
 */
class GuidePressMagazineModel extends Model
{
    use ReadQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new GuidePressMagazineStructure();
        $this->flexible_entity_class = GuidePressMagazine::class;
    }
}
