<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\AutoStructure\CreditNote as CreditNoteStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CreditNote;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * CreditNoteModel
 *
 * Model class for table credit_note.
 *
 * @see Model
 */
class CreditNoteModel extends Model
{
    use ModelRelationTrait;
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new CreditNoteStructure();
        $this->flexible_entity_class = CreditNote::class;
    }
}
