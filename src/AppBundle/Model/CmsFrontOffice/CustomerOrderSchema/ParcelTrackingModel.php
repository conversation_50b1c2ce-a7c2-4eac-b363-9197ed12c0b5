<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\AutoStructure\ParcelTracking as ParcelTrackingStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\ParcelTracking;

/**
 * ParcelTrackingModel
 *
 * Model class for table parcel_tracking.
 *
 * @see Model
 */
class ParcelTrackingModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ParcelTrackingStructure();
        $this->flexible_entity_class = ParcelTracking::class;
    }
}
