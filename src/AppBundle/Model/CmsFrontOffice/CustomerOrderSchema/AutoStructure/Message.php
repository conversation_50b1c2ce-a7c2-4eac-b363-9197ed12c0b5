<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * Message
 *
 * Structure class for relation customer_order.message.
 * Message associated to an order
 *
 * customer_order_id:
 * Foreign key to order.
 * sender:
 * Message sender. Customer by default.
 *
 * @see RowStructure
 */
class Message extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this
            ->setRelation('customer_order.message')
            ->setPrimaryKey(['message_id'])
            ->addField('message_id', 'int4')
            ->addField('customer_order_id', 'int4')
            ->add<PERSON>ield('created_at', 'timestamptz')
            ->addField('sender', 'customer_order.message_sender')
            ->addField('content', 'text')
            ;
    }
}
