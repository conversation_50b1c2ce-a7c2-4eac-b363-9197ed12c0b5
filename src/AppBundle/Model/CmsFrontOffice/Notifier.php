<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice;

use SonVideo\Synapps\Client\Message;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\GenericEvent;

/**
 * Class Notifier
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice
 * <AUTHOR> <<EMAIL>>
 */
trait Notifier
{
    public function notify(EventDispatcherInterface $dispatcher, string $subject, array $payload): self
    {
        $dispatcher->dispatch('synapps.send', new GenericEvent(new Message($subject, $payload)));

        return $this;
    }
}
