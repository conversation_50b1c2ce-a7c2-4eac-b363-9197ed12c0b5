<?php
/*
 * This file is part of Melkart CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Form\Customer;

use PommProject\ModelManager\Session;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountModel;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints as Assert;
use SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer as CustomerAssert;

/**
 * Class AccountInformationFormType
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Form\Customer
 */
class AccountInformationFormType extends AbstractType
{
    private Session $pomm_session;

    /**
     * EditFormType constructor.
     */
    public function __construct(Session $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * buildForm
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', ChoiceType::class, [
                'label' => 'account_addresses_civility',
                'required' => true,
                'choices' => $this->getCivilities(),
                'constraints' => [new Assert\NotBlank(), new Assert\NotNull(), new CustomerAssert\Title()],
                'translation_domain' => 'account',
            ])
            ->add('firstname', TextType::class, [
                'label' => 'account_addresses_firstname',
                'required' => true,
                'constraints' => [new Assert\NotBlank(), new Assert\NotNull()],
                'translation_domain' => 'account',
            ])
            ->add('lastname', TextType::class, [
                'label' => 'account_addresses_lastname',
                'required' => true,
                'constraints' => [new Assert\NotBlank(), new Assert\NotNull()],
                'translation_domain' => 'account',
            ])
            ->add('cellphone', TextType::class, [
                'label' => 'account_addresses_mobile_phone',
                'required' => true,
                'constraints' => [
                    new Assert\NotBlank(),
                    new Assert\NotNull(),
                    new Assert\Regex([
                        'pattern' => '/^\+?\d+$/',
                        'message' => 'customer.information.address.cellphone.invalid',
                    ]),
                ],
            ])
            ->add('phone', TextType::class, [
                'label' => 'account_addresses_phone',
                'required' => false,
                'constraints' => [
                    new Assert\Regex([
                        'pattern' => '/^\+?\d+$/',
                        'message' => 'customer.information.address.phone.invalid',
                    ]),
                ],
            ])
            ->add('birthday', DateType::class, [
                'label' => 'account_addresses_birthday',
                'widget' => 'single_text',
                'required' => false,
            ]);

        $builder
            ->get('cellphone')
            ->addModelTransformer(
                new CallbackTransformer(
                    fn(?string $string): ?string => $this->from($string),
                    fn(?string $string): ?string => $this->to($string),
                ),
            );
        $builder
            ->get('phone')
            ->addModelTransformer(
                new CallbackTransformer(
                    fn(?string $string): ?string => $this->from($string),
                    fn(?string $string): ?string => $this->to($string),
                ),
            );
    }

    /**
     * from
     *
     * @param string|null $string
     *
     * @return string|null
     */
    public function from($string)
    {
        return $string;
    }

    /**
     * to
     *
     * @param string|null $string
     *
     * @return string|null
     */
    public function to($string)
    {
        $string = str_replace([' ', '.', '-'], '', $string);

        return $string === '' ? null : $string;
    }

    /**
     * getCountriesList
     */
    private function getCivilities(): array
    {
        $titles = $this->pomm_session->getModel(AccountModel::class)->getAvailableTitles();

        return array_combine(array_map(fn($title): string => 'short_civility_' . $title, $titles), $titles);
    }
}
