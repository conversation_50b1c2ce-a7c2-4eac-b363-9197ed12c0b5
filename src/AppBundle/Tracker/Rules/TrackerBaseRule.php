<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tracker\Rules;

use SonVideo\Cms\FrontOffice\AppBundle\Tracker\TrackerProviderContainer;

/**
 * Class TrackerBaseRule
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Tracker\Rules
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
class TrackerBaseRule
{
    private string $location;

    /**
     * @var TrackerProviderContainer
     */
    protected $provider_container;

    /**
     * TrackerBaseRule constructor.
     */
    public function __construct(string $location)
    {
        $this->location = $location;
    }

    /**
     * getLocation
     */
    public function getLocation(): string
    {
        return $this->location;
    }

    /**
     * setProviderContainer
     */
    public function setProviderContainer(TrackerProviderContainer $provider_container): self
    {
        $this->provider_container = $provider_container;

        return $this;
    }
}
