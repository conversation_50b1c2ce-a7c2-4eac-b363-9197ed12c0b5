<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Factory\Customer;

use PommProject\Foundation\Pomm;
use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Type\Advantage;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartManager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\ShipmentMethodModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketOrderLine;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Class OrderContextFactory
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Factory\Customer
 * <AUTHOR> Marniesse <<EMAIL>>
 */
class OrderContextFactory
{
    public const TYPE_CONTEXT_SHIPMENT = 'shipment';
    public const TYPE_CONTEXT_PAYMENT = 'payment';
    public const TYPE_CONTEXT_ARTICLE = 'article';
    public const TYPE_CONTEXT_PROMO_OFFER = 'promo_code';
    public const TYPE_CONTEXT_CUSTOMER_PROMO_CODE = 'customer_promo_code';
    public const TYPE_CONTEXT_QUOTE = 'quote';

    private Pomm $pomm;

    private ShoppingCartManager $shopping_cart_manager;

    private TokenStorageInterface $token_storage;

    private SessionInterface $session;

    private ?array $array = null;

    /**
     * Ip address of the customer
     */
    private ?string $ip_address = '127.0.0.1';

    /**
     * BasketOrderManager constructor.
     */
    public function __construct(
        Pomm $pomm,
        ShoppingCartManager $shopping_cart_manager,
        TokenStorageInterface $token_storage,
        SessionInterface $session
    ) {
        $this->pomm = $pomm;
        $this->shopping_cart_manager = $shopping_cart_manager;
        $this->token_storage = $token_storage;
        $this->session = $session;
    }

    /**
     * setIpAddress
     *
     * @return $this
     */
    public function setIpAddress(RequestStack $request): self
    {
        $current_request = $request->getCurrentRequest();

        if (!is_null($current_request)) {
            $this->ip_address = $current_request->getClientIp();
        }

        return $this;
    }

    /**
     * load
     *
     * Hydrate this object with basket order and basket order lines.
     */
    public function load(BasketOrder $basket_order, CollectionIterator $basket_order_lines): self
    {
        $this->createAllItems($basket_order_lines);

        $account = $this->token_storage->getToken()->getUser();
        $this->array['customer'] = is_null($account)
            ? []
            : [
                'customer_id' => $account->get('customer_id'),
                'email' => $account->get('email'),
            ];

        $this->array['shipping_address'] = $this->extractRealShippingAddress($basket_order, $this->array);
        $this->array['billing_address'] = $basket_order->get('billing_address');
        $estimated_delivery_date = $basket_order->get('estimated_delivery_date');
        $this->array['estimated_delivery_date'] =
            $estimated_delivery_date !== null ? $estimated_delivery_date->format('Y-m-d') : null;
        $this->array['comment'] = $basket_order->get('comment');
        $this->array['ip_address'] = $this->ip_address;

        if ($this->session->has(SESSION_KEY_IMPERSONATION_LOGIN)) {
            $this->array['impersonation'] = $this->session->get(SESSION_KEY_IMPERSONATION_LOGIN);
            $this->array['impersonation_staff_account'] = $this->session->get(SESSION_KEY_IMPERSONATION_STAFF_ACCOUNT);
        }

        $this->array['ecotax_price'] = $this->shopping_cart_manager->summary()['amount_ecotaxe'] ?? 0;

        return $this;
    }

    public function loadFromShoppingCartUsingAddress(array $address): OrderContextFactory
    {
        $this->loadItemsFromShoppingCart();
        $this->loadShoppingCartCustomer();
        $this->array['shipping_address'] = [
            'title' => null,
            'firstname' => null,
            'lastname' => null,
            'cellphone' => null,
            'city' => null,
            'postal_code' => $address['postal_code'],
            'address' => null,
            'country_code' => $address['country_id'],
        ];
        $this->array['billing_address'] = null;
        $this->array['comment'] = null;
        $this->array['ip_address'] = $this->ip_address;

        if ($this->session->has(SESSION_KEY_IMPERSONATION_LOGIN)) {
            $this->array['impersonation'] = $this->session->get(SESSION_KEY_IMPERSONATION_LOGIN);
            $this->array['impersonation_staff_account'] = $this->session->get(SESSION_KEY_IMPERSONATION_STAFF_ACCOUNT);
        }

        $this->array['ecotax_price'] = $this->shopping_cart_manager->summary()['amount_ecotaxe'] ?? 0;

        return $this;
    }

    /**
     * extractRealShippingAddress
     *
     *
     * @throws ModelException
     */
    protected function extractRealShippingAddress(BasketOrder $basket_order, array $data): ?array
    {
        // Base address data comes from the basket's shipping address
        $shipping_address = $basket_order->get('shipping_address');

        // Overwrite some data if shipping to a store
        if (isset($data['shipment_method']) && $data['shipment_method']['is_store']) {
            $store_address = $data['shipment_method']['tags']['store']['extra_data']['address'];

            $shipping_address = array_merge($shipping_address, [
                'company_name' => 'Son-Vidéo.com',
                'address' => $store_address['street'],
                'city' => $store_address['city'],
                'postal_code' => $store_address['zip_code'],
                'country_code' => 'FR',
            ]);
        }

        // Overwrite some data if shipping to a pickup location
        if (isset($data['shipment_method']) && $data['shipment_method']['is_relay']) {
            $pickup_data = $data['shipment_method']['extra_data'];

            $shipping_address = array_merge($shipping_address, [
                'company_name' => $pickup_data['name'],
                'address' => $pickup_data['address'],
                'city' => $pickup_data['city'],
                'postal_code' => $pickup_data['postal_code'],
                'country_code' => $pickup_data['country_code'],
            ]);
        }

        return $shipping_address;
    }

    /**
     * addCookies
     */
    public function addCookies(ParameterBag $cookies): self
    {
        $this->array['cookies'] = array_filter([
            'tradedoubler_id' => $cookies->get('tradedoubler_id'),
            'number_of_visits' => $cookies->get('popup') !== null ? (int) $cookies->get('popup') : null,
            'tracker_keyword' => $cookies->get('key'),
        ]);

        return $this;
    }

    /**
     * setOrderId
     *
     * @param  int|null $order_id
     */
    public function setOrderId($order_id): self
    {
        $this->array['order_id'] = $order_id;

        return $this;
    }

    /**
     * createAllItems
     */
    private function createAllItems(CollectionIterator $basket_order_lines): self
    {
        $shipment_method_model = $this->pomm->getDefaultSession()->getModel(ShipmentMethodModel::class);

        $shipment_method_lines = [];
        $this->array['items'] = [];
        foreach ($basket_order_lines as $one_line) {
            $id = $one_line->get('id');
            $one_line->clear('id');
            switch ($one_line->get('type')) {
                case BasketOrderLine::TYPE_SHIPMENT:
                    $one_line->set('shipment_method_id', $id);
                    $shipment_method_lines[] = $one_line;
                    break;
                case BasketOrderLine::TYPE_PAYMENT:
                    $one_line->set('payment_method_id', $id);
                    break;
                case BasketOrderLine::TYPE_ACCESSORY:
                case BasketOrderLine::TYPE_ARTICLE:
                    $one_line->set('article_id', $id);
                    break;
                case BasketOrderLine::TYPE_PROMO_OFFER:
                    $one_line->set('promo_code_id', $id);
                    break;
                case BasketOrderLine::TYPE_CUSTOMER_PROMO_CODE:
                    $one_line->set('customer_promo_code', $one_line->get('extra_data')['code'] ?? $id);
                    break;
                case BasketOrderLine::TYPE_QUOTE:
                    $one_line->set('quote_id', $id);
                    break;
                default:
                    throw new \RuntimeException(
                        sprintf('Basket order line type "%s" is not handled.', $one_line->get('type')),
                    );
            }

            $one_line->set('type', $this->getTypeForOrderLineContext($one_line->get('type')));
            $this->addItem($one_line);
        }

        $shipment_methods = $shipment_method_model->fetchWithList($shipment_method_lines);
        $this->array['shipment_method'] = is_null($shipment_methods) ? null : $shipment_methods->current()->extract();

        // add extra_data to the shipment method stored on order_line
        if (
            isset($this->array['shipment_method']) &&
            count($shipment_method_lines) === 1 &&
            isset($shipment_method_lines[0]['extra_data'])
        ) {
            $this->array['shipment_method']['extra_data'] = $shipment_method_lines[0]['extra_data'];
        }

        return $this;
    }

    /**
     * addItem
     *
     * Add an item from a BasketOrderLine.
     */
    private function addItem(BasketOrderLine $line): self
    {
        if (!isset($this->array['items']) || !is_array($this->array['items'])) {
            $this->array['items'] = [];
        }

        $new_item = $line->extract();
        $new_item['total_price'] = $line->get('price') * $line->get('quantity');

        $this->array['items'][] = $new_item;

        return $this;
    }

    /**
     * getTypeForOrderLineContext
     *
     * Convert the basket order line type to the order context line type.
     */
    private function getTypeForOrderLineContext(string $line_type): string
    {
        $correlation = [
            BasketOrderLine::TYPE_SHIPMENT => self::TYPE_CONTEXT_SHIPMENT,
            BasketOrderLine::TYPE_PAYMENT => self::TYPE_CONTEXT_PAYMENT,
            BasketOrderLine::TYPE_ARTICLE => self::TYPE_CONTEXT_ARTICLE,
            BasketOrderLine::TYPE_PROMO_OFFER => self::TYPE_CONTEXT_PROMO_OFFER,
            BasketOrderLine::TYPE_CUSTOMER_PROMO_CODE => self::TYPE_CONTEXT_CUSTOMER_PROMO_CODE,
            BasketOrderLine::TYPE_QUOTE => self::TYPE_CONTEXT_QUOTE,
            BasketOrderLine::TYPE_ACCESSORY => self::TYPE_CONTEXT_ARTICLE,
        ];

        if (!isset($correlation[$line_type])) {
            throw new \RuntimeException(sprintf('Basket order line type "%s" is not handled.', $line_type));
        }

        return $correlation[$line_type];
    }

    /**
     * @return mixed[]|null
     */
    public function getArray(): ?array
    {
        return $this->array;
    }

    protected function loadItemsFromShoppingCart(): void
    {
        $this->array['items'] = [];
        $line_no = 1;
        $line_no = $this->loadArticlesFromShoppingCart($line_no);
        $line_no = $this->loadAccessoriesFromShoppingCart($line_no);
        $line_no = $this->loadQuotesFromShoppingCart($line_no);
        $this->loadPromoCodesFromShoppingCart($line_no);
    }
    protected function loadArticlesFromShoppingCart(int $line_no): int
    {
        $articles = $this->shopping_cart_manager->getArticles();
        foreach ($articles as $article) {
            if ($article->get('unbasketable_reason') !== null) {
                continue;
            }

            $item = [
                'type' => self::TYPE_CONTEXT_ARTICLE,
                'article_id' => $article['article_id'],
                'sku' => $article['article_sku'],
                'price' => (float) $article['selling_price'],
                'price_vat_excluded' => (float) $article['selling_price_vat_excluded'],
                'quantity' => $article['quantity'],
                'total_price' => (float) $article['selling_price'] * $article['quantity'],
                'order_line_no' => $line_no++,
                'description' => $article['editorial_content']['basket_description'] ?? null,
                'extra_data' => [
                    'warranties' => $article['warranties_in_cart'],
                ],
            ];

            $this->array['items'][] = $item;
        }

        return $line_no;
    }

    protected function loadQuotesFromShoppingCart(int $line_no): int
    {
        $quotes = $this->shopping_cart_manager->getQuotes();
        foreach ($quotes as $quote) {
            $item = [
                'type' => self::TYPE_CONTEXT_QUOTE,
                'quote_id' => $quote['quote_id'],
                'price' => (float) $quote['quote_prices']['total_price_tax_included'],
                'price_vat_excluded' => (float) $quote['quote_prices']['total_price_tax_excluded'],
                'quantity' => $quote['quantity'],
                'total_price' => (float) $quote['quote_prices']['total_price_tax_included'] * $quote['quantity'],
                'order_line_no' => $line_no++,
                'description' => null,
                'extra_data' => [
                    'sub_type' => 'offer',
                    'items' => array_reduce(
                        $quote['quote_lines'],
                        function ($items, array $line) use ($quote) {
                            if ($line['type'] === 'product') {
                                $items[] = [
                                    'type' => self::TYPE_CONTEXT_ARTICLE,
                                    'sku' => $line['data']['product']['sku'],
                                    'quantity' => $line['data']['quantity'],
                                    'total_price' => $line['data']['total_price'],
                                    'total_discount_amount' => $line['data']['total_discount_amount'],
                                    'selling_price_tax_included' => $line['data']['selling_price_tax_included'],
                                    'selling_price_tax_excluded' => $line['data']['selling_price_tax_excluded'],
                                    'description' => $line['data']['product']['description'],
                                    // No warranty check when in a quote
                                ];
                            }

                            return $items;
                        },
                        [],
                    ),
                ],
            ];

            $this->array['items'][] = $item;
        }

        return $line_no;
    }

    protected function loadAccessoriesFromShoppingCart(int $line_no): int
    {
        $accessories = $this->shopping_cart_manager->getAccessories();
        foreach ($accessories as $accessory) {
            $item = [
                'type' => self::TYPE_CONTEXT_ARTICLE,
                'article_id' => $accessory['accessory_id'],
                'sku' => $accessory['accessory_sku'],
                'price' => (float) $accessory['selling_price'],
                'price_vat_excluded' => (float) $accessory['selling_price_vat_excluded'],
                'quantity' => $accessory['quantity'],
                'total_price' => (float) $accessory['selling_price'] * $accessory['quantity'],
                'order_line_no' => $line_no++,
                'description' => $accessory['short_description'],
                'extra_data' => null,
            ];

            $this->array['items'][] = $item;
        }

        return $line_no;
    }

    protected function loadPromoCodesFromShoppingCart(int $line_no): int
    {
        $promo_code_advantages = $this->shopping_cart_manager->getPromoCodeAdvantage();
        /** @var Advantage $promo */
        foreach ($promo_code_advantages as $promo) {
            $formatted_promo = $promo->format();
            $item = [
                'type' => $this->getTypeForOrderLineContext($promo->provider_type),
                'promo_code_id' => $promo->getPromoId(),
                'price' => $formatted_promo['price'],
                'price_vat_excluded' => $formatted_promo['price'],
                'quantity' => 1,
                'total_price' => $formatted_promo['price'],
                'order_line_no' => $line_no++,
                'description' => $formatted_promo['code'],
                'extra_data' => $formatted_promo,
            ];

            $this->array['items'][] = $item;
        }

        return $line_no;
    }

    /**
     * For an obscure reason, Bridge requires user data to create its own OrderContext.
     * Give it fake data for anonymous users.
     */
    protected function loadShoppingCartCustomer(): void
    {
        $account = $this->token_storage->getToken()->getUser();
        $this->array['customer'] =
            $account instanceof UserInterface
                ? [
                    'customer_id' => $account->get('customer_id'),
                    'email' => $account->get('email'),
                ]
                : [
                    'customer_id' => 0,
                    'email' => 'nobody@bridge',
                ];
    }
}
