<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\CustomerService;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\SystemSchema\ParameterModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class ImmediateRecallController
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget
 *
 * @Route("/rappel-immediat")
 */
class ImmediateRecallController extends AbstractImmediateRecallController
{
    /**
     * @Route("/popin",
     *     name="immediate_recall_popin_get",
     *     methods={"GET"})
     */
    public function getFormAction(Request $request): Response
    {
        $this->checkRequest($request);

        $params = $this->getContentDefaultParametersWith();

        try {
            $params = $this->addCommonPropertiesTo($params);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception]);

            $params['is_in_maintenance'] = true;
        }

        return $this->renderWith($params, [
            'hide_buttons' => $params['is_in_maintenance'] || !$params['is_active'],
        ]);
    }

    /**
     * @Route("/popin",
     *     name="immediate_recall_popin_post",
     *     methods={"POST"})
     */
    public function postAction(Request $request): Response
    {
        $this->checkRequest($request);

        $data = $this->handleForm($request->request->all());

        return $this->renderWith($data['params'], $data['response_options']);
    }

    /**
     * @deprecated
     * @see JSON API: \SonVideo\Cms\FrontOffice\AppBundle\Controller\Api\CustomerServiceController::getAction
     *
     * Plus, this should have been in a dedicated controller
     */
    public function showTextAction(bool $isShort = false)
    {
        $parameter = $this->getPommDefaultSession()->getModel(ParameterModel::class)->extractLastAncestorsFromPath('customer_service');

        return $this->render('AppBundle:Layout:v3/customer_service_text.html.twig', ['text' => $isShort ? $parameter['hours_short'] : $parameter['hours']]);
    }

    /**
     * renderWith
     *
     *
     */
    private function renderWith(array $params, array $response_options): JsonResponse
    {
        $params['account_information'] = $response_options['account_information'] ?? $this->getAccountInformation();

        return new JsonResponse(array_merge([
            'hide_buttons'        => false,
            'content'             => $this->renderView('AppBundle:CustomerService/ImmediateRecall:popin.html.twig', $params),
        ], $response_options));
    }
}
