<?php
/*
 * This file is part of Melkart CMS FrontOffice package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Advice\Question;

use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\ValidatorException;
use SonVideo\Cms\FrontOffice\AppBundle\Form\Advice\AskOrAnswerType;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\QuestionCreator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class AskAQuestionController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Advice\Question
 *
 * @Route("/poser-une-question")
 */
class AskAQuestionController extends BaseController
{
    /**
     * getAction
     *
     *
     * @throws \LogicException
     * @Route(name="ask_a_question_get", methods={"GET"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getAction(Request $request): Response
    {
        // Extract article_id from the request
        $article_id = $request->get('article');
        if ($article_id === null) {
            throw $this->createNotFoundException($this->get('translator')->trans('missing_article', [], 'comment'));
        }

        $article = $this->retrieveArticle($article_id, $request->getLocale());
        $form = $this->getForm();

        return $this->renderWith($form, $article);
    }

    /**
     * @Route(name="ask_a_question_post", methods={"POST"})
     *
     * @Security("has_role('SVD_CUSTOMER')")
     *
     *
     * @throws \LogicException
     * @throws NotFoundHttpException
     */
    public function postAction(Request $request): Response
    {
        $article = $this->retrieveArticle($request->get('article'), $request->getLocale());
        $form = $this->getForm()->handleRequest($request);

        try {
            if ($form->isSubmitted() && !$form->isValid()) {
                throw new ValidatorException('Question validation failed');
            }

            $data = $form->getData();

            $this->get(QuestionCreator::class)->create($data, $article, $request->getLocale());

            $this->addFlash(
                'submission_success',
                $this->get('translator')->trans(
                    'submission_success',
                    [
                        '%article_url%' => $article['article_url'],
                    ],
                    'question',
                ),
            );

            return $this->redirectToRoute('ask_a_question_success');
        } catch (ValidatorException $e) {
            // Do nothing as validation errors are displayed through the form created view
        } catch (\Exception $e) {
            // Log remaining exceptions
            $this->get('logger')->error($e->getMessage(), [
                'exception' => $e,
                'submitted' => $form->getData(),
            ]);

            $this->addFlash(
                'submission_failure',
                $this->get('translator')->trans('submission_failure', [], 'question'),
            );
        }

        return $this->renderWith($form, $article);
    }

    /**
     * Handling of a successful question submission
     *
     * @Route("/succes", name="ask_a_question_success", methods={"GET"})
     *
     * @throws \LogicException
     */
    public function successAction(): Response
    {
        $response = $this->render('AppBundle:Advice:question/ask_success.html.twig');
        $response->headers->addCacheControlDirective('no-cache', true);

        return $response;
    }

    /**
     * retrieveArticle
     *
     *
     * @throws NotFoundHttpException
     */
    protected function retrieveArticle(int $article_id, string $locale): Article
    {
        $article = $this->get('app.manager.article.article_fetcher')->getArticleByIdAndCulture($article_id, $locale);

        // Throw an exception if no article found
        if (!$article instanceof Article) {
            throw $this->createNotFoundException($this->get('translator')->trans('missing_article', [], 'comment'));
        }

        return $article;
    }

    /**
     * getForm
     */
    protected function getForm(): FormInterface
    {
        return $this->createForm(
            AskOrAnswerType::class,
            [],
            [
                'type' => 'ask',
                'account_information' => $this->getAccountInformation(),
            ],
        );
    }

    /**
     * renderWith
     *
     *
     * @throws \LogicException
     */
    protected function renderWith(FormInterface $form, Article $article): Response
    {
        $response = $this->render('AppBundle:Advice:question/ask.html.twig', [
            'form' => $form->createView(),
            'errors' => $form->getErrors(true),
            'article' => $article,
        ]);

        $response->headers->addCacheControlDirective('no-cache', true);

        return $response;
    }
}
