<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller;

use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrderModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\SiteReviewModel;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * SiteReviewsController
 *
 * @package   CMS Front-Office
 * @copyright 2017 Son-Video Distribution
 */
class SiteReviewsController extends BaseController
{
    public const REVIEWS_PER_PAGE = 30;

    /**
     * landingAction
     *
     * Landing page of the site reviews
     *
     *
     * @Route("/avis-clients", name="site_reviews", methods={"GET"})
     */
    public function landingAction(Request $request): Response
    {
        $page = $request->get('page', 1);
        $site_review_model = $this->getPommDefaultSession()->getModel(SiteReviewModel::class);

        // number of years since january 2000
        $years_of_service = date_diff(new \DateTime('2000-01-01'), new \DateTime())->y;

        $nb_of_customer = $this->getPommDefaultSession()
            ->getModel(CustomerOrderModel::class)
            ->getNumberOfCustomer();
        $percent_of_recommendation = $site_review_model->getRecommendationPercentage();
        $average_score = $site_review_model->getAverageScore();
        $pager = $site_review_model->getLatestReviews(static::REVIEWS_PER_PAGE, $page, $request->getLocale());
        $canonical_url = $this->generateUrl('site_reviews');

        // set Varnish tag
        $this->addTags(['site_reviews']);

        return $this->render('AppBundle:SiteReviews:page.html.twig', [
            'years_of_service' => $years_of_service,
            'nb_of_customer' => $nb_of_customer,
            'percent_of_recommendation' => $percent_of_recommendation,
            'average_score' => $average_score,
            'pager' => $pager,
            'canonical_url' => $canonical_url,
        ]);
    }
}
