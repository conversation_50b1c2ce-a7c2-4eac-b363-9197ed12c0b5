<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2015 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller;

use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\SearchSchema\AutocompleteModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * SearchController
 *
 * Controller for search API routes.
 *
 * @package     cms-front
 * @copyright   2016 Son-Video Distribution
 * <AUTHOR> HUBERT <<EMAIL>>
 * @see         BaseController
 *
 * @Route("/autocompletion")
 */
class AutocompleteController extends BaseController
{
    public const AUTOCOMPLETE_LIMIT = 60;

    /**
     * api
     *
     * Perform an autocomplete search.
     *
     *
     * @Route("/api", name="api_autocomplete", methods={"GET"})
     */
    public function api(Request $request): Response
    {
        $_locale = $request->getLocale();
        $word = $request->query->get('word', '');
        $limit = $request->query->get('limit');
        $item_type = $request->query->get('type');

        if (mb_strlen($word) < 3) {
            return new JsonResponse(
                [
                    'error' => sprintf(
                        "Minimum requirement not fulfilled, '%s' must be at least 3 letters long.",
                        $word,
                    ),
                ],
                Response::HTTP_BAD_REQUEST,
            );
        }

        $proposals = $this->getPommDefaultSession()
            ->getModel(AutocompleteModel::class)
            ->findLike($word, $_locale, $limit ?? static::AUTOCOMPLETE_LIMIT, $item_type);

        return new JsonResponse([
            'request' => ['word' => $word, 'culture' => $_locale, 'performed_at' => new \DateTime()],
            'proposals' => $proposals,
        ]);
    }

    /**
     * productAction
     *
     * Perform an autocomplete search on the products only.
     *
     *
     * @Route("/product", name="product_autocomplete", methods={"GET"})
     */
    public function productAction(Request $request): Response
    {
        $_locale = $request->getLocale();
        $word = trim($request->query->get('word', ''));
        $limit = $request->query->get('limit', static::AUTOCOMPLETE_LIMIT);

        if ($word === '') {
            return new JsonResponse(
                [
                    'error' => $this->get('translator')->trans(
                        'please_enter_min_characters',
                        [
                            'min' => 1,
                        ],
                        'component',
                    ),
                ],
                Response::HTTP_BAD_REQUEST,
            );
        }

        $proposals = $this->getPommDefaultSession()
            ->getModel(AutocompleteModel::class)
            ->findProductsLike($word, $_locale, $limit);

        return new JsonResponse([
            'request' => ['word' => $word, 'culture' => $_locale, 'performed_at' => new \DateTime()],
            'proposals' => $proposals,
        ]);
    }
}
