<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller;

use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Cache\CacheTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\RequestApiException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Account;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformation;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformationModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\Notifier;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PublicSchema\CountryModel;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;

/**
 * BaseController
 *
 * Common operations shared by all controllers of application.
 *
 * @package     CMS Front-Office
 * @copyright   2016 Son-Video Distribution
 * <AUTHOR> DUBOIS <<EMAIL>>
 * @see         Controller
 */
abstract class BaseController extends Controller
{
    use Notifier;
    use CacheTrait;

    public const AUTH_PROVIDER_KEY = 'main';

    protected function getPommDefaultSession(): PommSession
    {
        return $this->get('pomm')->getDefaultSession();
    }

    /**
     * getCustomerId
     */
    protected function getCustomerId(): ?int
    {
        $user = $this->getUser();

        if (!$user instanceof Account) {
            return null;
        }

        return $user->get('customer_id');
    }

    protected function inform(string $subject, array $data = [])
    {
        $event_dispatcher = $this->get('event_dispatcher');
        $this->notify($event_dispatcher, $subject, $data);
    }

    /**
     * trans
     *
     * Alias for translate.
     *
     * @return string
     */
    protected function trans(string $id, array $parameters = [], string $domain = '')
    {
        return $this->get('translator')->trans($id, $parameters, $domain);
    }

    /**
     * getCountryCodes
     */
    protected function getCountryCodes(): array
    {
        $countries = $this->get('pomm')
            ->getDefaultSession()
            ->getModel(CountryModel::class)
            ->findAll();

        $indexed_countries = [];
        foreach ($countries as $country) {
            $indexed_countries[$country->get('country_id')] = $country->get('name');
        }

        return $indexed_countries;
    }

    /**
     * getAccountInformation
     *
     * @return null|AccountInformation
     */
    protected function getAccountInformation()
    {
        $customer_id = $this->getCustomerId();

        if ($customer_id === null) {
            return null;
        }

        return $this->getPommDefaultSession()
            ->getModel(AccountInformationModel::class)
            ->retrieveOrCreate($customer_id);
    }

    /**
     * hasNewsletter
     *
     * @return bool|null
     */
    protected function hasNewsletter()
    {
        $account_information = $this->getAccountInformation();

        if (!$account_information instanceof AccountInformation) {
            return null;
        }

        return isset($account_information['preferences']['newsletter']) &&
            $account_information['preferences']['newsletter'] === true;
    }

    /**
     * mustBeXmlHttpRequest
     *
     * @throws RequestApiException
     */
    protected function mustBeXmlHttpRequest(Request $request)
    {
        // If environment is "test", we do not throw exception.
        // This is done because header can not be set in Behatch rest context. Some MR are in progress to fix this bug:
        //   https://github.com/Behatch/contexts/pull/218
        //   https://github.com/Behatch/contexts/pull/212
        // We can remove the environment condition as soon the bug is fixed

        if (!$request->isXmlHttpRequest() && $this->get('kernel')->getEnvironment() !== 'test') {
            throw new RequestApiException('Bad request, it must be XMLHttpRequest.');
        }
    }

    /**
     * logUser
     *
     * Try to log a user with email and password.
     */
    protected function logUser(Request $request, string $email, string $password): self
    {
        $token = new UsernamePasswordToken($email, $password, self::AUTH_PROVIDER_KEY);

        $authenticate_token = $this->get('security.authentication.manager')->authenticate($token);
        $this->get('security.token_storage')->setToken($authenticate_token);

        $event = new InteractiveLoginEvent($request, $authenticate_token);
        $this->get('event_dispatcher')->dispatch('security.interactive_login', $event);

        return $this;
    }
}
