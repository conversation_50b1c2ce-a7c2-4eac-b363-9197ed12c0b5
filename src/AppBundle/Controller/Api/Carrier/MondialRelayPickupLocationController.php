<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api\Carrier;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\DataFormatter\StringFormatter;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class MondialRelayPickupLocationController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Api\Carrier
 */
class MondialRelayPickupLocationController extends BaseController
{
    public const DAYS = [
        'Samedi' => 'saturday',
        'Dimanche' => 'sunday',
        'Lundi' => 'monday',
        'Mardi' => 'tuesday',
        'Mercredi' => 'wednesday',
        'Jeudi' => 'thursday',
        'Vendredi' => 'friday',
    ];

    protected $country_code;

    /**
     * getList
     *l
     * Returns the list of nearby pickup locations for Mondial Relay service
     *
     *
     *
     * @Route("/api/points-retrait/mondial-relay", name="api_pickup_mondial_relay_get", methods={"GET"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getList(Request $request): JsonResponse
    {
        $params = [
            'Ville' => preg_replace('/[^A-Za-z\d]/', ' ', StringFormatter::transliterate($request->get('city', ''))),
            'CP' => $request->get('postal_code', ''),
        ];

        try {
            $mondial_relay_api = $this->get('app.client.mondial_relay');
            $locations = $mondial_relay_api->searchPickupPointWith($request->get('shipment_method_id'), $params);
            $formatted_locations = array_map(
                fn(\stdClass $location): array => $this->formatLocation($location),
                $locations,
            );

            return new JsonResponse($formatted_locations);
        } catch (\Exception $exception) {
            $this->get('logger')->error('mondial_relay.pickup_location', ['exception' => $exception]);

            return new JsonResponse([], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @return array{
     *     relay_id: mixed,
     *     coords: array{lat: float, long: float},
     *     name: mixed,
     *     address: mixed,
     *     address_supplement: array{line2: mixed, line3: mixed, line4: mixed},
     *     postal_code: mixed,
     *     city: mixed,
     *     country_code: mixed,
     *     opening_hours: mixed[],
     *     photo: mixed,
     *     holidays: mixed[]
     * }
     */
    protected function formatLocation(\stdClass $location): array
    {
        $opening_hours = $this->formatOpeningHoursPerDays($location);
        $holidays = [];

        if (property_exists($location->Informations_Dispo, 'Periode')) {
            $holidays = $this->collectClosedHours($location->Informations_Dispo->Periode);
        }

        return [
            'relay_id' => $location->Num,
            'coords' => [
                'lat' => $this->formatCoordinate($location->Latitude),
                'long' => $this->formatCoordinate($location->Longitude),
            ],
            'name' => $location->LgAdr1,
            'address' => $location->LgAdr3,
            'address_supplement' => [
                'line2' => $location->LgAdr2,
                'line3' => $location->LgAdr4,
                'line4' => $location->Localisation1,
            ],
            'postal_code' => $location->CP,
            'city' => $location->Ville,
            'country_code' => $location->Pays,
            'opening_hours' => $opening_hours,
            'photo' => $location->URL_Photo,
            'holidays' => $holidays,
        ];
    }

    /**
     * formatCoordinate
     *
     *
     */
    protected function formatCoordinate(string $coord): float
    {
        return (float) trim(str_replace(',', '.', $coord));
    }

    /**
     * formatDays
     *
     *
     */
    protected function formatOpeningHoursPerDays(\stdClass $location): array
    {
        $opening_hours = [];
        foreach (static::DAYS as $day => $day_key) {
            $property_name = sprintf('Horaires_%s', $day);
            if (property_exists($location, $property_name)) {
                $opening_hours[strtolower($day_key)] = array_merge(
                    [
                        'name' => $day_key,
                    ],
                    $this->collectOpenedHours($location->{$property_name}->string),
                );
            }
        }

        return $opening_hours;
    }

    /**
     * collectOpenedHours
     *
     *
     * @return array{morning: string|null, afternoon: string|null}
     */
    protected function collectOpenedHours(array $hours): array
    {
        $formatted = [
            'morning' => null,
            'afternoon' => null,
        ];
        $next_key = 'morning';
        $skip_next = false;
        foreach ($hours as $index => $hour) {
            if ($hour === '0000') {
                continue;
            }

            // Hours are formatted by range (09h00 - 12h00 then 14h00)
            // Therefore, if a couple just been formatted we skip the next line as it has already been
            // put in the hours range
            if ($skip_next) {
                // And tell the script to use the next available value
                $skip_next = false;

                continue;
            }

            // Some store are open without interruption on a given day
            $open_at = (int) substr($hour, 0, 2);
            $close_at = (int) substr($hours[$index + 1], 0, 2);
            if ($open_at >= 12 || ($close_at >= 12 && $open_at >= 12)) {
                $next_key = 'afternoon';
            }

            // But we can't be sure that a store do not use odd hours like (09h00 - 11h00 then 11h30 - 19H00)
            // Therefore we may have to reassign to morning if that prove necessary
            if ($index > 1) {
                $formatted['morning'] = $formatted['afternoon'];
            }

            $formatted[$next_key] = sprintf(
                '%sh%s - %sh%s',
                $open_at,
                substr($hour, 2),
                $close_at,
                substr($hours[$index + 1], 2),
            );

            $skip_next = true;
            $next_key = 'afternoon';
        }

        return $formatted;
    }

    /**
     * collectClosedHours
     *
     * @param array|\stdClass $holidays
     */
    public function collectClosedHours($holidays): array
    {
        $items = [];

        // Mondial Relay has the outstanding good idea of sending back an Array when there are multiple results
        // And flatten it when if there is only one. therefore...
        if (is_array($holidays)) {
            foreach ($holidays as $item) {
                $items[] = [
                    'start_date' => $item->Debut,
                    'end_date' => $item->Fin,
                ];
            }
        } else {
            $items[] = [
                'start_date' => $holidays->Debut,
                'end_date' => $holidays->Fin,
            ];
        }

        return $items;
    }
}
