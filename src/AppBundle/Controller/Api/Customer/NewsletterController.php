<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api\Customer;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\NewsletterManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class NewsletterController extends BaseController {

    const RPC_METHOD_UNSUBSCRIBE_NEWSLETTER = 'customer:unsubscribe_newsletter';

    private $newsletter_manager;

    public function __construct(NewsletterManager $newsletter_manager) {
        $this->newsletter_manager = $newsletter_manager;
    }

    /**
     * @Route(
     *     "/api/customer/unsubscribe/{token}",
     *     name="api_customer_unsubscribe_newsletter",
     *     methods={"GET"}
     * )
     */
    public function unsubscribe(string $token) {

        try {
            $this->newsletter_manager->unsubscribe($token);
        } catch (\Exception $e) {
            return new JsonResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        }

        return new JsonResponse(['status' => 'success']);
    }
    
}