<?php
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api\Customer;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformationModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ChangeShowCancelledOrdersController extends BaseController
{
    /**
     * @Route(
     *     "/api/customer/update-show-cancelled-orders",
     *     name="api_customer_update_show_cancelled_orders",
     *     methods={"POST"}
     * )
     */
    public function changeShowCancelledOrdersAction(Request $request): JsonResponse
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse([], Response::HTTP_BAD_REQUEST);
        }

        $post_data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $show_cancelled_orders = $post_data['show_cancelled_orders'];

        try {
            $this->save($show_cancelled_orders);
        } catch (\Exception $exception) {
            return JSendResponse::error('La sauvegarde du choix à échouée');
        }

        return JSendResponse::success([
            'show_cancelled_orders' => $show_cancelled_orders,
        ]);
    }

    private function save(bool $show_cancelled_orders): void
    {
        $account_information = $this->getAccountInformation();
        $account_information->set(
            'preferences',
            array_merge($account_information['preferences'] ?? [], [
                'show_cancelled_orders' => $show_cancelled_orders,
            ]),
        );

        $this->getPommDefaultSession()
            ->getModel(AccountInformationModel::class)
            ->updateOne($account_information, ['preferences']);
    }
}
