<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\ShipmentMethodFetcher;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class StoreController
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Api
 */
class StoreController extends BaseController
{
    /**
     * Return list of store with distance between then and the zipcode
     *
     *
     * @Route("/api/liste-emport-depot", name="get_list_of_store", methods={"GET"})
     */
    public function getListOfStoreByZipCodeAndStore(Request $request): JsonResponse
    {
        try {
            $stores_information = $this->get(ShipmentMethodFetcher::class)->listOfStoreByDistanceByZipCode(
                $request->get('zipcode'),
            );
        } catch (\Exception $e) {
            return JSendResponse::fail([
                'error' => $e->getMessage(),
            ]);
        }

        if (0 === (is_countable($stores_information['stores']) ? count($stores_information['stores']) : 0)) {
            return JSendResponse::fail([
                'error' => 'no store found',
            ]);
        }

        return JSendResponse::success([
            'stores' => $stores_information['stores'],
            'had_fallback_to_department_search' => $stores_information['had_fallback_to_department_search'],
        ]);
    }
}
