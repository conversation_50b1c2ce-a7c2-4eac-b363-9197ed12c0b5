<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api;

use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\CustomerService\AbstractImmediateRecallController;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CustomerServiceImmediateRecallController extends AbstractImmediateRecallController
{
    /**
     *
     * @Route(
     *     "/api/customer-service/immediate-recall",
     *     name="api_get_customer_service_immediate_recall",
     *     methods={"GET"}
     * )
     */
    public function getAction(Request $request): JsonResponse
    {
        $this->checkRequest($request);

        $params = $this->getContentDefaultParametersWith();

        try {
            $params = $this->addCommonPropertiesTo($params);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception]);

            $params['is_in_maintenance'] = true;
        }

        return $this->respondWith($params, [
            'hide_buttons' => $params['is_in_maintenance'] || !$params['is_active'],
        ]);
    }

    /**
     * @Route("/api/customer-service/immediate-recall",
     *     name="api_post_customer_service_immediate_recall",
     *     methods={"POST"}
     * )
     */
    public function postAction(Request $request): Response
    {
        $this->checkRequest($request);

        $data = $this->handleForm(json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR));

        return $this->respondWith($data['params'], $data['response_options']);
    }

    private function respondWith(array $params, array $response_options): JsonResponse
    {
        $params['account_information'] = $response_options['account_information'] ?? $this->getAccountInformation();

        unset($response_options['account_information'], $params['form_email'], $params['form_phone']);

        return JSendResponse::success(array_merge([
            'hide_buttons'        => false,
            'content'             => $params,
        ], $response_options));
    }

}
