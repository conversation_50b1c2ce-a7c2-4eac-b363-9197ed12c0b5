<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Session;

/**
 * Class BasketableCollection
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Session
 * <AUTHOR> <<EMAIL>>
 */
class BasketableCollection implements \Countable, \Iterator
{
    private array $collection = [];

    /**
     * BasketableCollection constructor.
     */
    public function __construct(array $articles)
    {
        foreach ($articles as $article_id => $quantity) {
            $this->addBasketable($article_id, $quantity);
        }
    }

    /**
     * addBasketable
     *
     * @param mixed $id
     */
    public function addBasketable($id, int $quantity): self
    {
        if (isset($this->collection[$id])) {
            $this->collection[$id]->addToQuantity($quantity);
        } else {
            $this->collection[$id] = new Basketable($quantity);
        }

        return $this;
    }

    /**
     * removeBasketable
     *
     * @param mixed $id
     */
    public function removeBasketable($id): self
    {
        unset($this->collection[$id]);

        return $this;
    }

    /**
     * setBasketableQuantity
     *
     * @param mixed $id
     */
    public function setBasketableQuantity($id, int $quantity): self
    {
        if (isset($this->collection[$id])) {
            $this->collection[$id]->setQuantity($quantity);
        } else {
            $this->collection[$id] = new Basketable($quantity);
        }

        return $this;
    }

    /**
     * count
     */
    public function count(): int
    {
        return count($this->collection);
    }

    /**
     * extract
     */
    public function extract(): array
    {
        return array_map(fn($object) => $object->extract(), $this->collection);
    }

    /**
     * getBasketable
     *
     * @param mixed $id
     */
    public function getBasketable($id): Basketable
    {
        if (isset($this->collection[$id])) {
            return $this->collection[$id];
        }

        throw new \InvalidArgumentException(sprintf('Id \'%s\' is not found in collection.', $id));
    }

    /**
     * rewind
     */
    public function rewind(): void
    {
        reset($this->collection);
    }

    /**
     * current
     *
     * @return mixed
     */
    public function current()
    {
        return current($this->collection);
    }

    /**
     * key
     *
     * @return mixed
     */
    public function key()
    {
        return key($this->collection);
    }

    /**
     * next
     */
    public function next(): void
    {
        next($this->collection);
    }

    /**
     * valid
     *
     * @return bool
     */
    public function valid()
    {
        return key($this->collection) !== null;
    }
}
