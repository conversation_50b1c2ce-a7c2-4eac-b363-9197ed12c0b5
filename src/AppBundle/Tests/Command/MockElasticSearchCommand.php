<?php
/*
 * This file is part of FOCMS package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Command;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Mock\Client\ElasticsearchClientMock;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class MockElasticSearchCommand extends Command
{
    protected static $defaultName = 'cms:mock-elastic-search';

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setDescription('Mock the next ES search with given response.')->addOption(
            'result',
            null,
            InputOption::VALUE_REQUIRED,
            'The result of the next ES search.',
        );
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $result = json_decode($input->getOption('result'), true, 512, JSON_THROW_ON_ERROR);
        ElasticsearchClientMock::setNextSearchResults($result);
    }
}
