Feature: The CMS has guide
  In order to navigate on the site
  As a guest visitor
  I need to have guide

  <PERSON><PERSON><PERSON>: Display guide associated stand
    Given   I am on "/guide/guide-01"
    # other guides
    Then    I should see "Consultez nos guides home-cinéma pour bien choisir"
    When    I inspect element ".guide-more"
    And     I should see 6 "div.card" elements in inspected element
    # associated stand
    When    I inspect element ".SVDv3_guide_catassoc"
    And     I should see "Vidéoprojecteurs" in the "li > a[href='/rayon/television/videoprojecteurs']" element of inspected element
    And     I should see "Pico projecteurs" in the "li > a[href='/rayon/television/videoprojecteurs/picoprojecteurs']" element of inspected element

  Scenario: Check structured data
    Given   I am on "/guide/guide-01"
    Then    I should see "\"@context\":\"http:\/\/schema.org\"" in the "script[type='application/ld+json']" element
    And     I should see "\"@type\":\"BreadcrumbList\"" in the "script[type='application/ld+json']" element
    And     I should see "{\"@type\":\"ListItem\",\"position\":1,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/guides\",\"name\":\"Les guides\"}}" in the "script[type='application/ld+json']" element
    And     I should see "{\"@type\":\"ListItem\",\"position\":2,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/guide\/guide-01\",\"name\":\"Guide 01\"}}" in the "script[type='application/ld+json']" element

  @javascript
  Scenario: check trackers
    Given   I am on "/guide/guide-01"
    Then    The Iadvize custom data variable should be set to "{\"page_type\": \"listing\", \"product_category\": \"Home-cinéma\", \"product_subcategory1\": \"Haute-fidélité\", \"product_subcategory2\": \"Décoration\", \"device\": \"desktop\",\"flag_panier_initie\":0,\"erreur_connexion\":0,\"cart_amount\":0}"


  Scenario: Check root controller redirection
    Given   I am on "/guide"
    Then    I should be on url ending by "/guides"
    When    I am on "/guide/"
    Then    I should be on url ending by "/guides"
