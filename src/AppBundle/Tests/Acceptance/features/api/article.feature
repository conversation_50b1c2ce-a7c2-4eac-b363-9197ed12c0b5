Feature: check the CMS front API
  In order to retrieve articles
  As a guest visitor
  I need to have an API that returns article infos

  Scenario: Get all articles by brand and category
    Given   I add "X-Requested-With" header equal to "XMLHttpRequest"
    And     I send a "GET" request to "/api/articles" with parameters:
    | key         | value |
    | brand_id    | 343   |
    | category_id | 2     |
    Then    the response status code should be 200
    And     the JSON node "status" should be equal to the string "success"
    And     the JSON node "data" should exist
    And     the JSON node "data->articles" should exist
    And     the JSON node "data->articles" should have 3 elements
    And     the JSON node "data->articles[0]->article_id" should exist
    And     the JSON node "data->articles[0]->sku" should exist
    And     the JSON node "data->articles[0]->name" should exist
    And     the JSON node "data->articles[0]->article_url" should exist

  Scenario: Get all articles for non existing brand
    Given   I add "X-Requested-With" header equal to "XMLHttpRequest"
    And     I send a "GET" request to "/api/articles" with parameters:
      | key         | value |
      | brand_id    | 0     |
      | category_id | 2     |
    Then    the response status code should be 200
    And     the JSON node "status" should be equal to the string "success"
    And     the JSON node "data" should exist
    And     the JSON node "data->articles" should exist
    And     the JSON node "data->articles" should have 0 element

  Scenario: Get all articles for non existing category
    Given   I add "X-Requested-With" header equal to "XMLHttpRequest"
    And     I send a "GET" request to "/api/articles" with parameters:
      | key         | value |
      | brand_id    | 343   |
      | category_id | 0     |
    Then    the response status code should be 200
    And     the JSON node "status" should be equal to the string "success"
    And     the JSON node "data" should exist
    And     the JSON node "data->articles" should exist
    And     the JSON node "data->articles" should have 0 element
