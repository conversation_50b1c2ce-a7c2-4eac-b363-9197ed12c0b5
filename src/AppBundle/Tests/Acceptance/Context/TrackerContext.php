<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context;

use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\PyStringNode;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\Mink\Exception\ExpectationException;
use Behat\MinkExtension\Context\RawMinkContext;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary\CommonDictionaryTrait;

/**
 * Class TrackerContext
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class TrackerContext extends RawMinkContext implements Context
{
    use CommonDictionaryTrait;

    /**
     * trackerMustContains
     *
     * Example: A tracker must contains "bla bla"
     *
     *
     * @throws ElementNotFoundException
     * @Then /^A tracker must contains "(?P<content>[^"]*)"$/
     */
    public function trackerMustContains(string $content): void
    {
        $blocks = $this->getSession()
            ->getPage()
            ->findAll('css', sprintf('script:contains(\'%s\')', $content));

        if ($blocks === []) {
            throw new ElementNotFoundException($this->getSession(), 'Tracker');
        }
    }

    /**
     * iadvizeCustomDataVariableShouldBeSetTo
     *
     *
     * @Then /^The Iadvize custom data variable should be set to "(?P<data>(?:[^"]|\\")*)"$/
     */
    public function iadvizeCustomDataVariableShouldBeSetTo(string $data): void
    {
        $this->checkIadvizeVariableValue('idzCustomData', $data);
    }

    /**
     * iadvizeTransactionVariableShouldBeSetTo
     *
     *
     * @Then /^The Iadvize transaction variable should be set to "(?P<data>(?:[^"]|\\")*)"$/
     */
    public function iadvizeTransactionVariableShouldBeSetTo(string $data): void
    {
        $this->checkIadvizeVariableValue('idzTrans', $data);
    }

    /**
     * checkIadvizeVariableValue
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     */
    private function checkIadvizeVariableValue(string $variable, string $expected_data): void
    {
        // Decode Iadvize data expected
        $expected = json_decode($this->fixStepArgument($expected_data), true, 512, JSON_THROW_ON_ERROR) ?? [];

        // Retrieve Iadvize variable value in JS (catch exception to return a clean message)
        try {
            $idz_custom_data = $this->getSession()
                ->getDriver()
                ->getWebDriverSession()
                ->execute(['script' => sprintf('return %s', $variable), 'args' => []]);
        } catch (\Exception $ex) {
            throw new ElementNotFoundException(
                $this->getSession(),
                sprintf('Tracker Iadvize (through %s variable)', $variable),
            );
        }

        if (array_diff($expected, $idz_custom_data) !== [] || array_diff($idz_custom_data, $expected) !== []) {
            throw new ExpectationException(
                sprintf(
                    'Iadvize variable value is "%s", but "%s" expected.',
                    json_encode($idz_custom_data, JSON_THROW_ON_ERROR),
                    $this->fixStepArgument($expected_data),
                ),
                $this->getSession(),
            );
        }
    }

    /**
     * cookieShouldBeSetTo
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     * @Then /^The cookie "(?P<cookie>(?:[^"]|\\")*)" should be set to "(?P<data>(?:[^"]|\\")*)"$/
     */
    public function cookieShouldBeSetTo(string $cookie, string $data): void
    {
        $value = $this->fixStepArgument($data);

        try {
            $cookie_value = $this->getSession()
                ->getDriver()
                ->getWebDriverSession()
                ->execute(['script' => "return SonVideo.getCookie('" . $cookie . "')", 'args' => []]);
            if (!$cookie_value) {
                $cookie_value = 'false';
            }
        } catch (\Exception $ex) {
            throw new ElementNotFoundException($this->getSession(), sprintf('Cookie "%s"', $cookie));
        }

        if ($value !== $cookie_value) {
            throw new ExpectationException(
                sprintf('The Cookie "%s" value is "%s", but "%s" expected.', $cookie, $cookie_value, $data),
                $this->getSession(),
            );
        }
    }

    /**
     * criteoCustomDataVariableShouldBeSetTo
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     * @Then /^The Criteo custom data variable should be set to:$/
     */
    public function criteoCustomDataVariableShouldBeSetTo(PyStringNode $py_string_node): void
    {
        // Decode Iadvize data expected
        $expected = json_decode($py_string_node->getRaw(), true, 512, JSON_THROW_ON_ERROR);

        // Retrieve criteo_q value in JS
        try {
            $criteo_data = $this->getSession()
                ->getDriver()
                ->getWebDriverSession()
                ->execute(['script' => 'return criteo_q', 'args' => []]);
        } catch (\Exception $ex) {
            throw new ElementNotFoundException($this->getSession(), 'Tracker Criteo (no custom data variable)');
        }

        if ($expected != $criteo_data) {
            throw new ExpectationException(
                sprintf(
                    'Criteo custom data is "%s", but "%s" expected.',
                    json_encode($criteo_data, JSON_THROW_ON_ERROR),
                    json_encode($expected, JSON_THROW_ON_ERROR),
                ),
                $this->getSession(),
            );
        }
    }

    /**
     * googleDataLayerShouldContain
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     * @internal param string $data
     * @Then /^The google dataLayer should contain "(?P<data>(?:[^"]|\\")*)"$/
     */
    public function googleDataLayerShouldContain(string $expected_data): void
    {
        // Decode dataLayer data expected
        $expected = $this->fixStepArgument($expected_data);

        // Retrieve Google variable value in JS (catch exception to return a clean message)
        try {
            $datalayer_data = $this->getSession()
                ->getDriver()
                ->getWebDriverSession()
                ->execute(['script' => 'return dataLayer', 'args' => []]);
        } catch (\Exception $ex) {
            throw new ElementNotFoundException($this->getSession(), 'Tracker Google (through dataLayer variable)');
        }

        if (strpos(json_encode($datalayer_data, JSON_THROW_ON_ERROR), $expected) === false) {
            throw new ExpectationException(
                sprintf(
                    'Datalayer variable value is "%s", but "%s" expected.',
                    json_encode($datalayer_data, JSON_THROW_ON_ERROR),
                    $this->fixStepArgument($expected_data),
                ),
                $this->getSession(),
            );
        }
    }

    /**
     * googleDataLayerShouldNotContain
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     * @internal param string $data
     * @Then /^The google dataLayer should not contain "(?P<data>(?:[^"]|\\")*)"$/
     */
    public function googleDataLayerShouldNotContain(string $expected_data): void
    {
        // Decode dataLayer data expected
        $expected = $this->fixStepArgument($expected_data);

        // Retrieve Google variable value in JS (catch exception to return a clean message)
        try {
            $datalayer_data = $this->getSession()
                ->getDriver()
                ->getWebDriverSession()
                ->execute(['script' => 'return dataLayer', 'args' => []]);
        } catch (\Exception $ex) {
            throw new ElementNotFoundException($this->getSession(), 'Tracker Google (through dataLayer variable)');
        }

        if (strpos(json_encode($datalayer_data, JSON_THROW_ON_ERROR), $expected) !== false) {
            throw new ExpectationException(
                sprintf(
                    'The value "%s" is found in dataLayer variable but it should not.',
                    json_encode($datalayer_data, JSON_THROW_ON_ERROR),
                ),
                $this->getSession(),
            );
        }
    }

    /**
     * googleDataLayerShouldContainArray
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     * @internal param string $data
     * @Then /^The google dataLayer should contain "(?P<data>(?:[^"]|\\")*)" array$/
     */
    public function googleDataLayerShouldContainArray(string $expected_data): void
    {
        // Decode dataLayer data expected
        $expected = json_decode($this->fixStepArgument($expected_data), true, 512, JSON_THROW_ON_ERROR);

        // Retrieve Google variable value in JS (catch exception to return a clean message)
        try {
            $datalayer_data = $this->getSession()
                ->getDriver()
                ->getWebDriverSession()
                ->execute(['script' => 'return dataLayer', 'args' => []]);
        } catch (\Exception $ex) {
            throw new ElementNotFoundException($this->getSession(), 'Tracker Google (through dataLayer variable)');
        }

        $finded = false;
        foreach ($datalayer_data as $data) {
            if ($this->recursiveKSort($data) === $this->recursiveKSort($expected)) {
                $finded = true;
            }
        }

        if (!$finded) {
            throw new ExpectationException(
                sprintf(
                    'Datalayer variable value is "%s", but "%s" expected.',
                    json_encode($datalayer_data, JSON_THROW_ON_ERROR),
                    $this->fixStepArgument($expected_data),
                ),
                $this->getSession(),
            );
        }
    }

    /**
     * recursiveKSort
     *
     * @param $data
     *
     * @return array
     */
    private function recursiveKSort($data)
    {
        if (is_array($data)) {
            ksort($data);
            foreach ($data as $key => $value) {
                $data[$key] = $this->recursiveKSort($value);
            }
        }

        return $data;
    }
}
