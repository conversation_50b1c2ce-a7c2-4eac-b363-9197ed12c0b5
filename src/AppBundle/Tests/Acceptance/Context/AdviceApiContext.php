<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context;

use Behat\Behat\Context\Context;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\Mink\Exception\ExpectationException;
use Behat\Mink\Exception\ResponseTextException;
use Behat\Symfony2Extension\Context\KernelDictionary;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Mock\Manager\Advice\ReviewCreatorMock;
use Behat\MinkExtension\Context\RawMinkContext;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary\CommonDictionaryTrait;

/**
 * Class AdviceApiContext
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context
 */
class AdviceApiContext extends RawMinkContext implements Context
{
    use KernelDictionary;
    use CommonDictionaryTrait;

    /**
     * nextCheckOfAlreadySubmitWillReturn
     *
     * @Given /^The next advice api call to check if user has already submit a review will return (?P<value>.*)$/
     */
    public function nextCheckOfAlreadySubmitWillReturn(string $value): void
    {
        ReviewCreatorMock::setCachedResult('has_already_submit_a_review', $value);
    }

    /**
     * clearCache
     *
     * @Given /^clear advice api cache$/
     */
    public function clearCache(): void
    {
        ReviewCreatorMock::clearCache();
    }

    /**
     * shouldSeeNumberOfQuestionBlocks
     *
     * Example: I should see 2 question blocks
     *
     *
     * @Then /^I should see (?P<number>[^"]*) question block(?:s)?$/
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     */
    public function shouldSeeNumberOfQuestionBlocks(int $number): void
    {
        $blocks = $this->getSession()
            ->getPage()
            ->findAll('css', '#product-questions-list .product-review-item');
        if ($blocks === []) {
            throw new ElementNotFoundException($this->getSession(), 'No block question element');
        }

        if (count($blocks) !== $number) {
            throw new ExpectationException(
                sprintf('Expected %d question blocks, got %d', $number, count($blocks)),
                $this->getSession(),
            );
        }
    }

    /**
     * shouldSeeInNthQuestionBlock
     *
     * Example: I should see "Commander" in the 1st question block
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     * @Then /^I should see "(?P<label>[^"]*)" in the (?P<umpteenth>\d+)(?:st|nd|rd|th)? question block?$/
     *
     */
    public function shouldSeeInNthQuestionBlock(string $label, int $umpteenth): void
    {
        $index = $umpteenth - 1;
        $blocks = $this->getSession()
            ->getPage()
            ->findAll('css', '#product-questions-list .product-review-item');
        if ($blocks === []) {
            throw new ElementNotFoundException($this->getSession(), 'No block question element');
        }

        if (!isset($blocks[$index])) {
            throw new ElementNotFoundException(
                $this->getSession(),
                sprintf('%d block question containing %s', $umpteenth, $label),
            );
        }

        $error_message = sprintf('The text "%s" was not found anywhere in the %d block question', $label, $umpteenth);

        if (!$this->elementContainsText($blocks[$index], $label)) {
            throw new ResponseTextException($error_message, $this->getSession()->getDriver());
        }
    }

    /**
     * shouldNotSeeInNthQuestionBlock
     *
     * Example: I should not see "Commander" in the 1st question block
     *
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     * @Then /^I should not see "(?P<label>[^"]*)" in the (?P<umpteenth>\d+)(?:st|nd|rd|th)? question block?$/
     *
     */
    public function shouldNotSeeInNthQuestionBlock(string $label, int $umpteenth): void
    {
        $index = $umpteenth - 1;
        $blocks = $this->getSession()
            ->getPage()
            ->findAll('css', '#product-questions-list .product-review-item');
        if ($blocks === []) {
            throw new ElementNotFoundException($this->getSession(), 'No block question element');
        }

        if (!isset($blocks[$index])) {
            throw new ElementNotFoundException(
                $this->getSession(),
                sprintf('%d block question containing %s', $umpteenth, $label),
            );
        }

        $error_message = sprintf('The text "%s" was not found anywhere in the %d block question', $label, $umpteenth);

        if ($this->elementContainsText($blocks[$index], $label)) {
            throw new ResponseTextException($error_message, $this->getSession()->getDriver());
        }
    }

    /**
     * fillInTheSearchInput
     *
     * Example: I fill in the search input with "enceinte"
     *
     *
     * @When /^I fill in the search input with "(?P<search_text>[^"]*)"?$/
     */
    public function fillInTheSearchInput(string $search_text): void
    {
        $search_box = $this->getSession()
            ->getPage()
            ->find('css', 'input[type=search]');
        $search_box->setValue($search_text);
    }
}
