<?php
/*
 * This file is part of CMS BackOffice package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary;

use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Behat\Mink\Element\DocumentElement;
use Behat\Mink\Element\Element;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\ElementNotFoundException;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\FeatureContext;

/**
 * Class CommonDictionaryTrait
 *
 * @package SonVideo\Cms\BackOffice\AppBundle\Tests\Acceptance\Context
 * <AUTHOR> <<EMAIL>>
 */
trait CommonDictionaryTrait
{
    /** @var FeatureContext */
    protected $feature_context;

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        $environment = $scope->getEnvironment();

        $this->feature_context = $environment->getContext(FeatureContext::class);
    }

    /**
     * Returns fixed step argument (with \\" replaced back to ")
     *
     * @param string $argument
     */
    protected function fixStepArgument($argument): string
    {
        return str_replace('\\"', '"', $argument);
    }

    /**
     * scrollIntoView
     */
    protected function scrollIntoView(string $selector, int $index = 0)
    {
        $function = <<<JS
        (function(){
            const element_height = $($('{selector}').get({index})).height()
            const window_height = $(window).height()
            const element_offset_top = $($('{selector}').get({index})).offset().top
            $('html, body').animate({ scrollTop: element_offset_top - ((window_height / 2) + element_height) }, 100);
        })()
        JS;

        $this->getSession()->evaluateScript(
            strtr($function, [
                '{selector}' => $selector,
                '{index}' => $index,
            ]),
        );
    }

    /**
     * elementContainsText
     *
     * Checks that current page contains text.
     */
    protected function elementContainsText(Element $element, string $text): bool
    {
        $actual = $element->getText();
        $actual = preg_replace('/\s+/u', ' ', $actual);
        $regex = '/' . preg_quote($text, '/') . '/ui';

        return (bool) preg_match($regex, $actual);
    }

    /**
     * tryWithScroll
     *
     * Try to do an action. If action fails, scroll in the page and try to do it again.
     *
     * @throws \Exception
     */
    protected function tryWithScroll(callable $function, array $args = [])
    {
        $max_try = 10;
        $js_function = <<<JS
        (function(){
            const window_height = $(window).height()
            $('html, body').animate({ scrollTop: Math.max(0, (window_height * :try) - 100)}, 10);
        })()
        JS;

        $try = 0;
        $success = false;
        while (!$success && $try <= $max_try) {
            try {
                call_user_func_array($function, $args);
                $success = true;
            } catch (\Exception $e) {
                $this->getSession()->evaluateScript(
                    strtr($js_function, [
                        ':try' => $try,
                    ]),
                );
                if ($try === $max_try) {
                    throw $e;
                }
            }

            $try++;
        }
    }

    /**
     * findLabelInCssElement
     *
     * @param NodeElement|null $element
     *
     * @throws ElementNotFoundException
     */
    public function findLabelInCssElement(string $css_selector, string $label, NodeElement $element = null): NodeElement
    {
        if (!$element instanceof NodeElement) {
            $element = $this->getPage();
        }

        $results = $element->findAll('css', $css_selector);
        foreach ($results as $result) {
            $field_label = trim(str_replace('*', '', $result->getText()));

            if ($field_label === $label) {
                return $result;
            }

            similar_text($field_label, $label, $percent);
            if ($percent > 92) {
                return $result;
            }
        }

        throw new ElementNotFoundException(
            $this->feature_context->getSession(),
            sprintf('Element with label "%s"', $label),
        );
    }

    /**
     * getPage
     */
    protected function getPage(): DocumentElement
    {
        return $this->feature_context->getSession()->getPage();
    }

    /**
     * findParentByTagName
     *
     * Returns the closest parent element having a specific tag name.
     *
     *
     * @return Element|null
     */
    protected function findParentByTagName(NodeElement $el, string $tag_name)
    {
        $container = $el->getParent();
        while ($container && $container->getTagName() != 'body') {
            if ($container->getTagName() === $tag_name) {
                return $container;
            }

            $container = $container->getParent();
        }

        return null;
    }
}
