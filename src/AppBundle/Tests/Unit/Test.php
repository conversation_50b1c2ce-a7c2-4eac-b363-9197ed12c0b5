<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit;

use Symfony\Component\HttpKernel\HttpKernelInterface;
use atoum\atoum\adapter;
use atoum\atoum\annotations\extractor;
use atoum\atoum\asserter\generator;
use atoum\atoum\test\assertion\manager;
use atoum\atoum;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\ImpersonationController;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AuthenticationToken;
use SonVideo\Cms\FrontOffice\AppBundle\Security\SilentAuthenticationToken;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Mock\PaymentClientMock;
use SonVideo\Synapps\Client\RpcClientService;
use PommProject\Foundation\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Account;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\Finder\Finder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpKernel\Kernel;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;

/**
 * Class Test
 *
 * Base abstract class to launch test in the CMS Front-Office Application
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit
 */
abstract class Test extends atoum\test
{
    /** @var $string */
    protected $class;

    /** @var HttpKernelInterface */
    protected $kernel;

    /** @var boolean */
    protected $kernelReset = true;

    private bool $booted = false;

    /**
     * {@inheritdoc}
     */
    public function __construct(
        adapter $adapter = null,
        extractor $annotationExtractor = null,
        generator $asserterGenerator = null,
        manager $assertionManager = null,
        \Closure $reflectionClassFactory = null
    ) {
        parent::__construct(
            $adapter,
            $annotationExtractor,
            $asserterGenerator,
            $assertionManager,
            $reflectionClassFactory,
        );
        $this->setTestNamespace('Tests\Unit');
    }

    /**
     * setUp
     *
     * @see Atoum
     */
    public function setUp(): void
    {
        PaymentClientMock::clearCache();
    }

    /**
     * tearDown
     *
     * @see Atoum
     */
    public function tearDown(): void
    {
        PaymentClientMock::clearCache();
    }

    /**
     * Creates a Kernel.
     *
     * Available options:
     *
     *  * environment
     *  * debug
     *
     * @param array $options An array of options
     *
     * @return Kernel A HttpKernelInterface instance
     */
    protected function createKernel(array $options = []): Kernel
    {
        if (null === $this->class) {
            $this->class = $this->getKernelClass();
        }

        return new $this->class($options['environment'] ?? 'test', $options['debug'] ?? true);
    }

    /**
     * Attempts to guess the kernel location.
     *
     * When the Kernel is located, the file is required.
     *
     * @throws \RuntimeException
     *
     * @return string The Kernel class name
     */
    protected function getKernelClass(): string
    {
        $dir = $this->getKernelDirectory();
        $finder = new Finder();
        $finder
            ->name('*Kernel.php')
            ->depth(0)
            ->in($dir);
        $results = iterator_to_array($finder);
        if ($results === []) {
            throw new \RuntimeException(
                sprintf(
                    'Impossible to find a Kernel file, override the %1$s::getKernelDirectory() method or %1$s::createKernel() method.',
                    get_class($this),
                ),
            );
        }

        $file = current($results);
        $class = $file->getBasename('.php');

        require_once $file;

        return $class;
    }

    /**
     * Override this method if needed
     */
    protected function getKernelDirectory(): string
    {
        $dir = getcwd() . '/app';
        if (!is_dir($dir)) {
            $dir = dirname($dir);
        }

        return $dir;
    }

    /**
     * return Kernel
     */
    public function getKernel(): Kernel
    {
        if (!$this->kernel instanceof HttpKernelInterface) {
            $this->kernel = $this->createKernel();
        }

        return $this->kernel;
    }

    /**
     * Enable or disable kernel reseting on client creation.
     *
     *
     */
    public function enableKernelReset(bool $kernelReset): self
    {
        $this->kernelReset = $kernelReset;

        return $this;
    }

    /**
     * getContainer
     */
    protected function getContainer(): Container
    {
        if (!$this->booted) {
            $this->getKernel()->boot();
            $this->booted = true;
        }

        return $this->getKernel()->getContainer();
    }

    protected function getSession(): Session
    {
        return $this->getContainer()->get('session');
    }

    /**
     * mockRpcClient
     *
     * Mock the "call" method for RPC Client. It always returns $expected_result.
     * The RPC client service in container is overwrited with this mock.
     *
     * @return $this
     */
    protected function mockRpcClient(array $expected_result)
    {
        // Don't know why scrutinizer crashes with this moc :(
        //        $rpc_client = new RpcClientService('test', $this->getContainer()->get('pomm')->getDefaultSession());
        //        $this->calling($rpc_client)->call = function($service, $method, $args) use ($expected_result) {
        //            return [
        //                'result' => $expected_result
        //            ];
        //        };

        $rpc_client = new class (
            'test',
            $this->getContainer()
                ->get('pomm')
                ->getDefaultSession(),
            [],
            $expected_result,
        ) extends RpcClientService {
            private $expected_result;

            public function __construct(
                string $server_url,
                PommSession $session,
                array $client_options,
                $expected_result
            ) {
                parent::__construct($server_url, $session, $client_options);
                $this->expected_result = $expected_result;
            }

            /**
             * @return array{result: mixed}
             */
            public function call(string $service, string $method, array $args = []): array
            {
                return ['result' => $this->expected_result];
            }
        };

        $this->getContainer()->set('sonvideo.synapps.rpc_client', $rpc_client);

        return $this;
    }

    /**
     * Simulate a user login. Service security.token_storage is now usable.
     */
    protected function mockUserLogin(int $customer_id): self
    {
        $account = new Account([
            'customer_id' => $customer_id,
            'email' => '<EMAIL>',
            'password' => 'secret',
            'roles' => ['SVD_CUSTOMER'],
        ]);
        $token = new UsernamePasswordToken($account, $account->getPassword(), 'main', $account->getRoles());
        $this->getContainer()
            ->get('security.token_storage')
            ->setToken($token);

        $event = new InteractiveLoginEvent(new Request(), $token);
        $this->getContainer()
            ->get('event_dispatcher')
            ->dispatch('security.interactive_login', $event);

        return $this;
    }

    /**
     * userLogout
     */
    protected function userLogout(): self
    {
        $this->getContainer()
            ->get('security.token_storage')
            ->setToken(null);

        return $this;
    }

    /**
     * Simulate a user login. Service security.token_storage is now usable.
     */
    protected function mockImpersonationLogin(string $token = 'a803d25a46c8ea732ecb1525a3971c0f')
    {
        // Create a token
        $silent_authentication_token = new SilentAuthenticationToken(
            $token,
            AuthenticationToken::TOKEN_PURPOSE_IMPERSONATION,
            ImpersonationController::PROVIDER_KEY,
        );

        // Authenticate the token
        $user = $this->getContainer()
            ->get('postgres_authenticator')
            ->authenticateToken(
                $silent_authentication_token,
                $this->getContainer()->get('app.svd_customer_provider'),
                ImpersonationController::PROVIDER_KEY,
            );
        $this->getContainer()
            ->get('security.token_storage')
            ->setToken($user);

        // Dispatch authentication
        $this->getContainer()
            ->get('event_dispatcher')
            ->dispatch('security.interactive_login', new InteractiveLoginEvent(new Request(), $user));
    }

    /**
     * runProtectedMethod
     *
     * Run protected/private method for object and return the result.
     *
     * @param         $object
     *
     * @return mixed
     * @throws \ReflectionException
     */
    protected function runProtectedMethod($object, string $method_name, array $args)
    {
        $class = new \ReflectionClass(get_class($object));
        $method = $class->getMethod($method_name);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $args);
    }
}
