<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\Query\Filter;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\MultipleValuesContextFilter;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter\ColorFilter as TestedInstance;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class ColorFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\Query\Filter
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class ColorFilter extends PommTest
{
    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedInstance
    {
        return new TestedInstance($this->getPommSession());
    }

    /**
     * getFilter
     */
    protected static function getFilter(array $values, array $not_values): MultipleValuesContextFilter
    {
        return (new MultipleValuesContextFilter())->addValues($values)->addNotValues($not_values);
    }

    /**
     * testMakeQuery
     */
    public function testMakeQuery(): void
    {
        $this->assert(sprintf('Test %s query returns a string sql.', get_class($this)))
            ->given($where = new Where('true'))
            ->and($sql = $this->getTestedInstance()->makeQuery($where, static::getFilter(['blanc'], [])))
            ->string($sql)

            ->assert('Test the query with values.')
            ->given(
                $iterator = $this->getPommSession()
                    ->getQueryManager()
                    ->query($sql, $where->getValues()),
            )
            ->array(array_column($iterator->extract(), 'article_id'))
            ->isEqualTo([92948, 104130, 111297, 111523, 82533, 117734, 777777])
            ->assert('Test the query with excluded values.')
            ->given($where = new Where('true'))
            ->and($sql = $this->getTestedInstance()->makeQuery($where, static::getFilter([], ['blanc'])))
            ->and(
                $iterator = $this->getPommSession()
                    ->getQueryManager()
                    ->query($sql, $where->getValues()),
            )
            ->array(array_column($iterator->extract(), 'article_id'))
            ->notContainsValues([92948, 104130, 111297, 111523]);
    }
}
