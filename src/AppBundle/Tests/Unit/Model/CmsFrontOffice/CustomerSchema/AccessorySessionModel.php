<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccessorySession;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartSummary;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccessorySessionModel as TestedClass;

/**
 * Class AccessorySessionModel
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSchema
 * <AUTHOR> <<EMAIL>>
 */
class AccessorySessionModel extends PommTest
{
    /**
     * getTestedModel
     */
    public function getTestedModel(): TestedClass
    {
        return $this->getPommSession()->getModel(TestedClass::class);
    }

    /**
     * testListAccessories
     */
    public function testListAccessories(): void
    {
        $accessories = [58336 => ['quantity' => 2], 42074 => ['quantity' => 5]];

        $this->assert('List accessories.')
            ->given($accessories = $this->getTestedModel()->listAccessories($accessories, 'fr'))
            ->object($accessories)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($accessories->count())
            ->isEqualTo(2)
            ->object(
                $accessory =
                    $accessories->get(0)['accessory_id'] === 58336 ? $accessories->get(0) : $accessories->get(1),
            )
            ->isInstanceOf(AccessorySession::class)
            ->integer($accessory->get('quantity'))
            ->isEqualTo(2)
            ->integer($accessory->get('accessory_id'))
            ->isEqualTo(58336)
            ->string($accessory->get('accessory_name'))
            ->isEqualTo('Embouts noir, 4x s / 4x m / 4xl')
            ->string($accessory->get('accessory_sku'))
            ->isEqualTo('SENN-517682')
            ->string($accessory->get('short_description'))
            ->isEqualTo('Sennheiser Embouts noir, 4x s / 4x m / 4xl')
            ->string($accessory->get('catalog'))
            ->isEqualTo('sennheiser')
            ->string(number_format($accessory->get('selling_price'), 2))
            ->isEqualTo('12.48')
            ->string(number_format($accessory->get('selling_price_vat_excluded'), 2))
            ->isEqualTo('10.40');
    }

    /**
     * testSummary
     */
    public function testSummary(): void
    {
        $accessories = [58336 => ['quantity' => 2], 42074 => ['quantity' => 5]];

        $this->assert('Summary of accessories returns a summary with good values.')
            ->given($summary = $this->getTestedModel()->summary($accessories))
            ->object($summary)
            ->isinstanceOf(ShoppingCartSummary::class)
            ->integer($summary->getNbArticles())
            ->isEqualTo(7)
            ->string(number_format($summary->getAmountSellingPrice(), 2))
            ->isEqualTo('3,438.66')
            ->string(number_format($summary->getAmountEcotaxe(), 2))
            ->isEqualTo('0.75')
            ->array($summary->getAccessoryIds(), 2)
            ->isEqualTo([58336, 42074])
            ->array($summary->getArticleIds(), 2)
            ->isEmpty->string(number_format($summary->getAmountSellingPriceForPromoCode(), 2))
            ->isEqualTo('0.00');
    }
}
