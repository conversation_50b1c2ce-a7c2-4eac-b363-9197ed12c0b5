<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSchema;

use PommProject\ModelManager\Model\Model;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Session\ShoppingCartSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleWarrantyExtension;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Basket;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketModelLayer as TestedModelLayer;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketLineModel as TestedBasketLineModel;

/**
 * Class BasketModelLayer
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSchema
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 * @see       PommTest
 */
class BasketModelLayer extends PommTest
{
    /**
     * getModelLayer
     */
    protected function getModelLayer(): TestedModelLayer
    {
        return $this->getPommSession()->getModelLayer(TestedModelLayer::class);
    }

    /**
     * getModel
     */
    protected function getModel(string $model_class): Model
    {
        return $this->getPommSession()->getModel($model_class);
    }

    /**
     * testSaveShoppingCartArticles
     */
    public function testSaveShoppingCart(): void
    {
        $shopping_cart_session = new ShoppingCartSession([94315 => 1, 123456 => 1, 123457 => 2], 869);
        $shopping_cart_session
            ->getBasketableCollection('articles')
            ->getBasketable('123456')
            ->addWarranty(new ArticleWarrantyExtension(['tag_path' => 'warranty.vol_casse.1']));
        $shopping_cart_session->setCustomerPromoCode('PAPAYOU', 970481);

        $this->assert('Convert a session shopping cart not empty')
            ->given($basket = $this->getModelLayer()->saveShoppingCart(970481, $shopping_cart_session))
            ->object($basket)
            ->isInstanceOf(Basket::class)

            ->given($data = $basket->extract())
            ->integer($data['customer_id'])
            ->isEqualTo(970481)
            ->integer($data['promo_offer_id'])
            ->isEqualTo(869)
            ->string($data['customer_promo_code_code'])
            ->isEqualTo('PAPAYOU')
            ->integer($data['customer_promo_code_customer_id'])
            ->isEqualTo(970481)

            ->assert('Check that articles in new basket are the same as in session shopping cart')
            ->given($res = $this->listBasketArticleIds(970481, 'fr'))
            ->array($res)
            ->hasKeys([123456, 123457, 94315])
            ->integer($res[123456]['quantity'])
            ->isEqualTo(1)
            ->integer($res[123457]['quantity'])
            ->isEqualTo(2)
            ->integer($res[94315]['quantity'])
            ->isEqualTo(1)
            ->array($res[94315]['extra_data']['warranties'])
            ->hasSize(0)
            ->array($res[123457]['extra_data']['warranties'])
            ->hasSize(0)
            ->array($res[123456]['extra_data']['warranties'])
            ->hasSize(1)
            ->string(array_column($res[123456]['extra_data']['warranties'], 'tag_path')[0])
            ->isEqualTo('warranty.vol_casse.1')

            ->assert('Check exception on an empty session shopping cart')
            ->given($tested_instance = $this->getModelLayer())
            ->then->exception(function () use ($tested_instance): void {
                $tested_instance->saveShoppingCart(970481, new ShoppingCartSession([]));
            })
            ->hasMessage('The shopping cart is empty.');
    }

    /**
     * listBasketArticles
     *
     */
    private function listBasketArticleIds(int $customer_id, string $culture): array
    {
        $article_ids = [];

        $articles = $this->getPommSession()
            ->getModel(TestedBasketLineModel::class)
            ->listArticles($customer_id, $culture);

        foreach ($articles as $article) {
            $article_ids[$article->get('content')['id']] = [
                'quantity' => $article->get('quantity'),
                'extra_data' => $article->get('extra_data'),
            ];
        }

        return $article_ids;
    }
}
