<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\GuideSchema;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\GuideFamily;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\GuideFamilyModel as TestedModel;

/**
 * Class GuideFamilyModel
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\GuideSchema
 * <AUTHOR> <<EMAIL>>
 */
class GuideFamilyModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testFindGuideFamily
     */
    public function testFindGuideFamily(): void
    {
        $this->assert('Test with a guide having a main family')
            ->given($guide_family = $this->getModel()->findGuideFamily(1))
            ->object($guide_family)
            ->isInstanceOf(GuideFamily::class)
            ->integer($guide_family->get('family_id'))
            ->isEqualTo(1)
            ->string($guide_family->get('slug'))
            ->isEqualTo('home-cinema')

            ->assert('Test with a guide without a main family')
            ->given($guide_family = $this->getModel()->findGuideFamily(9))
            ->object($guide_family)
            ->isInstanceOf(GuideFamily::class)
            ->integer($guide_family->get('family_id'))
            ->isEqualTo(4)
            ->string($guide_family->get('slug'))
            ->isEqualTo('vinyle')

            ->assert('Test with a non existing guide')
            ->given($guide_family = $this->getModel()->findGuideFamily(99))
            ->variable($guide_family)
            ->isNull();
    }
}
