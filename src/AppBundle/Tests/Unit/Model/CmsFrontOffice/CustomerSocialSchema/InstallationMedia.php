<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSocialSchema;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\InstallationMedia as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;

/**
 * Class InstallationMedia
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSocialSchema
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class InstallationMedia extends Test
{
    /**
     * getValidMedia
     *
     * @return array{control: array<int, array{image_url: string, thumbnail_url: string}>, main: array<int, array{image_url: string, thumbnail_url: string}>, secondary: array{image_url: string, thumbnail_url: string}[]}
     */
    protected function getValidMedia(): array
    {
        return [
            'control' => [
                [
                    'image_url' => '/images/installations/4552/s1398508_0_15b6466f856a8f.jpeg',
                    'thumbnail_url' => 'images/installations/4552/s1398508_0_15b6466f856a8f.jpeg',
                ],
            ],
            'main' => [
                [
                    'image_url' => '/images/installations/4552/s1398508_0_15b6323a9a38ca.jpeg',
                    'thumbnail_url' => 'images/installations/4552/s1398508_0_15b6323a9a38ca.jpeg',
                ],
            ],
            'secondary' => [
                [
                    'image_url' => '/images/installations/4552/s1398508_0_15b6468a8f2dd9.jpeg',
                    'thumbnail_url' => 'images/installations/4552/s1398508_0_15b6468a8f2dd9.jpeg',
                ],
                [
                    'image_url' => '/images/installations/4552/s1398508_0_15b646864d372f.jpeg',
                    'thumbnail_url' => 'images/installations/4552/s1398508_0_15b646864d372f.jpeg',
                ],
            ],
        ];
    }

    /**
     * testConstruct
     */
    public function testConstruct(): void
    {
        $this->assert('Test constructor')
            ->given($instance = new TestedClass($this->getValidMedia()))
            ->array($instance->normalize())
            ->isEqualTo($this->getValidMedia());
    }

    /**
     * testAddMedia
     */
    public function testAddMedia(): void
    {
        $this->assert('Test add media in a complete media list')
            ->given($instance = new TestedClass($this->getValidMedia()))
            ->and($instance->addMedia('url1', 'url2'))
            ->array($array = $instance->normalize())
            ->string($array['secondary'][2]['image_url'])
            ->isEqualTo('url1')
            ->string($array['secondary'][2]['thumbnail_url'])
            ->isEqualTo('url2')

            ->assert('Test add media')
            ->given(
                $instance = new TestedClass([
                    'control' => [
                        [
                            'image_url' => '/images/installations/4552/s1398508_0_15b6466f856a8f.jpeg',
                            'thumbnail_url' => 'images/installations/4552/s1398508_0_15b6466f856a8f.jpeg',
                        ],
                    ],
                ]),
            )
            ->and($instance->addMedia('url1', 'url2'))
            ->and($instance->addMedia('url3', 'url4'))
            ->and($instance->addMedia('url5', 'url6'))
            ->array($array = $instance->normalize())
            ->string($array['main'][0]['image_url'])
            ->isEqualTo('url1')
            ->string($array['main'][0]['thumbnail_url'])
            ->isEqualTo('url2')
            ->string($array['secondary'][0]['image_url'])
            ->isEqualTo('url3')
            ->string($array['secondary'][0]['thumbnail_url'])
            ->isEqualTo('url4')
            ->string($array['secondary'][1]['image_url'])
            ->isEqualTo('url5')
            ->string($array['secondary'][1]['thumbnail_url'])
            ->isEqualTo('url6');
    }

    /**
     * testRemoveMediaByImageUrl
     */
    public function testRemoveMediaByImageUrl(): void
    {
        $this->assert('Remove media by url.')
            ->given($instance = new TestedClass($this->getValidMedia()))
            ->and($instance->removeMediaByImageUrl(['/images/installations/4552/s1398508_0_15b6323a9a38ca.jpeg']))
            ->array($array = $instance->normalize())
            ->integer(is_countable($array['main']) ? count($array['main']) : 0)
            ->isEqualTo(1)
            ->string($array['main'][0]['image_url'])
            ->isEqualTo('/images/installations/4552/s1398508_0_15b6468a8f2dd9.jpeg')
            ->integer(is_countable($array['secondary']) ? count($array['secondary']) : 0)
            ->isEqualTo(1)
            ->string($array['secondary'][0]['image_url'])
            ->isEqualTo('/images/installations/4552/s1398508_0_15b646864d372f.jpeg');
    }
}
