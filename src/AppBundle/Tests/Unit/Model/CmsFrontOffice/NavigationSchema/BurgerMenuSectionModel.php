<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\NavigationSchema;

use PommProject\ModelManager\Model\Model;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\NavigationSchema\BurgerMenuSectionModel as TestedClass;

class BurgerMenuSectionModel extends PommTest
{
    private ?Model $model = null;

    /**
     * testGetNodes
     */
    public function testRemoveEmptySections(): void
    {
        $this->model = $this->getPommSession()->getModel(TestedClass::class);

        $this->assertRemovalOfSections('It keeps what should be kept', []);
        $this->assertRemovalOfSections('It removes when has empty entries and groups', [
            ['burger_menu_section_id' => 'n1000000', 'display_order' => 4, 'entries' => [], 'groups' => []],
        ]);
        $this->assertRemovalOfSections('It removes when has inactive entry or (null url and no section)', [
            [
                'burger_menu_section_id' => 'n1000000',
                'display_order' => 4,
                'entries' => [
                    [
                        'entry_id' => 'n100000a',
                        'display_order' => 1,
                        'is_active' => false,
                        'url' => '/some-url',
                        'icon' => null,
                        'color' => null,
                        'label' => 'An inactive leaf',
                        'sections' => [],
                    ],
                ],
                'groups' => [],
            ],
            [
                'burger_menu_section_id' => 'n2000000',
                'display_order' => 5,
                'entries' => [
                    [
                        'entry_id' => 'n100000a',
                        'display_order' => 1,
                        'is_active' => true,
                        'url' => null,
                        'icon' => null,
                        'color' => null,
                        'label' => 'An active leaf without url provided nor sublevel',
                        'sections' => [],
                    ],
                    [
                        'entry_id' => 'n100000b',
                        'display_order' => 2,
                        'is_active' => true,
                        'url' => '',
                        'icon' => null,
                        'color' => null,
                        'label' => 'An active leaf with empty url provided nor sublevel',
                        'sections' => [],
                    ],
                ],
                'groups' => [],
            ],
        ]);
        $this->assertRemovalOfSections('It removes when sub-level contains only inactive leaf', [
            [
                'burger_menu_section_id' => 'n1000000',
                'display_order' => 4,
                'entries' => [
                    [
                        'entry_id' => 'n100000a',
                        'display_order' => 1,
                        'is_active' => true,
                        'url' => null,
                        'icon' => null,
                        'color' => null,
                        'label' => 'An active sublevel',
                        'sections' => [
                            [
                                'burger_menu_section_id' => 'n1100000',
                                'display_order' => 1,
                                'entries' => [
                                    [
                                        'entry_id' => 'n110000a',
                                        'display_order' => 1,
                                        'is_active' => false,
                                        'url' => null,
                                        'icon' => null,
                                        'color' => null,
                                        'label' => 'An inactive leaf',
                                        'sections' => [],
                                    ],
                                ],
                                'groups' => [],
                            ],
                        ],
                    ],
                ],
                'groups' => [],
            ],
        ]);
        $this->assertRemovalOfSections('It removes when group contains only inactive entry', [
            [
                'burger_menu_section_id' => 'n1000000',
                'display_order' => 4,
                'entries' => [],
                'groups' => [
                    [
                        'group_id' => 'n1a00000',
                        'title' => 'A group with an inactive entry',
                        'display_order' => 1,
                        'entries' => [
                            [
                                'entry_id' => '3a00000a',
                                'display_order' => 1,
                                'is_active' => false,
                                'url' => '/some-url-',
                                'icon' => null,
                                'color' => null,
                                'label' => 'Entry 2',
                                'sections' => [],
                            ],
                        ],
                    ],
                ],
            ],
            [
                'burger_menu_section_id' => 'n2000000',
                'display_order' => 5,
                'entries' => [],
                'groups' => [
                    [
                        'group_id' => 'n2a00000',
                        'title' => 'A group with an active entry',
                        'display_order' => 1,
                        'entries' => [
                            [
                                'entry_id' => 'n2a0000a',
                                'display_order' => 1,
                                'is_active' => true,
                                'url' => null,
                                'icon' => null,
                                'color' => null,
                                'label' => 'An active sublevel',
                                'sections' => [
                                    [
                                        'burger_menu_section_id' => 'n2a10000',
                                        'display_order' => 1,
                                        'entries' => [
                                            [
                                                'entry_id' => 'n2a1000a',
                                                'display_order' => 1,
                                                'is_active' => false,
                                                'url' => null,
                                                'icon' => null,
                                                'color' => null,
                                                'label' => 'An inactive leaf',
                                                'sections' => [],
                                            ],
                                        ],
                                        'groups' => [],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    private function assertRemovalOfSections(string $assertion, array $sections_that_should_be_removed): void
    {
        $sections_that_should_always_be_kept = [
            // Has an active leaf with provided url
            [
                'burger_menu_section_id' => '10000000', // not relevant, as a "meaning" only to ease debug
                'display_order' => 1,
                'entries' => [
                    [
                        'entry_id' => '1000000a',
                        'display_order' => 1,
                        'is_active' => true,
                        'url' => '/some-active-entry-url',
                        'icon' => '/an-icon.png',
                        'color' => null,
                        'label' => 'An active leaf',
                        'sections' => [],
                    ],
                ],
                'groups' => [],
            ],
            // Has an active sublevel
            [
                'burger_menu_section_id' => '20000000',
                'display_order' => 2,
                'entries' => [
                    [
                        'entry_id' => '2000000a',
                        'display_order' => 1,
                        'is_active' => true,
                        'url' => null,
                        'icon' => null,
                        'color' => null,
                        'label' => 'An active entry with active sublevel',
                        'sections' => [
                            [
                                'burger_menu_section_id' => '21000000',
                                'display_order' => 1,
                                'entries' => [
                                    [
                                        'entry_id' => '2100000a',
                                        'display_order' => 1,
                                        'is_active' => true,
                                        'url' => '/some-url',
                                        'icon' => null,
                                        'color' => null,
                                        'label' => 'An active leaf',
                                        'sections' => [],
                                    ],
                                ],
                                'groups' => [],
                            ],
                        ],
                    ],
                ],
                'groups' => [],
            ],
            // Has an active group
            [
                'burger_menu_section_id' => '30000000',
                'display_order' => 3,
                'entries' => [],
                'groups' => [
                    [
                        'group_id' => '3a000000',
                        'title' => 'A group with an active entry',
                        'display_order' => 1,
                        'entries' => [
                            [
                                'entry_id' => '3a00000a',
                                'display_order' => 1,
                                'is_active' => true,
                                'url' => '/some-url-',
                                'icon' => null,
                                'color' => null,
                                'label' => 'Entry 2',
                                'sections' => [],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->assert($assertion)
            ->given($sections = array_merge($sections_that_should_always_be_kept, $sections_that_should_be_removed))
            ->and($sections = $this->model->removeEmptySections($sections))
            ->then()
            ->array(array_column($sections, 'burger_menu_section_id'))
            ->isEqualTo(['10000000', '20000000', '30000000']);
    }
}
