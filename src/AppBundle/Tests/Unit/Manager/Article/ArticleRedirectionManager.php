<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Article;

use Symfony\Component\HttpFoundation\RedirectResponse;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\ArticleRedirectionManager as TestedClass;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Class ArticleRedirectionManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Article
 */
class ArticleRedirectionManager extends PommTest
{
    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get('app.manager.article.article_redirection_manager');
    }

    /**
     * testRedirectArticle
     */
    public function testRedirectArticle(): void
    {
        $this->assert('Test brand/category partial article url')
            ->given($res = $this->getTestedInstance()->redirectArticle('/article/enceinte-colonne/triangle'))
            ->string($res->getTargetUrl())
            ->endWith('/marque/enceinte-colonne/triangle')
            ->assert('Test category partial article url')
            ->given($res = $this->getTestedInstance()->redirectArticle('/article/enceinte-colonne'))
            ->string($res->getTargetUrl())
            ->endWith('/categorie/enceinte-colonne')
            ->assert('Test old url redirection')
            ->given(
                $res = $this->getTestedInstance()->redirectArticle('/article/old-url-elipson-prestige-4i-calvados-fr'),
            )
            ->string($res->getTargetUrl())
            ->endWith('/article/elipson-prestige-4i-calvados-fr')

            ->assert('Test wrong partial url')
            ->exception(fn(): RedirectResponse => $this->getTestedInstance()->redirectArticle('/article/enceintes-colonne/triangle'))
            ->isInstanceOf(NotFoundHttpException::class)
            ->assert('Test an non existing old url')
            ->exception(fn(): RedirectResponse => $this->getTestedInstance()->redirectArticle(
                '/article/very-old-url-elipson-prestige-4i-calvados-fr',
            ))
            ->isInstanceOf(NotFoundHttpException::class);
    }
}
