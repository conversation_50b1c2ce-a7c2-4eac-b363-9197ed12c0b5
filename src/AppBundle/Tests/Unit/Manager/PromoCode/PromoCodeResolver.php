<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\PromoCode;

use SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Type\Advantage;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\PromoCodeException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoCodeInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\PromoCodeFetcherTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\PromoCode\PromoCodeResolver as TestedClass;

/**
 * Class PromoCodeManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\PromoCode
 * <AUTHOR> <<EMAIL>>
 */
class PromoCodeResolver extends PommTest
{
    use PromoCodeFetcherTrait;

    /**
     * getTestedInstance
     */
    public function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get('app.manager.promo_code_resolver');
    }

    /**
     * testGetAdvantage
     *
     * @param mixed              $expected_result
     * @dataProvider getAdvantageProvider
     */
    public function testGetAdvantage(PromoCodeInterface $promo_code, array $cart_summary, $expected_result): void
    {
        if ($expected_result instanceof PromoCodeException) {
            $this->assert('Returns an exception with invalid code.')
                ->given($tested_instance = $this->getTestedInstance())
                ->exception(function () use ($tested_instance, $promo_code, $cart_summary): void {
                    $tested_instance->getAdvantage($promo_code, $cart_summary);
                })
                ->isInstanceOf(PromoCodeException::class)
                ->hasMessage($expected_result->getMessage());

            return;
        }

        $this->assert('Returns a valid advantage with valid code.')
            ->given($res = $this->getTestedInstance()->getAdvantage($promo_code, $cart_summary))
            ->object($res)
            ->isInstanceOf(Advantage::class)
            ->string($res->getType())
            ->isEqualTo($expected_result->getType())
            ->string($res->getName())
            ->isEqualTo($expected_result->getName())
            ->float($res->getPrice())
            ->isEqualTo($expected_result->getPrice())
            ->variable($res->gift_sku)
            ->isEqualTo($expected_result->gift_sku);
    }

    /**
     * getAdvantageProvider
     */
    protected function getAdvantageProvider(): array
    {
        return array_merge($this->getAdvantageProviderError(), $this->getAdvantageProviderValid());
    }

    /**
     * getAdvantageProviderError
     *
     * Returns a list of tests that must throw an exception.
     */
    protected function getAdvantageProviderError(): array
    {
        return [
            [$this->getPromoOfferObject(879), [], new PromoCodeException(PromoCodeException::PROMO_CODE_EMPTY_CART)],
            [
                $this->getPromoOfferObject(879),
                $this->getSummaryCart500Euros(),
                new PromoCodeException(PromoCodeException::PROMO_CODE_OUTDATED),
            ],
            [
                $this->getPromoOfferObject(908),
                $this->getSummaryCart100Euros(),
                new PromoCodeException(PromoCodeException::PROMO_CODE_THRESHOLD_NOT_REACHED),
            ],
            [
                $this->getPromoOfferObject(916),
                $this->getSummaryCart500EurosWithArticle101262(),
                new PromoCodeException(PromoCodeException::PROMO_CODE_NEED_ARTICLE),
            ],
        ];
    }

    /**
     * getAdvantageProviderValid
     *
     * Returns a list of tests that must return a valid advantage.
     */
    protected function getAdvantageProviderValid(): array
    {
        $advantage_for_kitsecu = new Advantage(1, 'CODE', 'gift', 'Kit de sécurité', 14.9);
        $advantage_for_kitsecu->gift_sku = 'ATECAKITSECU134';

        $advantage1_for_stream17 = new Advantage(1, 'CODE', 'gift', '3 mois d\'abonnement', 59.97);
        $advantage1_for_stream17->gift_sku = 'QOBUZ3MOIS';

        $advantage2_for_stream17 = new Advantage(1, 'CODE', 'gift', '6 mois d\'abonnement', 119.94);
        $advantage2_for_stream17->gift_sku = 'QOBUZ6MOIS';

        $advantage3_for_stream17 = new Advantage(1, 'CODE', 'gift', '1 an d\'abonnement', 219);
        $advantage3_for_stream17->gift_sku = 'QOBUZ1AN';

        return [
            [
                $this->getPromoOfferObject(869),
                $this->getSummaryCart100Euros(),
                new Advantage(1, 'CODE', 'variable_discount', '5 %', (float) -5),
            ],
            [$this->getPromoOfferObject(777), $this->getSummaryCart500Euros(), $advantage_for_kitsecu],
            [
                $this->getPromoOfferObject(916),
                $this->getSummaryCart500Euros(),
                new Advantage(1, 'CODE', 'fixed_discount', '100', (float) -100),
            ],
            [$this->getPromoOfferObject(908), $this->getSummaryCart500Euros(), $advantage1_for_stream17],
            [$this->getPromoOfferObject(908), $this->getSummaryCart1000Euros(), $advantage2_for_stream17],
            [$this->getPromoOfferObject(908), $this->getSummaryCart2000Euros(), $advantage3_for_stream17],
        ];
    }

    /**
     * getSummaryCart500Euros
     * @return array{nb_articles: int, article_ids: int[], article_ids_without_destock: int[], amount_selling_price: int, amount_selling_price_for_promo_code: int, amount_ecotaxe: int, customer_id: null, promo_code_id: int, has_quote: false, details: array<int, array{article_id: int, full_selling_price: int, full_selling_price_for_promo_code: int}>}
     */
    protected function getSummaryCart500Euros(): array
    {
        return [
            'nb_articles' => 5,
            'article_ids' => [123458],
            'article_ids_without_destock' => [123458],
            'amount_selling_price' => 50000,
            'amount_selling_price_for_promo_code' => 500,
            'amount_ecotaxe' => 4,
            'customer_id' => null,
            'promo_code_id' => 10,
            'has_quote' => false,
            'details' => [
                [
                    'article_id' => 123458,
                    'full_selling_price' => 5000,
                    'full_selling_price_for_promo_code' => 500,
                ],
            ],
        ];
    }

    /**
     * getSummaryCart100Euros
     * @return array{nb_articles: int, article_ids: int[], article_ids_without_destock: int[], amount_selling_price: int, amount_selling_price_for_promo_code: int, amount_ecotaxe: int, customer_id: null, promo_code_id: int, has_quote: false, details: array<int, array{article_id: int, full_selling_price: int, full_selling_price_for_promo_code: int}>}
     */
    protected function getSummaryCart100Euros(): array
    {
        return [
            'nb_articles' => 1,
            'article_ids' => [123458],
            'article_ids_without_destock' => [123458],
            'amount_selling_price' => 100,
            'amount_selling_price_for_promo_code' => 100,
            'amount_ecotaxe' => 4,
            'customer_id' => null,
            'promo_code_id' => 10,
            'has_quote' => false,
            'details' => [
                [
                    'article_id' => 123458,
                    'full_selling_price' => 100,
                    'full_selling_price_for_promo_code' => 100,
                ],
            ],
        ];
    }

    /**
     * getSummaryCart500EurosWithArticle101262
     * @return array{nb_articles: int, article_ids: int[], article_ids_without_destock: int[], amount_selling_price: int, amount_selling_price_for_promo_code: int, amount_ecotaxe: int, customer_id: null, promo_code_id: int, has_quote: false, details: array<int, array{article_id: int, full_selling_price: int, full_selling_price_for_promo_code: int}>}
     */
    protected function getSummaryCart500EurosWithArticle101262(): array
    {
        return [
            'nb_articles' => 5,
            'article_ids' => [101262],
            'article_ids_without_destock' => [101262],
            'amount_selling_price' => 500,
            'amount_selling_price_for_promo_code' => 500,
            'amount_ecotaxe' => 4,
            'customer_id' => null,
            'promo_code_id' => 10,
            'has_quote' => false,
            'details' => [
                [
                    'article_id' => 101262,
                    'full_selling_price' => 500,
                    'full_selling_price_for_promo_code' => 500,
                ],
            ],
        ];
    }

    /**
     * getSummaryCart1000Euros
     * @return array{nb_articles: int, article_ids: int[], article_ids_without_destock: int[], amount_selling_price: int, amount_selling_price_for_promo_code: int, amount_ecotaxe: int, customer_id: null, promo_code_id: int, has_quote: false, details: array<int, array{article_id: int, full_selling_price: int, full_selling_price_for_promo_code: int}>}
     */
    protected function getSummaryCart1000Euros(): array
    {
        return [
            'nb_articles' => 10,
            'article_ids' => [101262],
            'article_ids_without_destock' => [101262],
            'amount_selling_price' => 5000,
            'amount_selling_price_for_promo_code' => 1000,
            'amount_ecotaxe' => 4,
            'customer_id' => null,
            'promo_code_id' => 10,
            'has_quote' => false,
            'details' => [
                [
                    'article_id' => 101262,
                    'full_selling_price' => 5000,
                    'full_selling_price_for_promo_code' => 1000,
                ],
            ],
        ];
    }
    /**
     * getSummaryCart2000Euros
     * @return array{nb_articles: int, article_ids: int[], article_ids_without_destock: int[], amount_selling_price: int, amount_selling_price_for_promo_code: int, amount_ecotaxe: int, customer_id: null, promo_code_id: int, has_quote: false, details: array<int, array{article_id: int, full_selling_price: int, full_selling_price_for_promo_code: int}>}
     */
    protected function getSummaryCart2000Euros(): array
    {
        return [
            'nb_articles' => 20,
            'article_ids' => [101262],
            'article_ids_without_destock' => [101262],
            'amount_selling_price' => 2000,
            'amount_selling_price_for_promo_code' => 2000,
            'amount_ecotaxe' => 4,
            'customer_id' => null,
            'promo_code_id' => 10,
            'has_quote' => false,
            'details' => [
                [
                    'article_id' => 101262,
                    'full_selling_price' => 2000,
                    'full_selling_price_for_promo_code' => 2000,
                ],
            ],
        ];
    }
}
