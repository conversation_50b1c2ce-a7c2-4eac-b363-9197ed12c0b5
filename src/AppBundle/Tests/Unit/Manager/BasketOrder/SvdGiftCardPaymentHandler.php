<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\BasketOrder;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\SvdGiftCardPaymentHandler as TestedClass;
use SonVideo\Synapps\Client\RpcClientService;

/**
 * Class PaymentHandler
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\BasketOrder
 * @engine inline
 */
class SvdGiftCardPaymentHandler extends PommTest
{
    /**
     * getTestedInstance
     *
     *
     */
    private function getTestedInstance(array $rpc_response): TestedClass
    {
        $this->__get('mockGenerator')->orphanize('__construct');
        $mock = $this->newMockInstance(RpcClientService::class);
        $this->calling($mock)->call = $rpc_response;

        return new TestedClass(
            $this->getPommSession(),
            $mock,
            $this->getContainer()->get('app.manager.basket_order_manager'),
        );
    }

    /**
     * testAddToBasketOrder
     */
    public function testAddToBasketOrder(): void
    {
        $this->assert('Add an svd gift card payment with no remaining balance for given customer basket order')
            ->given(
                $tested_instance = $this->getTestedInstance([
                    'result' => [
                        'svd_gift_card' => [
                            'is_valid' => true,
                            'amount' => 800,
                        ],
                    ],
                ]),
            )
            ->and($manager = $this->getContainer()->get('app.manager.basket_order_manager'))
            ->if($this->mockUserLogin(123456))
            ->and($manager->createNewBasketOrderForCustomer())
            ->when($res = $tested_instance->addToBasketOrder('00000000000000000000', 123456))
            ->boolean($res)
            ->isFalse()

            ->assert('Add an svd_gift card payment with remaining balance for given customer basket order')
            ->given(
                $tested_instance2 = $this->getTestedInstance([
                    'result' => [
                        'svd_gift_card' => [
                            'is_valid' => true,
                            'amount' => 400,
                        ],
                    ],
                ]),
            )
            ->and($manager = $this->getContainer()->get('app.manager.basket_order_manager'))
            ->if($this->mockUserLogin(123456))
            ->and($manager->createNewBasketOrderForCustomer())
            ->when($res = $tested_instance2->addToBasketOrder('00000000000000000000', 123456))
            ->boolean($res)
            ->isTrue()

            ->assert('Add an svd_gift card payment entered with some spaces on the interface')
            ->given(
                $tested_instance2 = $this->getTestedInstance([
                    'result' => [
                        'svd_gift_card' => [
                            'is_valid' => true,
                            'amount' => 400,
                        ],
                    ],
                ]),
            )
            ->and($manager = $this->getContainer()->get('app.manager.basket_order_manager'))
            ->if($this->mockUserLogin(123456))
            ->and($manager->createNewBasketOrderForCustomer())
            ->when($res = $tested_instance2->addToBasketOrder('0000 0000 0000 0000 0000', 123456))
            ->boolean($res)
            ->isTrue()

            ->assert('Fail to add an svd gift card payment on a basket order who does not exists')
            ->if($this->mockUserLogin(24))
            ->and($tested_instance = $this->getTestedInstance([]))
            ->exception(function () use ($tested_instance): void {
                $tested_instance->addToBasketOrder('000000000', 24);
            })
            ->isInstanceOf(\UnexpectedValueException::class)

            ->assert('Fail to add an svd gift card payment when call to bridge fails')
            ->if($this->mockUserLogin(123456))
            ->and(
                $this->getContainer()
                    ->get('app.manager.basket_order_manager')
                    ->createNewBasketOrderForCustomer(),
            )
            ->when($tested_instance = $this->getTestedInstance([]))
            ->exception(function () use ($tested_instance): void {
                $tested_instance->addToBasketOrder('000000000', 123456);
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage('RPC call fails for svd_gift_card:check action')

            ->assert('Fail to add an svd gift card payment when card number is invalid')
            ->if($this->mockUserLogin(123456))
            ->and(
                $this->getContainer()
                    ->get('app.manager.basket_order_manager')
                    ->createNewBasketOrderForCustomer(),
            )
            ->and($card_number = '0101010')
            ->when(
                $tested_instance = $this->getTestedInstance([
                    'result' => [
                        'svd_gift_card' => [
                            'is_valid' => false,
                        ],
                    ],
                ]),
            )
            ->exception(function () use ($tested_instance, $card_number): void {
                $tested_instance->addToBasketOrder($card_number, 123456);
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage(sprintf('Validation for svd gift card failed with card number n°: %s', $card_number))

            ->assert('Fail to add an svd gift card payment on a basket order who does not need an additional payment')
            ->if($this->mockUserLogin(24))
            ->and(
                $this->getContainer()
                    ->get('app.manager.basket_order_manager')
                    ->createNewBasketOrderForCustomer(),
            )
            ->and(
                $this->getContainer()
                    ->get('app.manager.basket_order.payment_handler')
                    ->addToBasketOrderWithRemainingBalance(1, 24),
            )
            ->when($tested_instance = $this->getTestedInstance([]))
            ->exception(function () use ($tested_instance): void {
                $tested_instance->addToBasketOrder('000000000', 24);
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage('The current order does not need additional payment');
    }
}
