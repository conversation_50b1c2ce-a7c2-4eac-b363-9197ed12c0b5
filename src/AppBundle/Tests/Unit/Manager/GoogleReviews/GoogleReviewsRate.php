<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\GoogleReviews;

use SonVideo\Cms\FrontOffice\AppBundle\Manager\GoogleReviews\GoogleReviewsRate as TestedInstance;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

class GoogleReviewsRate extends PommTest
{
    public function setUp(): void
    {
        parent::setUp();

        $this->loadSqlFixtures(['google_reviews.sql']);
    }

    public function getTestedInstance(): TestedInstance
    {
        return new TestedInstance($this->getContainer()->get('pomm.default_session'));
    }

    public function testGet(): void
    {
        $this->assert('Successfully retrieve stored rate')
            ->given($google_reviews_rate = $this->getTestedInstance())
            ->float($google_reviews_rate->get())
            ->isEqualTo(4.9);
    }
}
