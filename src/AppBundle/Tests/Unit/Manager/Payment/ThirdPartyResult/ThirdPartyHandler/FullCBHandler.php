<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Payment\ThirdPartyResult\ThirdPartyHandler;

use SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\ThirdPartyHandler\FullCBHandler as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\UnexpectedResultException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class FullCBHandler
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Payment\ThirdPartyResult\ThirdPartyHandler
 * <AUTHOR> <<EMAIL>>
 */
class FullCBHandler extends Test
{
    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get(TestedClass::class);
    }

    /**
     * getSuccessParameters
     * @return array{REFMERCHANT: string, REFID: string, REFORDER: string, RETURNCODE: string, AMOUNT: string, SIGN: string}
     */
    public static function getSuccessParameters(): array
    {
        return [
            'REFMERCHANT' => '',
            'REFID' => '00000543989999',
            'REFORDER' => '1477670',
            'RETURNCODE' => TestedClass::RESULT_CODE_SUCCESS,
            'AMOUNT' => '26389',
            'SIGN' => 'C736FD9447A7202FCFBB69328FC3A85603CDB6882D98A2A128296319C08DD3D3',
        ];
    }

    /**
     * getCancelParameters
     * @return array{REFMERCHANT: string, REFID: string, REFORDER: string, RETURNCODE: string, AMOUNT: string, SIGN: string}
     */
    public static function getCancelParameters(): array
    {
        return [
            'REFMERCHANT' => '',
            'REFID' => '00000543989999',
            'REFORDER' => '1477670',
            'RETURNCODE' => TestedClass::RESULT_CODE_CANCEL,
            'AMOUNT' => '26389',
            'SIGN' => 'C736FD9447A7202FCFBB69328FC3A85603CDB6882D98A2A128296319C08DD3D3',
        ];
    }

    /**
     * getExceptionParameters
     * @return array{REFMERCHANT: string, REFID: string, REFORDER: string, RETURNCODE: string, AMOUNT: string, SIGN: string}
     */
    public static function getExceptionParameters(): array
    {
        return [
            'REFMERCHANT' => '',
            'REFID' => '00000543989999',
            'REFORDER' => '1477670',
            'RETURNCODE' => '20',
            'AMOUNT' => '26389',
            'SIGN' => 'C736FD9447A7202FCFBB69328FC3A85603CDB6882D98A2A128296319C08DD3D3',
        ];
    }

    /**
     * testGetAssociatedPaymentMethods
     */
    public function testGetAssociatedPaymentMethods(): void
    {
        $this->assert('Returns the good payment methods.')
            ->given($res = $this->getTestedInstance()->getAssociatedPaymentMethods())
            ->array($res)
            ->isEqualTo([PaymentMethod::FULLCB3X_PAYMENT_METHOD, PaymentMethod::FULLCB4X_PAYMENT_METHOD]);
    }

    /**
     * testIsInSuccess
     */
    public function testIsInSuccess(): void
    {
        $this->assert('Is success for valid parameters in FullCB response.')
            ->given($params = static::getSuccessParameters())
            ->given($request = Request::create('/whatever', Request::METHOD_POST, $params))
            ->and($res = $this->getTestedInstance()->isInSuccess($request))
            ->boolean($res)
            ->isTrue->assert('Is not success for valid parameters in FullCB response.')
            ->given($params = static::getCancelParameters())
            ->given($request = Request::create('/whatever', Request::METHOD_POST, $params))
            ->and($res = $this->getTestedInstance()->isInSuccess($request))
            ->boolean($res)
            ->isFalse->assert('Is not success for valid parameters in FullCB response.')
            ->given($params = static::getExceptionParameters())
            ->given($request = Request::create('/whatever', Request::METHOD_POST, $params))
            ->exception(function () use ($request): void {
                $this->getTestedInstance()->isInSuccess($request);
            })
            ->isInstanceOf(UnexpectedResultException::class);
    }
}
