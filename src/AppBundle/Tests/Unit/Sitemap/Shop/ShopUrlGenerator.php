<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Sitemap\Shop;

use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\Shop\ShopUrlGenerator as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class ShopUrlGenerator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Sitemap\Shop
 * <AUTHOR> <<EMAIL>>
 */
class ShopUrlGenerator extends PommTest
{
    /**
     * getInstance
     */
    private function getInstance(): TestedClass
    {
        return new TestedClass($this->getContainer()->get('pomm'), $this->getContainer()->get('router'));
    }

    /**
     * testGenerate
     */
    public function testGenerate(): void
    {
        $router = $this->getContainer()->get('router');

        $this->assert('Check url generation for shop is working.')
            ->given($res = $this->getInstance()->generate())
            ->array($res)
            ->contains($router->generate('show_shops'))
            ->contains($router->generate('show_shop', ['slug' => 'coupe-d-europe-de-football']))
            ->contains(
                $router->generate('show_shop', ['slug' => 'jbl-en-promotion-enceintes-caissons-barres-de-son-etc']),
            )
            ->contains($router->generate('show_shop', ['slug' => 'systeme-multiroom-harman-kardon-omni']));
    }
}
