<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Sitemap\StaticPage;

use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\StaticPage\StaticPageUrlGenerator as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class StaticPageUrlGenerator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Sitemap\StaticPage
 * <AUTHOR> <<EMAIL>>
 */
class StaticPageUrlGenerator extends PommTest
{
    /**
     * getInstance
     */
    private function getInstance(): TestedClass
    {
        return new TestedClass($this->getContainer()->get('app.static_page_router'));
    }

    /**
     * testGenerate
     */
    public function testGenerate(): void
    {
        $this->assert('Check url generation for stand is working.')
            ->given($res = $this->getInstance()->generate())
            ->array($res)
            ->contains('/avis-facebook')
            ->contains('/2017-une-annee-musicale')
            ->contains('/faq/transporteurs');
    }
}
