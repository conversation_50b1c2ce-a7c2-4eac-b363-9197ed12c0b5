<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ProductModel;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\RangeContextFilter;

/**
 * Class MarginRateFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class MarginRateFilter extends AbstractFilter implements FilterInterface
{
    /**
     * getFilterKey
     */
    public static function getFilterKey(): string
    {
        return 'margin_rate';
    }

    /**
     * getFilterType
     */
    public static function getFilterType(): string
    {
        return RangeContextFilter::class;
    }

    /**
     * makeQuery
     *
     * @param   mixed $filter
     */
    public function makeQuery(Where $conditions, $filter): string
    {
        if (!$filter instanceof RangeContextFilter) {
            throw new \RuntimeException('Filter must be an instance of RangeContextFilter.');
        }

        $sql = <<<SQL
        SELECT
          a.article_id
        FROM {article} a
          INNER JOIN {product} p ON p.sku = a.sku
        WHERE {conditions}
        SQL;

        $sub_cond = Where::create();
        if ($filter->getMinValue() !== null) {
            $sub_cond->andWhere('p.margin_rate >= $*', [$filter->getMinValue() / 100]);
        }

        if ($filter->getMaxValue() !== null) {
            $sub_cond->andWhere('p.margin_rate <= $*', [$filter->getMaxValue() / 100]);
        }

        $conditions->andWhere($sub_cond);

        return strtr($sql, [
            '{conditions}' => $sub_cond,
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
        ]);
    }
}
