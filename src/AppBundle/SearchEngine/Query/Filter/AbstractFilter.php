<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter;

use PommProject\ModelManager\Session as PommSession;

/**
 * Class AbstractFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter
 * <AUTHOR> <<EMAIL>>
 */
abstract class AbstractFilter
{
    private PommSession $pomm_session;

    /**
     * AbstractFilter constructor.
     */
    public function __construct(PommSession $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * getModelRelation
     */
    protected function getModelRelation(string $class_name): string
    {
        return $this->pomm_session
            ->getModel($class_name)
            ->getStructure()
            ->getRelation();
    }
}
