.sacempro-header
{
    background: #000 url('https://image.son-video.com/images/illustration/pages/sacemPro/SVD_BTOB_201701-SacemPro.jpg') no-repeat 0 30%;
    background-size: cover;
}

.btn-purple, .btn-purple:focus, .btn-purple:active
{
    color: #fff;
    background-color: #826aaf;
    border-color: #826aaf;
}

.btn-purple:focus, .btn-purple:active, .btn-purple:hover
{
    color: #fff;
    background-color: #624b93;
    border-color: #624b93;
}

.btn-offwhite, .btn-offwhite:focus, .btn-offwhite:active
{
    color: #826aaf;
}

.btn-offwhite:focus, .btn-offwhite:active, .btn-offwhite:hover
{
    color: #624b93;
}

.sacem-grid-item
{
    max-width: 300px;
    height: 230px;
    margin: 0 auto 30px auto;
    text-align: center;
    position: relative;
}

.sacem-grid-item a
{
    display: block;
    width: 100%;
    height: 100%;
    background: #826aaf;
}

.sacem-grid-item h3
{
    padding: 15px 10px !important;
    color: #fff !important;
    background: #826aaf !important;
    font-weight: 300 !important;
    margin: 0 !important;
    -webkit-transition: padding .3s ease;
    -moz-transition: padding .3s ease;
    -o-transition: padding .3s ease;
    transition: padding .3s ease;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
}

.sacem-grid-item a:hover
{
    background: #624b93;
}

.sacem-grid-item a:hover h3
{
    padding: 20px 10px !important;
    background: #624b93 !important;
}
