describe('check back to top feature', function () {
    before(function () {
        cy.setupDatabase()
        cy.mockRPCCall('bo-cms', 'customer:get_status_newsletter', '{"status": "inactive" }')
    })

    beforeEach(function () {
        cy.reloadFixtures()
        cy.clearCookies()
    })

    it('Check button in V5', function () {
        cy.preAcceptSiteCookies().visit('/')

        cy.get('[data-context=back-to-top]').should('not.be.visible')
        cy.get('[data-context=stores-near-you]').scrollIntoView().should('be.visible')
        cy.get('[data-context=back-to-top]').scrollIntoView().should('be.visible').click()
        cy.get('[data-context=logo]').should('be.visible')
        cy.get('[data-context=back-to-top]').should('not.be.visible')
    })

    it('Check button in V3', function () {
        cy.preAcceptSiteCookies().visit('/article/elipson-prestige-4i-calvados-fr')

        cy.get('#fiche_Caracteristiques').scrollIntoView().should('be.visible')
        cy.get('#back-to-top').scrollIntoView().should('be.visible').click()
        cy.get('[data-context=logo]').should('be.visible')
        cy.get('#back-to-top').should('not.be.visible')
    })

    it(`Is not displayed in customer's space`, () => {
        cy.log('=== check v3 pages ===')
        cy.login('jack', '/mon-compte')
        cy.get('footer').scrollIntoView()
        cy.get('#back-to-top').should('not.exist')

        cy.log('=== check v5 pages ===')
        cy.visit('/mon-compte/mes-devis-et-offres')
        cy.get('footer').scrollIntoView()
        cy.get('#back-to-top').should('not.exist')
    })
})
