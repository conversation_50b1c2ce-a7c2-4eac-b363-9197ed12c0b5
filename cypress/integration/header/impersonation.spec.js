import { SITE_VERSIONS } from './helpers'

before(() => {
    cy.setupDatabase()
})
beforeEach(() => {
    // reset and seed the database prior to every test
    cy.reloadFixtures()
    cy.mockRPCCall('bo-cms', 'customer:get_status_newsletter', '{"status": "inactive" }')
    cy.intercept('GET', '/api/customer-info').as('customer_info')
    cy.clearCookies()
})

describe('Impersonation', () => {
    SITE_VERSIONS.forEach((site_version) => {
        context(site_version.name, () => {
            it('should let a staff impersonate a customer with a valid token', () => {
                cy.preAcceptSiteCookies().visit('/impersonation/login?token=a803d25a46c8ea732ecb1525a3971c0f')

                cy.wait('@customer_info')

                cy.visit(site_version.page)

                // Check user info
                cy.wait('@customer_info').then((payload) => {
                    expect(payload.response.body).to.deep.eq({
                        user: {
                            fullname: '<PERSON>',
                            firstname: '<PERSON>',
                            lastname: '<PERSON>',
                            username: '<EMAIL>',
                            civility: 'Mr',
                            phone: '0124598763',
                            is_authenticated_fully: true,
                            impersonation_username: '<EMAIL>',
                            has_newsletter: false,
                            show_cancelled_orders: false,
                        },
                        quotes: {
                            count: 3,
                        },
                        features: [],
                    })
                })

                // check the impersonation toolbar
                cy.get('[data-context=impersonation]').should('be.visible').as('impersonation')
                cy.get('@impersonation').find('[data-context=badge]').should('contain', 'Mode conseiller')
                cy.get('@impersonation').find('span').eq(0).should('contain', 'Vous êtes connecté en tant que')
                cy.get('@impersonation').find('span').eq(1).should('contain', '<EMAIL>')
                cy.get('@impersonation')
                    .find('[data-context=logout]')
                    .should('have.attr', 'href', '/mon-compte/deconnexion')
                    .click()

                // Check user info after logout
                cy.wait('@customer_info').then((payload) => {
                    expect(payload.response.body).to.deep.eq({ user: null, features: [] })
                })

                // no toolbar
                cy.get('[data-context=impersonation]').should('not.exist')
            })

            it('should not allow a staff to impersonate a customer with an invalid token', () => {
                cy.preAcceptSiteCookies().visit('/impersonation/login?token=invalid')

                cy.wait('@customer_info')

                cy.visit(site_version.page)

                // not logged in
                cy.wait('@customer_info').then((payload) => {
                    expect(payload.response.body).to.deep.eq({ user: null, features: [] })
                })

                // no toolbar
                cy.get('[data-context=impersonation]').should('not.exist')
            })

            it('should not show the impersonation toolbar for a regular logged in user', () => {
                cy.visit('/impersonation/login?token=none')

                cy.wait('@customer_info')

                cy.login('jack', site_version.page)

                // Check user info : no impersonation
                cy.wait('@customer_info').then((payload) => {
                    expect(payload.response.body).to.deep.eq({
                        user: {
                            fullname: 'Jack Bauer',
                            firstname: 'Jack',
                            lastname: 'Bauer',
                            username: '<EMAIL>',
                            civility: 'Mr',
                            phone: '0124598763',
                            is_authenticated_fully: true,
                            impersonation_username: null,
                            has_newsletter: false,
                            show_cancelled_orders: false,
                        },
                        quotes: {
                            count: 3,
                        },
                        features: [],
                    })
                })

                // no toolbar
                cy.get('[data-context=impersonation]').should('not.exist')
            })
        })
    })
})
