import { beforeAll, getViewportConfig, SITE_VERSIONS, visitDesktop, visitMobile } from './helpers'

describe('Header - Search', () => {
    before(beforeAll)
    SITE_VERSIONS.forEach((version) => {
        describe(version.name, function () {
            const PAGE = version.page

            before(function () {
                if ('v5' === version.name) {
                    cy.applySqlFixture('home/default.sql')
                }
            })

            context('Mobile', () => {
                before(function () {
                    visitMobile(PAGE, version.name)
                })
                context('Mobile (between 320px and 380px)', getViewportConfig([321, 736]), function () {
                    it('should show the search link and show/hide the search input', function () {
                        cy.get('header [data-context=mobile] [data-context=right] [data-context=search-link]').as(
                            'search_link'
                        )

                        cy.get('header [data-context=search-dropdown]').should('not.exist')

                        // show the search input
                        cy.get('@search_link').should('be.visible').click()

                        cy.get('header [data-context=search-dropdown]').as('dropdown')
                        cy.get('@dropdown').find('[data-context=search-results]').should('not.exist')
                        cy.get('@dropdown').find('[data-context=search-mobile]').should('be.visible')

                        // temporize for 2ms in order to not be f***ed by the debounce
                        cy.wait(200)

                        // hide the search input
                        cy.get('[data-context=close-search-button]').should('be.visible').click()
                        cy.get('header [data-context=search-dropdown]').should('not.exist')
                    })

                    it('should show a message if there is no result to show', function () {
                        cy.server()
                        cy.route('GET', '/autocomplete**', () => ({
                            products: [],
                            stands: [],
                            brands: [],
                        })).as('autocomplete')
                        visitMobile(PAGE, version.name)

                        cy.get('header [data-context=mobile] [data-context=right] [data-context=search-link]')
                            .should('be.visible')
                            .click()

                        cy.get('header [data-context=search-dropdown] [data-context=search-results]').should(
                            'not.exist'
                        )

                        cy.get('header [data-context=search-dropdown] [data-context=search-mobile] input').type(
                            'something'
                        )

                        cy.wait('@autocomplete')

                        cy.get('header [data-context=search-dropdown] [data-context=search-results]').should(
                            'not.exist'
                        )

                        cy.get('header [data-context=search-dropdown] [data-context=no-results]').should(
                            'contain',
                            "Notre moteur de recherche n'a pas pu répondre à votre demande."
                        )

                        cy.get('header [data-context=search-dropdown] [data-context=no-results]').should(
                            'contain',
                            'Vous pouvez compléter ou modifier votre recherche pour obtenir plus de résultats.'
                        )

                        // clear input from the UI
                        cy.get('header [data-context=search-dropdown] [data-context=close-search-button]').click()

                        cy.get('header [data-context=search-dropdown] [data-context=search-mobile]').should('not.exist')
                    })

                    it('should show a limited set of results on mobile', function () {
                        cy.clearCookies()
                        cy.server()
                        cy.route('GET', '/autocomplete**', 'fixture:autocomplete/search_articles.json').as(
                            'autocomplete'
                        )
                        visitMobile(PAGE, version.name)

                        cy.get('header [data-context=mobile] [data-context=right] [data-context=search-link]')
                            .should('be.visible')
                            .click()

                        cy.get('header [data-context=search-dropdown] [data-context=search-results]').should(
                            'not.exist'
                        )

                        cy.get('header [data-context=search-dropdown] [data-context=search-mobile] input')
                            .should('have.focus')
                            .type('test something')

                        cy.get('header [data-context=search-dropdown] [data-context=search-results]').should(
                            'be.visible'
                        )

                        // see more link
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=see-more-link]'
                        )
                            .should('be.visible')
                            .should('have.attr', 'href', '/recherche?termes=test+something')
                            .should('contain', 'Voir plus')

                        // check results
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=product-result]'
                        ).should('have.length', 11)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=product-result]:visible'
                        ).should('have.length', 4)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=stand]'
                        ).should('have.length', 5)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=stand]:visible'
                        ).should('have.length', 4)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=brand]'
                        ).as('brands')
                        cy.get('@brands').should('have.length', 3)
                        cy.get('@brands').eq(0).scrollIntoView()
                        cy.get('@brands').eq(0).should('be.visible')
                        cy.get('@brands').eq(1).should('be.visible')
                        cy.get('@brands').eq(2).should('not.be.visible')

                        // close button in results
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=close-btn]'
                        ).should('not.be.visible')
                    })

                    it('should redirect to the search page upon pressing enter in the search field', function () {
                        cy.server()
                        visitMobile(PAGE, version.name)

                        cy.get('header [data-context=mobile] [data-context=right] [data-context=search-link]')
                            .should('be.visible')
                            .click()

                        cy.get('header [data-context=search-dropdown] [data-context=search-mobile] input').type(
                            'test{enter}'
                        )

                        cy.url().should('eq', Cypress.config().baseUrl + '/recherche?termes=test')
                    })

                    it('should redirect to the search page upon pressing the magnifying glass icon in the search input', function () {
                        cy.server()
                        visitMobile(PAGE, version.name)
                        cy.get('header [data-context=mobile] [data-context=right] [data-context=search-link]')
                            .should('be.visible')
                            .click()

                        cy.get('header [data-context=search-dropdown] [data-context=search-mobile] input').type('test')
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-mobile] [data-context=search-icon]'
                        ).click()
                        cy.url().should('eq', Cypress.config().baseUrl + '/recherche?termes=test')
                    })
                })

                context('Mobile (between 380px and 640px', getViewportConfig([386, 736]), function () {
                    it('should show the search link', function () {
                        cy.get('header [data-context=mobile] [data-context=right] [data-context=search-link]').should(
                            'be.visible'
                        )
                    })
                })

                context('Tablet (between 640px and 768px)', getViewportConfig([641, 736]), function () {
                    it('should show the search link', function () {
                        cy.get('header [data-context=mobile] [data-context=right] [data-context=search-link]').should(
                            'be.visible'
                        )
                    })
                })
            })

            context('Desktop', () => {
                before(function () {
                    visitDesktop(PAGE, version.name)
                })

                context('Tablet (between 768px and 1024px)', getViewportConfig([769, 768]), function () {
                    it('should show the search input', function () {
                        cy.get('header [data-context=top] [data-context=search-input]').should('be.visible')
                    })
                })

                context('Large screen (more than 1920px)', getViewportConfig([1920, 1080]), function () {
                    it('should show the search input', function () {
                        cy.get('header [data-context=top] [data-context=search-input]').should('be.visible')
                    })

                    it('should show all results on desktop', function () {
                        cy.server()
                        cy.route('GET', '/autocomplete**', 'fixture:autocomplete/search_articles.json').as(
                            'autocomplete'
                        )

                        cy.get('header [data-context=search-dropdown] [data-context=search-results]').should(
                            'not.exist'
                        )

                        cy.get('header [data-context=top] [data-context=search-input]').type('test something')

                        cy.get('header [data-context=search-dropdown] [data-context=search-results]').should(
                            'be.visible'
                        )

                        // see more link
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=see-more-link]'
                        )
                            .should('be.visible')
                            .should('have.attr', 'href', '/recherche?termes=test+something')
                            .should('contain', 'Voir tous les produits')

                        // check results
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=product-result]'
                        ).should('have.length', 11)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=product-result]:visible'
                        ).should('have.length', 11)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=stand]'
                        ).should('have.length', 5)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=stand]:visible'
                        ).should('have.length', 5)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=brand]'
                        ).should('have.length', 3)

                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=brand]:visible'
                        ).should('have.length', 3)

                        // close button in results
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=close-btn]'
                        )
                            .should('be.visible')
                            .click()
                        cy.get('header [data-context=search-dropdown] [data-context=search-results]').should(
                            'not.exist'
                        )
                    })

                    it('should display promotion tag', function () {
                        cy.server()
                        cy.route('GET', '/autocomplete**', 'fixture:autocomplete/search_articles.json').as(
                            'autocomplete'
                        )
                        cy.get('[data-context=search-input]').type('test something')
                        cy.get('[data-context=product-result]').eq(0).contains('Pré-commande').should('exist')
                        cy.get('[data-context=product-result]').eq(1).contains('Black Friday').should('exist')
                        cy.get('[data-context=product-result]').eq(2).contains('Seconde vie').should('exist')
                        cy.get('[data-context=product-result]').eq(3).contains('Exclusivité').should('exist')
                        cy.get('[data-context=product-result]').eq(4).contains('French Days').should('exist')
                        cy.get('[data-context=product-result]').eq(5).contains('Promotion').should('not.exist')
                        cy.get('[data-context=product-result]').eq(6).contains('Prix qui tue').should('exist')
                        cy.get('[data-context=product-result]').eq(7).contains('Soldes').should('exist')
                        cy.get('[data-context=product-result]').eq(8).contains('Ventes privées').should('exist')
                        cy.get('[data-context=product-result]').eq(9).contains('Idée cadeau').should('exist')
                        cy.get('[data-context=product-result]').eq(10).contains('Nouveau').should('exist')
                    })

                    it('should show some info in each block', function () {
                        cy.server()
                        cy.route('GET', '/autocomplete**', 'fixture:autocomplete/search_articles.json').as(
                            'autocomplete'
                        )

                        cy.get('header [data-context=top] [data-context=search-input]').type('test something')

                        // Check all block titles
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=search-dropdown-block] [data-context=title]'
                        ).as('block-titles')
                        ;['Produits', 'Rayons', 'Marques'].forEach((title, index) => {
                            cy.get('@block-titles').eq(index).should('contain', title)
                        })

                        // check an article
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=product-result]'
                        )
                            .eq(0)
                            .as('article')

                        cy.get('@article').find('img')

                        cy.get('@article').find('span').eq(0).should('contain', 'KEF LS50 Noir laqué')
                        cy.get('@article').find('span').eq(1).should('contain', 'KEF LS50 Noir laqué')
                        cy.get('@article').find('span').eq(2).should('contain', 'Enceintes bibliothèque')
                        cy.get('@article').find('div').should('contain', '475').and('contain', '€')

                        // check a stand
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=stand]'
                        )
                            .eq(0)
                            .as('stand')

                        cy.get('@stand')
                            .should('have.attr', 'href')
                            .and('contain', '/rayon/enceintes/enceintes/enceintes-compactes')

                        cy.get('@stand').should('contain', 'Enceintes bibliothèque')

                        // check a brand
                        cy.get(
                            'header [data-context=search-dropdown] [data-context=search-results] [data-context=brand]'
                        )
                            .eq(0)
                            .as('brand')

                        cy.get('@brand').should('have.attr', 'href').and('contain', '/marque/kef')

                        cy.get('@brand')
                            .find('img')
                            .should('have.attr', 'src')
                            .and('contain', '/images/static/marques/KEF.gif')
                    })

                    it('should redirect to the search page upon pressing enter in the search field', function () {
                        cy.server()
                        visitDesktop(PAGE, version.name)
                        cy.get('header [data-context=top] [data-context=search-input]').type('test{enter}')
                        cy.url().should('eq', Cypress.config().baseUrl + '/recherche?termes=test')
                    })

                    it('should redirect to the search page upon pressing the magnifying glass icon in the search input', function () {
                        cy.server()
                        visitDesktop(PAGE, version.name)
                        cy.get('header [data-context=top] [data-context=search-input]').type('toto')
                        cy.get('header [data-context=top] [data-context=search-icon]').click()
                        cy.url().should('eq', Cypress.config().baseUrl + '/recherche?termes=toto')
                    })

                    it('should track when the user click on a result', function () {
                        cy.server()
                        cy.route('GET', '/autocomplete**', 'fixture:autocomplete/search_articles.json').as(
                            'autocomplete'
                        )
                        cy.route('POST', '/autocomplete/click', () => ({
                            success: true,
                        })).as('track-autocomplete-click')
                        visitDesktop(PAGE, version.name)
                        cy.get('header [data-context=top] [data-context=search-input]').type('ma recherche')
                        cy.wait('@autocomplete')
                        cy.get('[data-context=product-result]').eq(0).click()
                        cy.wait('@track-autocomplete-click').then((xhr) => {
                            expect(xhr.request.body).to.deep.eq({
                                document_id: '111111',
                                search_terms: 'ma recherche',
                            })
                        })
                    })
                })
            })
        })
    })
})
