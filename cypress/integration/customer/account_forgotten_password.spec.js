describe('Forgotten password', function () {
    const PAGE = '/mon-compte/reinitialisation-mot-de-passe'

    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        // reset and seed the database prior to every test
        cy.reloadFixtures()
    })

    describe('Check the email field', function () {
        it(`Don't fill the email field`, function () {
            cy.visit(PAGE)

            cy.get('input[type=email]').should('be.empty')
        })

        it('Fill the email field', function () {
            cy.visit(`${PAGE}?email=admin%40son-video.com`)

            cy.get('input[type=email]').should('have.value', '<EMAIL>')
        })
    })
})
