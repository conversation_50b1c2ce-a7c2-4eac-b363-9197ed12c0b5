describe('Quote list page', () => {
    const formatDate = (date, custom_options) => {
        return new Intl.DateTimeFormat(
            'fr-FR',
            custom_options || {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
            }
        ).format(date)
    }

    before(() => {
        cy.setupDatabase()
    })

    beforeEach(() => {
        // reset and seed the database prior to every test
        cy.reloadFixtures()
        cy.applySqlFixture('customer/add_quote_offer_for_jack.sql')

        cy.login('jack', '/mon-compte/mes-devis-et-offres')
    })

    describe('New quote block in list', () => {
        it('should display all new quotes correctly', function () {
            // the dynamic date is set in sql fixtures hence why we can't use cy.clock to freeze the date
            // today + 15 days
            let expiration_date = formatDate(new Date(new Date().getTime() + 15 * 24 * 60 * 60 * 1000))
            cy.get('[data-context=quote]')
                .should('have.length', 7)
                .eq(2)
                .within(() => {
                    cy.get('[data-context=single-image]').should('have.length', 2)
                    cy.get('[data-context=item-data-name]').should('contain', 'Offre n°2000272')
                    cy.get('[data-context=available-date]').should('contain', `Valide jusqu'au ${expiration_date}`)

                    cy.get('[data-context=item-data-price]').should('contain', `1 107 €`)

                    // for now no link to details page
                    cy.get('[data-context=see-details-btn]').should('be.visible')
                    cy.get('[data-context=add-to-cart-btn]').should('be.visible')
                    cy.get('[data-context=pay-btn]').should('not.exist')
                })

            // the dynamic date is set in sql fixtures hence why we can't use cy.clock to freeze the date
            // today + 15 days
            expiration_date = formatDate(new Date(new Date().getTime() + 15 * 24 * 60 * 60 * 1000))
            cy.get('[data-context=quote]')
                .eq(3)
                .within(() => {
                    cy.get('[data-context=single-image]').should('have.length', 1)

                    cy.get('[data-context=item-data-name]').should('contain', 'Devis n°1000271')
                    cy.get('[data-context=available-date]').should('contain', `Valide jusqu'au ${expiration_date}`)

                    cy.get('[data-context=item-data-price]').should('contain', `326,88 €`)

                    cy.get('[data-context=see-details-btn]').should('be.visible')
                    cy.get('[data-context=add-to-cart-btn]').should('not.exist')
                    cy.get('[data-context=pay-btn]').should('be.visible')
                })
        })
    })

    describe('Highlighting quotes', () => {
        it('Should display quote badge', function () {
            cy.get('[data-context=account-quote-badge]').should('exist').contains('5')
        })
    })
})
