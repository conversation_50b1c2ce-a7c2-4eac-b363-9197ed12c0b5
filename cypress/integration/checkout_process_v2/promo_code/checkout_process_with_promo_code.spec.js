import { checkTimeline, TIMELINE_STEPS } from '../helpers'

describe('Sales funnel - Recap for command with gift promo code', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        cy.reloadFixtures()

        cy.applySqlFixture('checkout_process_v2/cart_with_promo_code_gift.sql')

        cy.server()
    })

    describe('Promo code Gift', function () {
        const visitPage = () => {
            cy.login('chuck', `/commander`)
        }

        it('should display basket order content with gift', function () {
            visitPage()

            cy.intercept('GET', '/**/ma-commande/v2').as('get_customer_order')
            cy.wait('@get_customer_order').then((xhr) => {
                const article_selling_price = xhr.response.body.summary[0].selling_price
                const discount_amount = xhr.response.body.total_discount_tax_included

                cy.get('[data-context=articles-price]').should('contain', discount_amount + article_selling_price)
                cy.get('[data-context=articles-discount]').should('contain', discount_amount)
                cy.get('[data-context=total-price]').should('contain', article_selling_price)
            })

            checkTimeline(TIMELINE_STEPS.delivery)

            cy.get('[data-context=sales-funnel]').should('be.visible')
            cy.get('[data-context=sales-funnel] [data-context=title-detail]').should('contain', 2)

            cy.get('[data-context=article-details] [data-context=description]')
                .eq(0)
                .should('contain', 'Prestige 4i Calvados')
            cy.get('[data-context=article-details] [data-context=description]')
                .eq(1)
                .should('contain', 'super enceinte')

            cy.get('[data-context=articles-discount]').should('contain', -100)
        })
    })
})
