export const createCustomerOrderInErp = (customer_id = 24, customer_order_id = 42) => {
    cy.mockRPCCall('erp', 'customer_order:create', JSON.stringify({ customer_order_id }))
    cy.mockRPCCall(
        'erp',
        'customer_order:fetch_for_cms',
        JSON.stringify({
            customer_order_id,
            created_at: '2018-09-11 00:00:00',
            modified_at: '2018-09-11 12:30:00',
            number_of_visits: 0,
            customer_id: customer_id,
            relay_id: null,
            quote_id: null,
            total_price: 249,
            total_price_vat_excluded: 207.5,
            ecotax_price: 0,
            shipping_price: 0,
            shipment_method_id: 1,
            billing_address: {
                address: 'tmp',
                cellphone: '',
                city: '',
                civility: 'M.',
                company_name: '',
                country_code: 'FR',
                first_name: '',
                last_name: '',
                phone: null,
                postal_code: '',
                price: 0,
            },
            shipping_address: {
                address: 'tmp',
                cellphone: '',
                city: '',
                civility: 'M.',
                company_name: '',
                country_code: 'FR',
                first_name: '',
                last_name: '',
                phone: null,
                postal_code: '',
            },
            products: [
                {
                    amount_extension_warranty: 0,
                    amount_theft_break_extension_warranty: 0,
                    article_id: 81078,
                    basket_description: 'rBlink',
                    duration_extension_warranty: null,
                    duration_theft_break_extension_warranty: null,
                    quantity: 1,
                    sku: 'ARCAMRBLINKNR',
                    total_amount: 249,
                },
            ],
            payments: [
                {
                    amount: 0,
                    code: 'CBS-O',
                    complement: 0,
                    customer_order_payment_id: 100,
                    payment_method_id: 32,
                    back_payment_method_id: 59,
                    extra_data: [],
                },
                {
                    amount: 249,
                    code: 'CBS-O',
                    complement: 0,
                    customer_order_payment_id: 101,
                    payment_method_id: 32,
                    back_payment_method_id: 59,
                    extra_data: [],
                },
            ],
        })
    )
    cy.mockRPCCall('erp', 'customer_order_payment:create', JSON.stringify([true]))
    cy.mockRPCCall('erp', 'customer_order_payment:handle_bank_redirection', JSON.stringify({ action: false }))
    cy.mockRPCCall('erp', 'customer_order:activate', JSON.stringify([true]))
}

export const cancelCustomerOrderPayments = (payments = []) => {
    cy.mockPaymentV2Call('/internal-api/v1/tunnel/aborted', '[]')

    // only payments are required in response
    cy.mockRPCCall('erp', 'customer_order:fetch_for_cms', JSON.stringify({ payments }))
}

export const TIMELINE_STEPS = {
    shopping_cart: 0,
    delivery: 2,
    payment: 3,
}

export const checkTimeline = (current_step) => {
    cy.log(`=== Check Timeline, step ${current_step} ===`)
    cy.get('[data-context=timeline]').scrollIntoView().should('be.visible')
    Object.values(TIMELINE_STEPS).forEach((step) => {
        if (current_step > step) {
            cy.get(`[data-context=step-${step}-completed]`).should('be.visible')

            return
        }

        cy.get(`[data-context=step-${step}-${current_step === step ? 'current' : 'upcoming'}]`).should('be.visible')
    })
}
export const checkNoTimeline = () => {
    cy.log(`=== Check NO Timeline ===`)
    cy.get('[data-context=timeline]').should('not.exist')
}
