describe('Articles can be listed on stand', function () {
    const PAGE = '/rayon/television/televiseurs/tv-uhd-8k'
    const ITEMS_PER_PAGE = 36
    const ALL_ARTICLE_IDS = [
        136687, 147147, 147502, 136904, 146168, 149660, 149788, 136905, 146195, 146245, 146510, 147415, 149662, 136906,
        146205, 139290, 150553, 140201, 140202, 140403, 146508, 140735, 146167, 146228, 147382, 147407, 148677, 141758,
        146194, 146244, 146795, 146802, 146948, 147411, 147418, 145827, 145999, 146159, 146936, 145829, 145830, 147504,
        149427, 145831, 151168, 145832, 146199, 146850, 145833, 146608, 146949, 150298, 145997, 146906, 146938, 148993,
        149425, 146001, 146521, 146941, 146003, 146163, 146192, 146524, 146525, 146942, 147416, 148587, 149813, 146162,
        146223, 146523, 146165, 146196, 146227, 146526, 147417, 146193, 146251, 146253, 146254, 149787, 146197, 146241,
        146249, 146250, 146606, 146945, 147413, 146200, 146950, 147419, 146201, 146953, 146202, 146507, 146203, 146204,
        146217, 146789, 146219, 146225, 146240, 146940, 146243, 146247, 146248, 150066, 146382, 146522, 148995, 146383,
        146937, 146944, 146384, 146505, 146506, 146509, 146511, 146512, 146520, 150550, 146541, 146605, 146785, 148991,
        146786, 146792, 146934, 148992, 146788, 146791, 148270, 146793, 147146, 149426, 146797, 146947, 146951, 146800,
        146803, 146905, 146907, 146908, 146909, 146910, 146946, 146939, 146943, 147505, 146952, 147148, 147150, 147153,
        151157, 151174, 147408, 147409, 148266, 147503, 148265, 150552, 148267, 148272, 148268, 148301, 148269, 148271,
        151175, 148273, 148312, 148274, 148302, 148304, 148305, 148306, 148314, 148308, 148313, 150551, 148315, 148316,
        148319, 148756, 148758, 148759, 148997, 150546, 150548, 151167, 150549, 150554, 151170, 151179, 151180,
    ]

    before(function () {
        cy.setupDatabase()
    })

    describe('Tests javascript behavior with totally mocked API calls', function () {
        beforeEach(function () {
            // For some obscur reason, the database seems to need a break before starting the next test, sometimes.
            cy.wait(500)

            // reset and seed the database prior to every test
            cy.reloadFixtures()
            // necessary only because we need a stand using the new display
            cy.applySqlFixture('article_lister/eav.sql')

            cy.intercept('GET', `${PAGE}/_filters**`, { fixture: 'api/rayon/{slug}/_filters/article_filters.json' }).as(
                'api_filters'
            )
            cy.intercept('POST', '/articles/_search**', { fixture: 'api/articles/_search/article_lister.json' }).as(
                'api_search'
            )
        })

        describe('Pagination works as expected', function () {
            it('dynamically display a button to load more elements', function () {
                cy.visit(PAGE)
                cy.wait('@api_filters')
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('not.contain', 'Chargement')

                cy.get('[data-context="filters-summary"]').should('contain', '195 résultats')
                cy.get('nav.article-pagination').should('exist')
                cy.get('.article-pagination a[aria-label="Page suivante"]').should('exist')
                cy.get('.article-pagination a[aria-label="Page suivante"]')
                    .should('have.attr', 'href')
                    .and('include', 'page=2')
                cy.get('.article-pagination a[aria-label="Page précédente"]').should('not.exist')
                cy.get('.article-pagination .pagination-item').should('have.length', 7) // 6 pages (195 / 36) + next
                cy.get('.article-pagination li.pagination-item:visible a').should('have.length', 3) // page 2, 6, and next
                cy.get('.article-pagination .ellipsis').should('be.visible')
                cy.get('.article-pagination .pagination-item.active').should('contain', '1')
                cy.get('.article-pagination .pagination-item.active').should('have.attr', 'aria-current', 'page')

                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(0)
                    .should('have.attr', 'href')
                    .and('include', 'page=2')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(0)
                    .should('have.attr', 'aria-label', 'Aller à la page 2')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(1)
                    .should('have.attr', 'href')
                    .and('include', 'page=6')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(1)
                    .should('have.attr', 'aria-label', 'Aller à la page 6')
                cy.get('.article-pagination li.pagination-item:hidden a')
                    .eq(0)
                    .should('have.attr', 'href')
                    .and('include', 'page=3')
                cy.get('.article-pagination li.pagination-item:hidden a')
                    .eq(0)
                    .should('have.attr', 'aria-label', 'Aller à la page 3')

                cy.get('[data-context="search-filter"]').as('filters')

                // select LG brand (22 articles) => hide pagination when no more page
                cy.get('@filters').eq(1).find('[data-context="checkbox"] label').eq(0).should('contain', 'LG').click()
                cy.get('[data-context="filters-summary"]').should('contain', '22 résultats')
                cy.get('.article-pagination').should('not.exist')

                // select TCL brand (15 articles + 22 previous = 37 articles) => only 1 page link
                cy.get('@filters').eq(1).find('[data-context="checkbox"] label').eq(5).should('contain', 'TCL').click()
                cy.get('[data-context="filters-summary"]').should('contain', '37 résultats')
                cy.get('.article-pagination').should('exist')
                cy.get('.article-pagination a[aria-label="Page suivante"]').should('exist')
                cy.get('.article-pagination a[aria-label="Page suivante"]')
                    .should('have.attr', 'href')
                    .and('include', 'page=2')
                cy.get('.article-pagination a[aria-label="Page précédente"]').should('not.exist')
                cy.get('.article-pagination .pagination-item').should('have.length', 3) // 2 pages (37 / 36) + next
                cy.get('.article-pagination .pagination-item.active').should('contain', '1')
                cy.get('.article-pagination li.pagination-item a').should('have.attr', 'href').and('include', 'page=2')
                cy.get('.article-pagination li.pagination-item a').should(
                    'have.attr',
                    'aria-label',
                    'Aller à la page 2'
                )
            })

            it('load next page on button click', function () {
                cy.visit(PAGE)
                cy.wait('@api_filters')
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('not.contain', 'Chargement')

                // Check initial canonical URL
                cy.get('head link[rel="canonical"]').should('have.attr', 'href').and('not.include', 'page=')

                // Check initial pagination links - page 1 should have next but no prev
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=2')
                cy.get('head link[rel="prev"]').should('not.exist')

                cy.get('.article-pagination a[aria-label="Page suivante"]').click()
                cy.url().should('include', 'page=2')
                cy.wait('@api_search').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        article_ids: ALL_ARTICLE_IDS,
                        page: 2,
                        item_per_page: ITEMS_PER_PAGE,
                        sort_by: { has_estimated_delivery_time: 'DESC' },
                    })
                })

                // Check that canonical URL is updated with page parameter
                cy.get('head link[rel="canonical"]').should('have.attr', 'href').and('include', 'page=2')

                // Check pagination links on page 2 - should have both next and prev
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=3')
                cy.get('head link[rel="prev"]').should('have.attr', 'href').and('not.include', 'page=') // page 1 has no page param

                cy.get('.article-pagination a[aria-label="Page suivante"]').should('exist')
                cy.get('.article-pagination a[aria-label="Page suivante"]')
                    .should('have.attr', 'href')
                    .and('include', 'page=3')
                cy.get('.article-pagination a[aria-label="Page précédente"]').should('exist')
                cy.get('.article-pagination a[aria-label="Page précédente"]')
                    .should('have.attr', 'href')
                    .and('include', 'page=1')
                cy.get('.article-pagination .pagination-item').should('have.length', 8) // 6 pages (195 / 36) + previous + next
                cy.get('.article-pagination li.pagination-item:visible a').should('have.length', 5) // page 1, 3, 6, previous and next
                cy.get('.article-pagination .pagination-item.active').should('contain', '2')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(0)
                    .should('have.attr', 'href')
                    .and('include', 'page=1')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(1)
                    .should('have.attr', 'href')
                    .and('include', 'page=1')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(2)
                    .should('have.attr', 'href')
                    .and('include', 'page=3')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(3)
                    .should('have.attr', 'href')
                    .and('include', 'page=6')
                cy.get('.article-pagination li.pagination-item:visible a')
                    .eq(4)
                    .should('have.attr', 'href')
                    .and('include', 'page=3')
                cy.get('.article-pagination .ellipsis').should('be.visible')

                // Go to previous page and check canonical URL is updated
                cy.get('.article-pagination a[aria-label="Page précédente"]').click()
                cy.url().should('not.include', 'page=')
                cy.get('head link[rel="canonical"]').should('have.attr', 'href').and('not.include', 'page=')

                // Check pagination links back on page 1 - should have next but no prev again
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=2')
                cy.get('head link[rel="prev"]').should('not.exist')
            })

            it('check pagination links on last page', function () {
                // Visit last page directly (page 6 for 195 results with 36 per page)
                cy.visit(`${PAGE}?page=6`)
                cy.wait('@api_filters')
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('not.contain', 'Chargement')

                // Check canonical URL on last page
                cy.get('head link[rel="canonical"]').should('have.attr', 'href').and('include', 'page=6')

                // Check pagination links on last page - should have prev but no next
                cy.get('head link[rel="prev"]').should('have.attr', 'href').and('include', 'page=5')
                cy.get('head link[rel="next"]').should('not.exist')
            })
        })

        describe('Options must be updatable through the url', function () {
            it('update url according to options changes', function () {
                cy.visit(PAGE)
                cy.wait('@api_filters')
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('not.contain', 'Chargement')

                // call next page
                cy.get('.article-pagination a[aria-label="Page suivante"]').click()
                cy.url().should('include', 'page=2')

                // sort option is handled
                cy.get('select[name=items_sorting_options]').select('Prix décroissant')
                cy.url().should('include', 'tri=%7B%22selling_price%22%3A%22desc%22%7D')

                // display option is handled
                cy.get('[data-context="toggle-grid"]').click()
                cy.url().should('include', 'affichage=GRID')

                // filters options are handled
                cy.get('[data-context="search-filter"]').as('filters')

                cy.get('@filters').eq(0).find('input[name=min_input]').clear().type('5000{enter}')
                cy.url().should('include', 'filtres=')
                cy.url().should('include', 'p_5490')
                cy.get('@filters').eq(1).find('[data-context="checkbox"] label').eq(0).click()
                cy.url().should('include', 'b_83')
            })

            it('load options and articles according to data in the url', function () {
                const urls_options = [
                    'tri=%7B%22selling_price%22%3A%22desc%22%7D',
                    'affichage=GRID',
                    'filtres=%5B%22p_5490%22%2C%22p_6990%22%2C%22p_5990%22%2C%22p_7990%22%2C%22p_11990%22%2C%22p_29990%22%2C%22p_19990%22%2C%22b_83%22%5D',
                    'page=2',
                ]
                cy.visit(`${PAGE}?${urls_options.join('&')}`)
                cy.wait('@api_filters')

                cy.wait('@api_search').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        page: 2,
                        item_per_page: ITEMS_PER_PAGE,
                        sort_by: { selling_price: 'desc' },
                        article_ids: [146511, 146512],
                    })
                })

                // filters properly initialized
                cy.get('[data-context="filters-summary"]').should('contain', '2 résultats')
                cy.get('select[name=items_sorting_options] option').eq(1).should('be.selected')
                cy.get('[data-context="toggle-grid"]').should('have.class', 'btn-primary')
                cy.get('[data-context="search-filter"]').as('filters')
                cy.get('@filters').eq(0).find('input[name=min_input]').should('have.value', 5490)
                cy.get('@filters').eq(0).find('input[name=max_input]').should('have.value', 6990)
                cy.get('@filters')
                    .eq(0)
                    .find('.input-progress')
                    .should('have.attr', 'style')
                    .should('match', /--low: ?58\.5%; --high: ?77\.5%;/i)
                cy.get('@filters')
                    .eq(1)
                    .find('[data-context="checkbox"] input[type=checkbox]')
                    .eq(0)
                    .should('be.checked')
            })

            it('navigation in browser history alter the options', function () {
                cy.visit(PAGE)
                cy.wait('@api_filters')
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('not.contain', 'Chargement')

                // Check initial pagination links on page 1
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=2')
                cy.get('head link[rel="prev"]').should('not.exist')

                /*
                 * Do actions
                 */
                // call page 2
                cy.get('.article-pagination a[aria-label="Page suivante"]').click()
                cy.wait('@api_search')

                // Check pagination links on page 2
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=3')
                cy.get('head link[rel="prev"]').should('have.attr', 'href').and('not.include', 'page=')

                // change sorting
                cy.get('select[name=items_sorting_options]').select('Prix décroissant')
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('contain', '195 résultats')
                cy.url().should('include', 'tri=')

                // change display
                cy.get('[data-context="toggle-grid"]').click()
                cy.url().should('include', 'affichage=')

                // change price
                cy.get('[data-context="search-filter"]').as('filters')
                cy.get('@filters').eq(0).find('input[name=min_input]').clear().type('5000{enter}')
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('contain', '7 résultats')
                cy.url().should('include', 'p_5490')

                // select a brand
                cy.get('@filters').eq(1).find('[data-context="checkbox"] label').eq(0).click()
                cy.wait('@api_search')
                cy.get('[data-context="filters-summary"]').should('contain', '2 résultats')
                cy.url().should('include', 'b_83')

                cy.get('[data-context=filters-summary-reset]')
                    .should('be.visible')
                    .should('contain', 'Effacer tous mes critères')

                /*
                 * Revert actions
                 */
                // revert brand selection through history
                cy.go('back')
                cy.wait('@api_search').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        page: 1,
                        item_per_page: ITEMS_PER_PAGE,
                        sort_by: { selling_price: 'desc' },
                        article_ids: [136906, 146205, 146511, 146512, 148319, 148758, 148759],
                    })
                })
                cy.url().should('not.include', 'b_83')
                cy.get('@filters')
                    .eq(1)
                    .find('[data-context="checkbox"] input[type=checkbox]')
                    .eq(0)
                    .should('not.be.checked')
                cy.get('[data-context="filters-summary"]').should('contain', '7 résultats')

                // Check pagination links after going back (should be on page 1 with filters)
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=2')
                cy.get('head link[rel="prev"]').should('not.exist')

                // revert price selection through history
                cy.go('back')
                cy.wait('@api_search').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        page: 1,
                        item_per_page: ITEMS_PER_PAGE,
                        sort_by: { selling_price: 'desc' },
                        article_ids: ALL_ARTICLE_IDS,
                    })
                })
                cy.url().should('not.include', 'p_5490')
                cy.get('[data-context="filters-summary"]').should('contain', '195 résultats')
                cy.get('@filters').eq(0).find('input[name=min_input]').should('have.value', 449)
                cy.get('@filters').eq(0).find('input[name=max_input]').should('have.value', 8999)

                // Check pagination links after reverting price filter (should be on page 1 with all results)
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=2')
                cy.get('head link[rel="prev"]').should('not.exist')

                // revert display selection through history
                cy.go('back')
                cy.wait('@api_search') // TODO: not cool, this call should not happen
                cy.url().should('not.include', 'affichage=')
                cy.get('[data-context="toggle-line"]').should('have.class', 'btn-primary')

                // revert sort selection through history
                cy.go('back')
                cy.wait('@api_search').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        page: 2,
                        item_per_page: ITEMS_PER_PAGE,
                        sort_by: { has_estimated_delivery_time: 'DESC' },
                        article_ids: ALL_ARTICLE_IDS,
                    })
                })
                cy.url().should('not.include', 'tri=')
                cy.get('select[name=items_sorting_options] option').eq(5).should('be.selected')

                // Check pagination links after reverting sort (should be back on page 2)
                cy.get('head link[rel="next"]').should('have.attr', 'href').and('include', 'page=3')
                cy.get('head link[rel="prev"]').should('have.attr', 'href').and('not.include', 'page=')
            })

            it('load options according to data in the url, handling the old way', function () {
                const urls_options = [
                    'tri=2',
                    'filtres=%7B%22sizes%22%3A%5B%2225%20cm%22%2C%2255%20cm%22%2C%2260%20cm%22%2C%2280%20cm%22%5D%7D',
                ]
                cy.visit(`${PAGE}?${urls_options.join('&')}`)
                cy.wait('@api_filters')

                cy.wait('@api_search').then((xhr) => {
                    expect(xhr.request.body).to.deep.eq({
                        page: 1,
                        item_per_page: ITEMS_PER_PAGE,
                        sort_by: { score: 'desc' },
                        article_ids: ALL_ARTICLE_IDS,
                    })
                })

                // filters properly initialized
                cy.get('select[name=items_sorting_options] option').eq(2).should('be.selected')
            })
        })

        describe('Numeric filters display rules', function () {
            it('should visit the page', function () {
                cy.intercept('GET', `${PAGE}/_filters**`, {
                    fixture: 'api/rayon/{slug}/_filters/multiple_numeric_types.json',
                }).as('api_filters')

                cy.visit(PAGE)
                cy.wait('@api_filters')

                // this call does not really matter in these tests
                cy.wait('@api_search')
            })

            it('uses the "default" display (=checkboxes) if has 1 to 5 values', function () {
                // one possible value
                cy.get('div[data-context=search-filter]:contains(Portée maximale)').click().as('filter')
                cy.get('@filter').should('have.attr', 'data-type', 'default')
                cy.get('@filter').find('[data-context=checkbox]').should('have.length', 1)

                // 5 possible values
                cy.get('div[data-context=search-filter]:contains(Fréquence aigüe max)').click().as('filter')
                cy.get('@filter').should('have.attr', 'data-type', 'default')
                cy.get('@filter').find('[data-context=checkbox]').should('have.length', 5)

                // works with decimal values too
                cy.get('div[data-context=search-filter]:contains(Poids)').click().as('filter')
                cy.get('@filter').should('have.attr', 'data-type', 'default')
                cy.get('@filter').find('[data-context=checkbox]').should('have.length', 5)

                // values are displayed using the suffix and formatted value
                cy.get('@filter').find('[data-context=checkbox]').eq(0).should('contain', '0,03 kg')

                // is also well formatted when displayed in selected values
                cy.get('@filter').find('[data-context=checkbox] label').eq(0).click()
                cy.get('@filter').find('[data-context=checkbox] label').eq(1).click()
                cy.get('[data-context=selected-filters]').should('contain', 'Poids : 0,03 kg')
                cy.get('[data-context=selected-filters]').should('contain', 'Poids : 0,099 kg')
            })

            it('uses a step slider if has 6 to 15 possible values', function () {
                cy.get('div[data-context=search-filter]:contains(Largeur)').click().as('filter')
                cy.get('@filter').should('have.attr', 'data-type', 'slider')

                // this filter has no value between 22 and 51 => goes to the closest value if entered between
                cy.get('@filter').find('input[name=min_input]').clear().type('45{enter}')
                cy.get('@filter').find('input[name=min_input]').should('have.value', '51')
                cy.get('@filter')
                    .find('.input-progress')
                    .should('have.attr', 'style')
                    .should('match', /--low: ?45\.5%; --high: ?100\.5%;/i)
            })

            it('uses a linear slider if has more than 15 possible values or has a decimal value', function () {
                // case with decimal value (14 values)
                cy.get('div[data-context=search-filter]:contains(Hauteur)').click().as('filter')
                cy.get('@filter').should('have.attr', 'data-type', 'slider')
                // has no value between 30 and 258 => all can be used between
                cy.get('@filter').find('input[name=min_input]').clear().type('42{enter}')
                cy.get('@filter').find('input[name=min_input]').should('have.value', '42')
                cy.get('@filter')
                    .find('.input-progress')
                    .should('have.attr', 'style')
                    .should('match', /--low: ?13\.5%; --high: ?100\.5%;/i)

                // case with 16 values
                cy.get('div[data-context=search-filter]:contains(Profondeur)').click().as('filter')
                cy.get('@filter').should('have.attr', 'data-type', 'slider')
                // has no value between 130 and 142 => all can be used between
                cy.get('@filter').find('input[name=min_input]').clear().type('140{enter}')
                cy.get('@filter').find('input[name=min_input]').should('have.value', '140')
                cy.get('@filter')
                    .find('.input-progress')
                    .should('have.attr', 'style')
                    .should('match', /--low: ?75\.5%; --high: ?100\.5%;/i)
            })

            it('does not consider "16/9" or "1,33" as numeric values and display them correctly', function () {
                cy.get('div[data-context=search-filter]:contains(Format Image)').click().as('filter')
                cy.get('@filter').should('contain', '16/9').should('contain', '1,33')
            })

            it('uses logarithmic progression for price filter', function () {
                cy.get('div[data-context=search-filter]:contains(Fourchette de prix)').as('filter')

                // values go from 49 to 3599 => use the full range
                cy.get('@filter').find('input[name=min_input]').should('have.value', '49')
                cy.get('@filter').find('input[name=max_input]').should('have.value', '3599')
                cy.get('@filter')
                    .find('.input-progress')
                    .should('have.attr', 'style')
                    .should('match', /--low: ?-0\.5%; --high: ?100\.5%;/i)

                // min to 79 => ~=10% of progress
                // max to 420 => ~=50% of progress
                cy.get('@filter').find('input[name=min_input]').clear().type('79{enter}')
                cy.get('@filter').find('input[name=max_input]').clear().type('420{enter}')
                cy.get('@filter')
                    .find('.input-progress')
                    .should('have.attr', 'style')
                    .should('match', /--low: ?10\.5%; --high: ?50\.5%;/i)
            })
        })

        describe('Article display', function () {
            it('Check confidential Price', () => {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=line-item]').eq(17).as('confidentialArticle')
                cy.get('@confidentialArticle')
                    .find('.SVDv3_rayon_listingProduits_prix')
                    .find('.SVDv3_reduction')
                    .should('not.exist')
                cy.get('@confidentialArticle').find('.SVDv3_rayon_listingProduits_prix .SVDv3_rayon_prixConfidentiel')
                cy.get('@confidentialArticle')
                    .find('.SVDv3_rayon_listingProduits_action')
                    .find('.SVDv3_bouton_ajoutPanier')
                    .should('not.exist')
            })
            it('Check reference price', () => {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')
                cy.get('[data-context=line-item]:contains(43PUS7505)').within(() => {
                    cy.get('[data-context=rounded-percentage]').should('contain.text', '- 18 %')
                    cy.get('[data-context=reference-price-crossed]')
                        .should('be.visible')
                        .should('contain.text', '550 €')
                    cy.get('[data-context=highlight]').should('not.exist')
                    cy.get('[data-context=reference-price-crossed]').click()
                })
                cy.get('[data-context=side-panel]')
                    .should('be.visible')
                    .should('contain.text', 'Prix de référence')
                    .should('contain.text', `L’indication prix de référence`)
                cy.get('[data-context=side-panel-overlay]').click()
                cy.get('[data-context=toggle-grid]').click()
                cy.get('[data-context=grid-item]:contains(43PUS7505)').within(() => {
                    cy.get('[data-context=rounded-percentage]').should('contain.text', '- 18 %')
                    cy.get('[data-context=reference-price-crossed]')
                        .should('be.visible')
                        .should('contain.text', '550 €')
                    cy.get('[data-context=highlight]').should('not.exist')
                    cy.get('[data-context=reference-price-crossed]').click()
                })
                cy.get('[data-context=side-panel]')
                    .should('be.visible')
                    .should('contain.text', 'Prix de référence')
                    .should('contain.text', `L’indication prix de référence`)
            })
        })

        describe("Verify that all basic information's are displayed properly", function () {
            it('Check that the length is displayed correctly', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=product-length]').htmlContains('Longueur : 108 cm')
            })

            it('Resets all filters', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                // WITH ONE FILTER
                // initial result
                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')
                // select one filter
                cy.get('[data-context=search-filter]').eq(1).find('[data-context=checkbox] label').eq(1).click()
                // new result
                cy.get('[data-context=filters-summary] strong').should('contain', '24 résultats')
                cy.get('[data-context=search-filter]')
                    .eq(1)
                    .find('[data-context=checkbox] input')
                    .eq(1)
                    .should('be.checked')
                // reset filters
                cy.get('[data-context=filters-summary-reset]')
                    .should('be.visible')
                    .should('contain', 'Effacer tous mes critères')
                    .click()
                // filter is reset
                cy.get('[data-context=search-filter]')
                    .eq(1)
                    .find('[data-context=checkbox] input')
                    .eq(1)
                    .should('not.be.checked')
                // and back to initial result
                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')

                // WITH MULTIPLE FILTERS
                // initial result
                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')
                // select one filter
                cy.get('[data-context=search-filter]').eq(1).find('[data-context=checkbox] label').eq(1).click()
                cy.get('[data-context=search-filter]').eq(1).find('[data-context=checkbox] label').eq(2).click()
                // new result
                cy.get('[data-context=filters-summary] strong').should('contain', '51 résultats')
                cy.get('[data-context=search-filter]')
                    .eq(1)
                    .find('[data-context=checkbox] input')
                    .eq(1)
                    .should('be.checked')
                cy.get('[data-context=search-filter]')
                    .eq(1)
                    .find('[data-context=checkbox] input')
                    .eq(2)
                    .should('be.checked')
                // reset filters
                cy.get('[data-context=filters-summary-reset]')
                    .should('be.visible')
                    .should('contain', 'Effacer tous mes critères')
                    .click()
                // filter is reset
                cy.get('[data-context=search-filter]')
                    .eq(1)
                    .find('[data-context=checkbox] input')
                    .eq(1)
                    .should('not.be.checked')
                cy.get('[data-context=search-filter]')
                    .eq(1)
                    .find('[data-context=checkbox] input')
                    .eq(2)
                    .should('not.be.checked')
                // and back to initial result
                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')
            })

            it('Check filter blocks', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=search-filter]').should('have.length', 15)
            })
        })

        describe('Check fixed/always displayed filters', function () {
            it('Check price range', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=search-filter]').eq(0).as('filter')
                cy.get('@filter')
                    .should('contain', 'Fourchette de prix')
                    .should('have.attr', 'data-type', 'slider')
                    .should('have.class', 'is-open')

                cy.get('@filter').find('input[name=min_input]').should('have.value', 449)
                cy.get('@filter').find('input[name=max_input]').should('have.value', 8999)

                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')

                cy.get('@filter').find('input[name=min_input]').clear().type('600{enter}')

                cy.get('[data-context=filters-summary] strong').should('contain', '177 résultats')

                cy.get('@filter').find('input[name=max_input]').clear().type('1000{enter}')

                cy.get('[data-context=filters-summary] strong').should('contain', '45 résultats')

                cy.get('@filter').find('a:contains(Réinitialiser)').click()

                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')
            })

            it('Check brand filter', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=search-filter]').eq(1).as('filter')
                cy.get('@filter')
                    .should('contain', 'Marques')
                    .should('have.attr', 'data-type', 'default')
                    .should('have.class', 'is-open')

                cy.get('@filter').find('[data-context=internal-filter]').should('be.visible')

                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')
                cy.get('@filter').find('[data-context=checkbox]').should('have.length', 7)

                cy.get('@filter').find('[data-context=internal-filter] input').type('a')

                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')

                cy.get('@filter').find('[data-context=checkbox] label').eq(1).click()
                cy.get('[data-context=filters-summary] strong').should('contain', '69 résultats')
                cy.get('@filter').find('[data-context=checkbox]').should('have.length', 2)

                cy.get('@filter').find('a:contains(Réinitialiser)').click()

                cy.get('[data-context=filters-summary] strong').should('contain', '195 résultats')
                cy.get('@filter').find('[data-context=checkbox]').should('have.length', 7)
            })

            it('Check categories filter not displayed (stand level 3 rule)', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=search-filter]').should('not.contain', 'Catégories')
            })
        })

        describe('Check composite filters / refactored eav', function () {
            it('Check closed and open by default filters', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=search-filter]').eq(4).as('filter')
                cy.get('@filter')
                    .should('contain', 'Diagonale (cm)')
                    .should('have.attr', 'data-type', 'slider')
                    .should('have.class', 'is-open')

                cy.get('[data-context=search-filter]').eq(5).as('filter')
                cy.get('@filter')
                    .should('contain', 'Diagonale (pouces)')
                    .should('have.attr', 'data-type', 'slider')
                    .should('not.have.class', 'is-open')
            })

            it('Can open and close filters', function () {
                cy.visit(PAGE)

                cy.wait('@api_filters')
                cy.wait('@api_search')

                cy.get('[data-context=search-filter]').eq(7).as('filter')
                cy.get('@filter').should('contain', 'Services de streaming').should('not.have.class', 'is-open')

                cy.get('@filter').find('.icon-chevron-bottom').click()
                cy.get('@filter')
                    .find('[data-context=filter-collapse-chevron]')
                    .should('have.class', 'icon-chevron-bottom')
                cy.get('@filter')
                    .should('have.class', 'is-open')
                    .find('[data-context=checkbox]')
                    .should('have.length', 10)
                cy.get('@filter')
                    .find('[data-context=filter-collapse-chevron]')
                    .should('have.class', 'icon-chevron-bottom')
                    .click()
                cy.get('@filter').should('not.have.class', 'is-open').find('[data-context=filter-collapse-chevron]')
            })

            it('Selects multiple filters', function () {
                // use same response than server, but with category filter included (= override stand level 3 rule)
                cy.intercept('GET', `${PAGE}/_filters**`, {
                    fixture: 'api/rayon/{slug}/_filters/article_filters__with_category.json',
                }).as('api_filters')
                cy.visit(PAGE)
                cy.wait('@api_filters')

                cy.wait('@api_search')
                cy.get('[data-context=search-filter]').eq(1).as('first_filter')
                cy.get('@first_filter')
                    .find('[data-context=checkbox]')
                    .as('first_filter_checkboxes')
                    .should('have.length', 3)
                cy.get('@first_filter_checkboxes').eq(0).should('not.have.class', 'disabled-filter')
                cy.get('@first_filter_checkboxes').eq(1).should('not.have.class', 'disabled-filter')
                cy.get('@first_filter_checkboxes').eq(2).should('not.have.class', 'disabled-filter')
                cy.get('[data-context=search-filter]').eq(2).as('second_filter')
                cy.get('@second_filter')
                    .find('[data-context=checkbox]')
                    .as('second_filter_checkboxes')
                    .should('have.length', 3)
                cy.get('@second_filter_checkboxes').eq(0).should('not.have.class', 'disabled-filter')
                cy.get('@second_filter_checkboxes').eq(1).should('not.have.class', 'disabled-filter')
                cy.get('@second_filter_checkboxes').eq(2).should('not.have.class', 'disabled-filter')
                cy.get('@first_filter').find('[data-context=checkbox] label').eq(1).click()
                cy.get('@second_filter_checkboxes').should('have.length', 3)
                cy.get('@second_filter_checkboxes').eq(0).should('not.have.class', 'disabled-filter')
                cy.get('@second_filter_checkboxes').eq(1).should('have.class', 'disabled-filter')
                cy.get('@second_filter_checkboxes').eq(2).should('have.class', 'disabled-filter')

                cy.get('@second_filter').find('[data-context=checkbox] label').eq(0).click()
                cy.get('@first_filter_checkboxes').eq(0).should('have.class', 'disabled-filter')
                cy.get('@first_filter_checkboxes').eq(1).should('not.have.class', 'disabled-filter')

                cy.get('@first_filter_checkboxes').eq(2).should('have.class', 'disabled-filter')
                // Selected filters summary
                cy.get('[data-context=tag]').should('have.length', 2)
                cy.get('[data-context=tag]').eq(0).should('contain', 'Elipson')
                cy.get('[data-context=tag]').eq(1).should('contain', 'Enceinte')
                // remove one filter
                cy.get('[data-context=tag]').eq(0).find('[data-context=remove-btn]').click()
                // result
                cy.get('[data-context=tag]').should('have.length', 1)
                cy.get('[data-context=tag]').eq(0).should('contain', 'Enceinte')
                cy.get('@second_filter').find('[data-context=checkbox] input').eq(0).should('be.checked')
                cy.get('@second_filter').find('[data-context=checkbox] input').eq(1).should('not.be.checked')
                cy.get('@second_filter').find('[data-context=checkbox] input').eq(2).should('not.be.checked')
                cy.get('@second_filter_checkboxes').eq(0).should('not.have.class', 'disabled-filter')
                cy.get('@second_filter_checkboxes').eq(1).should('not.have.class', 'disabled-filter')
                cy.get('@second_filter_checkboxes').eq(2).should('not.have.class', 'disabled-filter')
                cy.get('@first_filter').find('[data-context=checkbox] input').eq(0).should('not.be.checked')
                cy.get('@first_filter').find('[data-context=checkbox] input').eq(1).should('not.be.checked')
                cy.get('@first_filter').find('[data-context=checkbox] input').eq(2).should('not.be.checked')
                cy.get('@first_filter_checkboxes').eq(0).should('have.class', 'disabled-filter')
                cy.get('@first_filter_checkboxes').eq(1).should('not.have.class', 'disabled-filter')
                cy.get('@first_filter_checkboxes').eq(2).should('have.class', 'disabled-filter')
            })
        })

        it('Check display option between line and grid', function () {
            cy.visit(PAGE)

            cy.wait('@api_filters')
            cy.wait('@api_search')

            cy.get('[data-context=search-articles]').find('[data-context=grid-item]').should('not.exist')
            cy.get('[data-context=search-articles]')
                .find('[data-context=line-item]')
                .should('be.visible')
                .should('have.length', 30)

            cy.get('[data-context=toggle-line]').should('have.class', 'btn-primary')
            cy.get('[data-context=toggle-grid]').should('have.class', 'btn-default').click()

            cy.get('[data-context=search-articles]').find('[data-context=line-item]').should('not.exist')
            cy.get('[data-context=search-articles]')
                .find('[data-context=grid-item]')
                .should('be.visible')
                .should('have.length', 30)

            cy.get('[data-context=toggle-grid]').should('have.class', 'btn-primary')
            cy.get('[data-context=toggle-line]').should('have.class', 'btn-default').click()

            cy.get('[data-context=search-articles]').find('[data-context=grid-item]').should('not.exist')
            cy.get('[data-context=search-articles]')
                .find('[data-context=line-item]')
                .should('be.visible')
                .should('have.length', 30)
        })

        it('Checks that article can be added to cart', () => {
            cy.visit(PAGE)
            cy.wait('@api_filters')
            cy.wait('@api_search')
            cy.get('[data-context="filters-summary"]').should('not.contain', 'Chargement')

            cy.intercept('POST', '/mon-panier/ajouter').as('api-add-to-cart')

            cy.log('=== check in line mode ===')
            cy.get('[data-context=line-item]:contains(43PUS7505)')
                .find('a:contains(Ajouter au panier)')
                .scrollIntoView()
                .click()
            cy.wait('@api-add-to-cart')

            cy.log('=== close popin ===')
            cy.get('.swal2-container button:contains(Ok)').click()

            cy.log('=== check in grid mode ===')
            cy.get('[data-context=toggle-grid]').click()
            cy.get('[data-context=grid-item]:contains(43PUS7505)')
                .find('a:contains(Ajouter au panier)')
                .scrollIntoView()
                .click()
            cy.wait('@api-add-to-cart')
        })
    })
})
