describe('Basket - Immediate recall', () => {
    beforeEach(() => {
        cy.clock()
        cy.applySqlFixture('basket/all_content_types.sql')
        cy.intercept('POST', '**/mon-panier/supprimer-article').as('delete-product')
        cy.login('jack', '/mon-panier')
    })
    describe('Service is open', () => {
        beforeEach(() => {
            cy.server()
            cy.route('GET', '/api/customer-service/immediate-recall', () => {
                return {
                    status: 'success',
                    data: {
                        hide_buttons: false,
                        content: {
                            is_active: true,
                            is_in_maintenance: false,
                            errors: [],
                            succeeded: false,
                            selected_country: 'FR',
                            countries: [],
                            account_information: {},
                        },
                    },
                }
            })
        })
        it('display idle block after 15s', () => {
            cy.tick(14000)
            cy.get('[data-context=idle-content]').should('not.exist')
            cy.tick(2000)
            cy.get('[data-context=idle-content]').should('exist').should('be.visible')
        })
        it('no push in small basket', () => {
            cy.get('[data-context=article-line] [data-context=delete-btn]').each((e) => {
                cy.wrap(e).click()
                cy.wait('@delete-product')
            })
            cy.get('[data-context=quote-line').contains('Supprimer').click()
            cy.tick(30000)
            cy.get('[data-context=idle-content]').should('not.exist')
        })
        it('reopen after 30 minutes', () => {
            cy.tick(30000)
            cy.get('[data-context=idle-content]').should('exist').should('be.visible')
            cy.get('body').click(0, 0)
            cy.tick(30 * 60 * 1000) //wait 30 minutes
            cy.tick(30000) // wait 30s
            cy.get('[data-context=idle-content]').should('exist').should('be.visible')
        })
    })

    describe('Service is closed', () => {
        beforeEach(() => {
            cy.server()
            cy.route('GET', '/api/customer-service/immediate-recall', () => {
                return {
                    status: 'success',
                    data: {
                        hide_buttons: false,
                        content: {
                            is_active: false,
                            is_in_maintenance: false,
                            errors: [],
                            succeeded: false,
                            selected_country: 'FR',
                            countries: [],
                            account_information: {},
                        },
                    },
                }
            })
        })

        it('idle block is hidden if recall service is closed', () => {
            cy.tick(120000)
            cy.get('[data-context=idle-content]').should('exist').should('be.not.visible')
        })
    })
})
