import { cancelCustomerOrderPayments } from '../checkout_process_v2/helpers'

describe('Customer Order - add payment', () => {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        // reset and seed the database prior to every test
        cy.reloadFixtures()

        cy.applySqlFixture('customer_order/transform_order_to_v2.sql')
    })

    describe('Order already fully paid', () => {
        it('should redirect to order page with a message', () => {
            cy.login('jack', '/mon-compte/ma-commande/1305003/ajouter-paiement')

            cy.url().should('eq', Cypress.config().baseUrl + '/mon-compte/ma-commande/1305003')
            cy.get('[data-context=message-error]').should(
                'contain.text',
                `Votre commande n'a pas besoin de paiement supplémentaire`
            )
        })
    })

    describe('Partially paid', () => {
        const ORDER_PAGE = '/mon-compte/ma-commande/1305005'

        beforeEach(() => {
            cy.intercept('GET', `${ORDER_PAGE}/resume-v2`).as('get_details')
            cy.intercept('GET', `${ORDER_PAGE}/liste-paiements`).as('get_payments_list')
            cy.intercept('GET', `${ORDER_PAGE}/paiements`).as('get_payments')
            cy.intercept('PUT', `${ORDER_PAGE}/ajouter-paiement`).as('put_payment')
        })

        it('should display expected content', () => {
            cy.login('jack', ORDER_PAGE)

            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 22,
                        name: 'Bons d&apos;achats et cartes cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 32,
                        name: 'CB VISA / MASTERCARD / E-CARTE BLEUE',
                        total_cost: 0,
                    },
                ])
            )

            cy.mockRPCCall(
                'payment_client',
                'election',
                JSON.stringify([
                    {
                        payment_method_id: 50,
                        name: 'Paiement 3X sans frais par carte bancaire',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 51,
                        name: 'Paiement 4X sans frais par carte bancaire',
                        total_cost: 0,
                    },
                ])
            )

            // Tunnel abort was called and the customer order is reloaded
            cy.mockRPCCall(
                'erp',
                'customer_order:fetch_for_cms',
                JSON.stringify({ payments: [{ code: 'VIR', amount: 10, customer_order_payment_id: '697153' }] })
            )

            cancelCustomerOrderPayments([{ code: 'VIR', amount: 10, customer_order_payment_id: '697153' }])

            cy.get('[data-context=pay-my-order]').click()
            cy.wait(['@get_details', '@get_payments_list'])

            cy.get('[data-context=checkout-title]')
                .should('contain', 'Commande n°1305005')
                .should('contain', 'En attente de complément de paiement')

            cy.get('[data-context=created-order-message]').should('not.exist')

            // NOTE: the add payment display is almost the same as the one for quotation
            // only main things will be tested here

            cy.get('[data-context=delivery-summary]').should('be.visible')
            cy.get('[data-context=billing-summary]').should('be.visible')

            cy.get('[data-context=summary]')
                .htmlContains('Sous-total 999,00 €')
                .htmlContains('Livraison 1,00 €')
                .htmlContains('Virement bancaire -10,00 €')
                .htmlContains('TOTAL 990,00 €')
                .find('[data-context=article-item]')
                .should('have.length', 3)

            cy.get('[data-context=payment-methods]')
                .should('be.visible')
                .find('[data-context=payment-mean]:contains(Carte bancaire)')
                .should('be.visible')
        })
    })

    describe('Missing payments', () => {
        const ORDER_PAGE = '/mon-compte/ma-commande/1305006'
        const PAY_BUTTON = '[data-context=pay-btn]:visible'

        beforeEach(() => {
            cy.intercept('GET', `${ORDER_PAGE}/resume-v2`).as('get_details')
            cy.intercept('GET', `${ORDER_PAGE}/liste-paiements`).as('get_payments_list')
            cy.intercept('GET', `${ORDER_PAGE}/paiements`).as('get_payments')
            cy.intercept('PUT', `${ORDER_PAGE}/ajouter-paiement`).as('put_payment')
            cy.intercept('GET', `/ma-commande/traitement-paiement`, {
                body: { status: 'success', action: 'confirm' },
                statusCode: 200,
            }).as('payment-processing')
        })

        it('should display expected content', () => {
            cy.login('jack', ORDER_PAGE)
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 22,
                        name: 'Bons d&apos;achats et cartes cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 32,
                        name: 'CB VISA / MASTERCARD / E-CARTE BLEUE',
                        total_cost: 0,
                    },
                ])
            )
            cy.mockRPCCall(
                'payment_client',
                'election',
                JSON.stringify([
                    {
                        payment_method_id: 50,
                        name: 'Paiement 3X sans frais par carte bancaire',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 51,
                        name: 'Paiement 4X sans frais par carte bancaire',
                        total_cost: 0,
                    },
                ])
            )

            cy.get('[data-context=pay-my-order]').click()
            cy.wait(['@get_details', '@get_payments_list'])

            cy.get('[data-context=checkout-title]')
                .should('contain', 'Commande n°1305006')
                .should('contain', 'En attente de paiement')

            cy.get('[data-context=created-order-message]').should('not.exist')

            // NOTE: the add payment display is almost the same as the one for quotation
            // only main things will be tested here

            cy.get('[data-context=delivery-summary]').should('be.visible')
            cy.get('[data-context=billing-summary]').should('be.visible')

            cy.get('[data-context=summary]')
                .htmlContains('Sous-total 999,00 €')
                .htmlContains('Livraison 1,00 €')
                .htmlContains('TOTAL 1 000,00 €')
                .find('[data-context=article-item]')
                .should('have.length', 3)

            cy.get('[data-context=payment-methods]')
                .should('be.visible')
                .find('[data-context=payment-mean]:contains(Carte bancaire)')
                .should('be.visible')
        })

        it('should display an error message when having no eligible payment', () => {
            cy.mockRPCCall('bridge', 'payment:get_payments_for_order', JSON.stringify([]))
            cy.mockRPCCall('payment_client', 'election', JSON.stringify([]))
            cy.mockPaymentV2Call('/api/payment-methods/election/1305005', JSON.stringify([]))

            cy.login('jack', `${ORDER_PAGE}/ajouter-paiement`)
            cy.wait(['@get_details', '@get_payments_list'])

            cy.get('[data-context=payment-methods]')
                .should('contain.text', 'Aucun moyen de paiement éligible')
                .find('[data-context=payment-mean]')
                .should('not.exist')

            cy.get(PAY_BUTTON).should('not.exist')
        })

        it('should let adding payment', () => {
            cy.login('jack', ORDER_PAGE)
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 22,
                        name: 'Bons d&apos;achats et cartes cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 32,
                        name: 'CB VISA / MASTERCARD / E-CARTE BLEUE',
                        total_cost: 0,
                    },
                ])
            )
            cy.mockRPCCall(
                'erp',
                'customer_order_payment:create',
                JSON.stringify({
                    order: {
                        payments: [
                            {
                                code: 'CBS-O',
                                payment_method_id: 32,
                                amount: 990.0,
                            },
                        ],
                    },
                })
            )
            cy.mockRPCCall(
                'erp',
                'customer_order:fetch_for_cms',
                JSON.stringify({
                    payments: [
                        {
                            code: 'CBS-O',
                            payment_method_id: 32,
                            amount: 990.0,
                        },
                    ],
                })
            )
            cy.mockRPCCall('erp', 'customer_order_payment:handle_bank_redirection', JSON.stringify({ action: false }))
            cy.mockRPCCall('erp', 'customer_order:activate', JSON.stringify({ status_updated: true }))

            cy.get('[data-context=pay-my-order]').click()
            cy.wait(['@get_details', '@get_payments_list'])

            cy.get(PAY_BUTTON).click()
            cy.wait('@put_payment').then((xhr) => {
                expect(xhr.request.body.payment_method_id).to.eq(32)
            })

            cy.wait('@payment-processing')
            cy.wait('@get_details')
            cy.wait('@get_payments_list')

            cy.url().should('eq', Cypress.config().baseUrl + '/mon-compte/ma-commande/1305006/ajouter-paiement')

            cy.get('[data-context=customer-order]').should('not.exist')
            cy.get('[data-context=pay-my-order]').should('not.exist')

            cy.get('[data-context=checkout-title]').htmlContains('Commande n°1305006 En attente de paiement')
            cy.get('[data-context=created-order-message]').should('not.exist')
        })

        it('You should not leave', () => {
            cy.login('jack', ORDER_PAGE)

            cy.get('[data-context=pay-my-order]').click()
            cy.wait(['@get_details', '@get_payments_list'])

            cy.get('img.icon').should('be.visible').click()
            cy.get('div.tooltip')
                .should('contain', 'Êtes-vous sûr(e) de vouloir retourner à vos commandes ?')
                .should('be.visible')
            cy.get('[data-context=stay-in-funnel]').click()
            cy.get('div.tooltip').should('not.exist')
            cy.get('img.icon').should('be.visible').click()
            cy.get('[data-context=confirm]').should('contain.text', 'Voir mes commandes').click()
            cy.url().should('eq', Cypress.config().baseUrl + '/mon-compte/mes-commandes')
        })

        it('Display message in repayment when a gift card is used', () => {
            cy.login('jack', ORDER_PAGE)
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 22,
                        name: 'Bons d&apos;achats et cartes cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 32,
                        name: 'CB VISA / MASTERCARD / E-CARTE BLEUE',
                        total_cost: 0,
                    },
                ])
            )
            cy.mockRPCCall(
                'erp',
                'customer_order_payment:create',
                JSON.stringify({
                    order: {
                        payments: [
                            {
                                code: 'SVDCC',
                                payment_method_id: 22,
                                amount: 20,
                            },
                        ],
                    },
                })
            )
            cy.mockRPCCall(
                'erp',
                'customer_order:fetch_for_cms',
                JSON.stringify({
                    payments: [
                        {
                            code: 'SVDCC',
                            payment_method_id: 22,
                            amount: 20,
                        },
                    ],
                })
            )

            cy.get('[data-context=pay-my-order]').click()
            cy.wait(['@get_details', '@get_payments_list'])

            cy.get('[data-context=checkout-title]')
                .should('contain', 'Commande n°1305006')
                .should('contain', 'En attente de paiement')

            cy.get('[data-context=created-order-message]').should('not.exist')

            cy.get('[data-context=delivery-summary]').should('be.visible')
            cy.get('[data-context=billing-summary]').should('be.visible')

            cy.get('[data-context=method-group-title]').eq(1).click()
            cy.get('[data-context=payment-method-content]').as('payment-method-content').find('input').as('svd-cc')

            cy.mockRPCCall(
                'bridge',
                'svd_gift_card:check',
                JSON.stringify({ svd_gift_card: { is_valid: true, amount: 20 } })
            )

            cy.mockRPCCall('erp', 'customer_order:create', JSON.stringify({ customer_order_id: 24 }))

            cy.mockRPCCall('erp', 'customer_order_payment:handle_bank_redirection', JSON.stringify({ action: false }))
            cy.get('@svd-cc').clear()
            cy.get('@svd-cc').type('12573 0 ********* 70 94  0')
            cy.get('@payment-method-content').find('button').click()

            cy.wait('@payment-processing')
            cy.wait('@get_details')
            cy.wait('@get_payments_list')

            cy.get('[data-context=created-order-message]')
                .should('be.visible')
                .htmlContains('reste à payer de 980,00 €')
        })
    })
})
