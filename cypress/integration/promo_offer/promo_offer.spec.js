describe('Check promo page display', function () {
    before(function () {
        cy.setupDatabase()
        cy.reloadFixtures()
    })

    it('Check promo page without banner image', function () {
        cy.visit('/promos/sony-rembourse-jusqu-a-200-euros')

        cy.get('div.editorial').as('editorial').should('be.visible')
        cy.get('@editorial').find('.editorial-head').should('be.not.exist')
        cy.get('@editorial').find('.editorial-body').should('be.visible')
    })

    it('Check promo page with banner image', function () {
        cy.visit('/promos/cabasse-rembourse-80-euros')

        cy.get('div.editorial').as('editorial').should('be.visible')
        cy.get('@editorial').find('.editorial-head img').should('be.visible')
        cy.get('@editorial').find('.editorial-body').should('be.visible')
    })
})
