// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

import { IDS } from './helpers'

Cypress.Commands.add('setupDatabase', () => {
    cy.log('Setup DB')
    cy.exec(Cypress.env().SETUP_DATABASE).its('stderr').should('eq', '')
})
Cypress.Commands.add('reloadFixtures', () => {
    cy.log('Reload Fixtures')
    cy.exec(Cypress.env().RELOAD_FIXTURES).its('stderr').should('eq', '')
})

/**
 * define cy.login(id)
 */
Cypress.Commands.add('login', (id, redirection_url = undefined) => {
    // Log and check id is known
    cy.log(`Login as "${id}"`)
    if (!IDS.hasOwnProperty(id)) {
        throw new Error(`id "${id}" is unknown`)
    }

    // Remove cookie banner
    cy.preAcceptSiteCookies()

    // login specified user (will logout user from previous test if necessary)
    // If the page parameter is defined, redirect to it
    cy.visit(
        redirection_url
            ? `/_test/login?user=${IDS[id].username}&redirect=${redirection_url}`
            : `/_test/login?user=${IDS[id].username}`
    )
})

/**
 * Remove the cookie banner
 * facilitate debugging by not having the huge cookie banner covering part of the content
 * This one should be called before visit() and does not require to interact with the DOM and thus is faster
 */
Cypress.Commands.add('preAcceptSiteCookies', () => {
    cy.setCookie(
        'euconsent',
        '%7B%22g_review%22%3Afalse%2C%22hotjar%22%3Afalse%2C%22bloomreach%22%3Afalse%2C%22bing_ads%22%3Afalse%2C%22fb_ads%22%3Afalse%2C%22google_ads%22%3Afalse%2C%22google_rmk%22%3Afalse%2C%22google_display%22%3Afalse%2C%22kimple%22%3Afalse%2C%22twenga%22%3Afalse%7D'
    )
})

/**
 * define cy.logout()
 */
Cypress.Commands.add('logout', () => {
    cy.visit('/mon-compte/deconnexion')
})

/**
 * define cy.mockRPCCall(name, uri, result)
 *
 * Mock an RPC API Call & response (via a symfony command internally)
 *
 * @param {string|undefined} name
 * @param {string|undefined} uri
 * @param {string|undefined} result - expected result from the RPC call
 *
 * @example cy.mockRPCCall('bridge', 'carrier:get_carriers_for_order', '[{"shipment_method_id": 1, "cost": 9.9},{"shipment_method_id": 31, "cost": 0}]')
 */
Cypress.Commands.add('mockRPCCall', (name, uri, result) => {
    cy.log('Mock RPC Call', name, uri, result)
    cy.exec(`${Cypress.env().MOCK_RPC_CALL} --name=${name} --uri=${uri} --result='${result}'`)
})

Cypress.Commands.add('mockPaymentV2Call', (uri, result) => {
    cy.log('Mock Payment v2 Call', name, uri, result)
    cy.exec(`${Cypress.env().MOCK_PAYMENT_V2_CALL} --uri=${uri} --result='${result}'`)
})

/**
 * define cy.mockElasticSearch(result)
 *
 * Mock the next ElasticSearch result (via a symfony command internally)
 *
 * @param {string|undefined} result - expected result from the RPC call
 *
 * @example cy.mockElasticSearch('[{"shipment_method_id": 1, "cost": 9.9},{"shipment_method_id": 31, "cost": 0}]')
 */
Cypress.Commands.add('mockElasticSearch', (result) => {
    cy.exec(`${Cypress.env().MOCK_ELASTIC_SEARCH} --result='${result}'`).its('code').should('eq', 0)
})

/**
 * define cy.mockProductEngine(result)
 *
 * Mock the next Product Engine result (via a symfony command internally)
 *
 * @param {string|undefined} result - expected result from a search. Should define 'documents' and 'aggregations'
 */
Cypress.Commands.add('mockProductEngine', (result) => {
    cy.exec(`${Cypress.env().MOCK_PRODUCT_ENGINE} --result='${result}'`).its('code').should('eq', 0)
})

/**
 * Run specified fixture file in the database
 *
 * @param {string} The path to the file to load in db
 * @param {text} Whether using the "cypress" specific fixtures or the one "shared" across all tests (unit php, behat...)
 *                       in "src/AppBundle/Tests/fixtures/sql_specific_fixtures"
 *                       Leave the prefix empty to use the shared file path
 */
Cypress.Commands.add('applySqlFixture', (fixture_file, prefix = 'cypress:') => {
    const command = `${Cypress.env().APPLY_FIXTURE} ${prefix}${fixture_file}`
    cy.log(`=== apply sql fixture: ${command}`)
    cy.exec(command).its('stderr').should('eq', '')
})

/**
 * define cy.wrap(element).htmlContains(text)
 *
 * Check content of previous chained element against given string
 * Uses more formatting than should('contain'):
 * - Get all text, including text from sub element
 * - Format/remove html entities
 * - Remove return carriages if any
 *
 * @param {string|undefined} text
 *
 * @example cy.get(…).htmlContains('My texte')
 */
Cypress.Commands.add('htmlContains', { prevSubject: 'element' }, (subject, expected_text) => {
    cy.wrap(subject).should(($element) => {
        // innerText: access the native DOM element including child
        // replace: convert html entity &nbsp; and carriage returns to space
        expect($element.get(0).innerText.replace(/\u00a0|\u202F|\n|\r/g, ' ')).to.have.string(expected_text)
    })

    return cy.wrap(subject)
})

/**
 * define cy.wrap(element).htmlNotContains(text)
 *
 * Check content of previous chained element against given string
 * Uses more formatting than should('not.contain'):
 * - Get all text, including text from sub element
 * - Format/remove html entities
 * - Remove return carriages if any
 *
 * @param {string|undefined} text
 *
 * @example cy.get(…).htmlNotContains('My texte')
 */
Cypress.Commands.add('htmlNotContains', { prevSubject: 'element' }, (subject, expected_text) => {
    cy.wrap(subject).should(($element) => {
        // innerText: access the native DOM element including child
        // replace: convert html entity &nbsp; and carriage returns to space
        expect($element.get(0).innerText.replace(/\u00a0|\u202F|\n|\r/g, ' ')).to.not.have.string(expected_text)
    })

    return cy.wrap(subject)
})

/**
 * strange error that happen in between the screens (the waiting screen and the next screen)
 * error message depends on the browser and change when running headless as well
 * https://docs.cypress.io/api/events/catalog-of-events.html#To-catch-a-single-uncaught-exception
 */
Cypress.Commands.add('catchInBetweenScreensError', () => {
    cy.on('uncaught:exception', (err) => {
        // Do not catch all uncaught exception
        // Do not return done() as all test after would be passing regardless of the assertions
        if (err.message.includes(`Cannot read property`)) {
            return false
        }
        if (err.message.includes(`Cannot read properties`)) {
            return false
        }
    })
})

/**
 * define in.viewport chai assertion
 *
 * Checks that the chained element bounding box is completely within the viewport
 *
 * @example cy.get(…).should('be.in.viewport')
 */
const isInViewport = (_chai, utils) => {
    function assertIsInViewport(options) {
        const subject = this._obj
        const rect = subject[0].getBoundingClientRect()
        const win = Cypress.$(cy.state('window'))

        this.assert(
            rect.top >= 0 && rect.left >= 0 && rect.bottom <= win.innerHeight() && rect.right <= win.innerWidth(),
            'expected #{this} to be in viewport',
            'expected #{this} to not be in viewport',
            this._obj
        )
    }

    _chai.Assertion.addMethod('inViewport', assertIsInViewport)
}

chai.use(isInViewport)
