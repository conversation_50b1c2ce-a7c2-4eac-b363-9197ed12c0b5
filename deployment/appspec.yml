version: 0.0
os: linux
permissions:
    - {object: /var/www/fo-cms/deploy, type: [directory], pattern: "**", mode: "755", owner: "svd_admin", group: "nginx" }
    - {object: /var/www/fo-cms/deploy/src, type: [file], pattern: "**", mode: "640", owner: "svd_admin", group: "nginx" }
    - {object: /var/www/fo-cms/deploy/vendor, type: [file], pattern: "**", mode: "640", owner: "svd_admin", group: "nginx" }
    - {object: /var/www/fo-cms/shared/app, type: [file], pattern: "**", mode: "640", owner: "svd_admin", group: "nginx" }
    - {object: /var/www/fo-cms/deploy/bin, type: [file], pattern: "console", mode: "750", owner: "svd_admin", group: "nginx" }
    - {object: /var/www/fo-cms/deploy/bin/scripts, type: [file], pattern: "**", mode: "750", owner: "svd_admin", group: "nginx" }
    - {object: /var/www/fo-cms/deploy/bin/services, type: [file], pattern: "**", mode: "740", owner: "root", group: "svd_admin" }
files:
    - {source: fo-cms, destination: /var/www/fo-cms/deploy}
hooks:
    ApplicationStop:
        - {location: "deploy-scripts/stop-server", timeout: 60, runas: root }
        - {location: "deploy-scripts/stop-services", timeout: 60, runas: root }
    BeforeInstall:
        - {location: "deploy-scripts/before-install", timeout: 60, runas: root }
    AfterInstall:
        - {location: "deploy-scripts/after-install", timeout: 180, runas: root }
    ApplicationStart:
        - {location: "deploy-scripts/start-services", timeout: 240, runas: root }
        - {location: "deploy-scripts/start-server", timeout: 60, runas: root }

