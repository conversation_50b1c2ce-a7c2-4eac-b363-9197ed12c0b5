# CMS Front Office

[![forthebadge](https://forthebadge.com/images/badges/built-with-love.svg)](https://forthebadge.com)
[![forthebadge](https://forthebadge.com/images/badges/60-percent-of-the-time-works-every-time.svg)](https://forthebadge.com)
[![forthebadge](https://forthebadge.com/images/badges/contains-technical-debt.svg)](https://forthebadge.com)
[![forthebadge](https://forthebadge.com/images/badges/works-on-my-machine.svg)](https://forthebadge.com)

---

## Get started with `pchstack`

List existing commands for this project :
```shell
phcstack fo-cms
```

## Setup

To start only fo-cms : `phcstack fo-cms install-light`

To start fo-cms with site search (elk) and funnel (payment) working : `phcstack fo-cms install`


---

## NPM (webpack standalone)

> You'll have to open the webpack url to accept the self-signed SSL certificate (https://localhost:8888)

```bash
# remove existing files if necessary
cd /path/to/project
rm -rf node_modules
rm -rf web/dist/*

# select the correct npm version
nvm use

# Install dependencies
npm ci

# run webpack with HMR
npm run serve
```

## NPM (remote Lionel station)

La clé cdn.assets du parameters.yml doit avoir la valeur `'https://fo-cms.lionel:8888'`.

Le fichier /etc/hosts de la machine cliente accédant à l'environnement distant doit mapper `fo-cms.lionel` sur l'ip de l'environnement distant.

Le fichier /etc/hosts de l'environnement distant doit mapper `fo-cms.lionel` sur 127.0.0.1

```bash
# select the correct npm version
nvm use

# Install dependencies
npm ci

# run webpack with HMR
npm run serve-lionel
```

---

### JS Tracker activation (optional)

Add an entry "dev.son-video.com" in your hosts file with fo-cms ip to allow some js tracker to work.

Change AppBundle/Resources/views/Layout/v3/base.html.twig:168 value to allow js tracker loading.

## Lazy load content

We use [Lozad.js library](https://github.com/ApoorvSaxena/lozad.js) on the site to lazy load images and iframes.
Internally it uses the IntersectionObserver API to check if a component must be loaded or not, which have great performances.
For older browser support, we also provide a polyfill. (Without it, Lozad simply load all content immediately.)

To be lazy loaded, a content needs to have a `lozad` css class and `data-src` attribute. Optionally, it can have another `src`
which will be replaced.

The content must exist when the script is loaded, otherwise you need to call `window.SonVideo.lozadObserver.observe()`
to handle new content. All Twig content should be ok with that (initialisation is done at the bottom of the page).
For Vue component, there is a mixin (see below).

To ease the dev process, there is some reusable code:
- a mixin `lazyLoadImagesMixin` for Vue component in `shared/mixins`. Simply load it in a component to automatically
  recall the lozad observer on mounted and updated lifecycle events.
- a Twig function called `empty_data_image(X, Y)` to be used as value for a `src`. It generates the string for
  an empty svg as data:image with dimensions X/Y. Useful to keep an empty space while the data-src is not loaded.
- a js function `emptyDataImage(X, Y)` (in `shared/article/functions.js`) which do the same for Vue.
- a Twig filter called `lazy_load_content`, to be used with editorial_content to replace src and add the required class.
  (Note: an empty image is used as src.)

### Google Analytics

Wiki : https://gitlab.com/groups/son-video/-/wikis/%5BGA%**********************avec-Google-Analytics

## Run e2e tests locally with the CI configuration

Open a shell in the docker container
```shell
phcstack fo-cms docker-registry-exec
nvm install $(cat .nvmrc)
nvm use
npm ci
bin/console server:start --router=web/app_test.php *:8000
npm run e2e:ci
```

to launch a specific test use :   
`npx cypress run -b chrome --config-file cypress-ci.json -s path/to/file.spec.js`

## Déploiement depuis le poste de dev

### Avant de livrer

Le package sera généré à partir des sources en **local**
- Penser à merger sa branche sur master et à basculer dessus
- Mettre à jour ses containers docker

### Création du package

```bash
phcstack fo-cms exec # On entre dans le container
cd deployment
make prepare_before_packing
make -B composer
chmod -R +6 fo-cms
exit
```

### Livraison

#### Validation

```bash
cd ~/projets/fo-cms/deployment
nvm use
make npm
make deploy-validation
sudo make clean
```

#### Staging

```bash
cd ~/projets/fo-cms/deployment
nvm use
make npm
make deploy
sudo make clean
```

#### Production

Pour déployer en production il faut préalablement déployer sur staging puis lancer :
```bash 
phcstack fo-cms deploy-prod
```

---------------------------------------------------------------------------

## Liens utiles

- [Requêtes SQL helper pour les feature flags](https://gitlab.com/son-video/cms-schema/-/blob/master/docs/feature_flags.md)
